#!/usr/bin/env bash

TEST_PATH=/data/local/tmp/face

adb shell "mkdir -p $TEST_PATH"

if [ $# -eq 0 ]
then
    echo "pushNrun.sh [-s][-a][-as]" 
    exit
fi

# -s    Push SecureOS Target
if [ $1 ] && [ $1 = "-s" ]
then
    echo "push secureos arm64-v8a ..."
    adb push build-secureos-arm64-v8a/src/libbstFaceUnlock.so $TEST_PATH
    adb push build-secureos-arm64-v8a/bstFaceUnlockTest $TEST_PATH
fi

# -a    Push Android Target
if [ $1 ] && [ $1 = "-a" ]
then
    echo "push android arm64-v8a ..."
    adb push build-android-arm64-v8a/src/libbstFaceUnlock.so $TEST_PATH
    adb push build-android-arm64-v8a/bstFaceUnlockTest $TEST_PATH
fi

# -as    Push Android Sim-SecureOS Target
if [ $1 ] && [ $1 = "-as" ]
then
    echo "push android-sim-secureos arm64-v8a ..."
    adb push build-android-sim-secureos-arm64-v8a/src/libbstFaceUnlock.so $TEST_PATH
    adb push build-android-sim-secureos-arm64-v8a/bstFaceUnlockTest $TEST_PATH
fi
