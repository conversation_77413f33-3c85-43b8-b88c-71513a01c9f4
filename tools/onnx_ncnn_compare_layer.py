import ncnn
print(ncnn.__version__)
import numpy as np
import onnxruntime as rt
import cv2
import onnx
# 加载模型
'''

onnx_model = onnx.load('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/best.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/face_detection_yolo_v0.6.param')
net.load_model('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/face_detection_yolo_v0.6.bin')
'''


'''
# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/BSTFace/release/final_release_doc/V0.4.7/exp261/pth/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/BSTFace/release/final_release_doc/V0.4.7/exp261/pth/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/BSTFace/release/final_release_doc/V0.4.7/exp261/pth/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.bin')
'''


# sess = rt.InferenceSession('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/checkpoint_best.onnx')


# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/checkpoint_best.onnx')
# onnx_outputs = [x.name for x in ort_session.get_outputs()]


net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.bin')



'''

# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/blink/4.0_64x64_MobileNetHalf_Eyes_20221018.1014.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/blink/4.0_64x64_MobileNetHalf_Eyes_20221018.1014.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/blink/4.0_64x64_MobileNetHalf_Eyes_20221018.1014.bin')
'''
'''

# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/recognizer/backbone_epoch_24_loss_7.0121.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/recognizer/backbone_epoch_24_loss_7.0121.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/recognizer/backbone_epoch_24_loss_7.0121.bin')
'''
# for node in onnx_model.graph.node:
#     for output in node.output:
#         onnx_model.graph.output.extend([onnx.ValueInfoProto(name=output)])
sess = rt.InferenceSession(onnx_model.SerializeToString())
# net.opt.use_fp16_packed = False
# net.opt.use_fp16_storage = False
# net.opt.use_fp16_arithmetic = False
# net.opt.use_int8_packed = False
# net.opt.use_int8_storage = False
# net.opt.use_int8_arithmetic = False
# 获取输入输出的名字
input_name = sess.get_inputs()[0].name
for idx,out in enumerate(sess.get_inputs()):
    print("input idx:",idx,out.name)
input_shape = sess.get_inputs()[0].shape
output_names = []
for idx,out in enumerate(sess.get_outputs()):
    print("output idx:",idx,out.name)
    output_names.append(out.name)


# 读取图像
image = cv2.imread('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/cmake-build-debug/test/bst_net_in.png')
image = cv2.resize(image, (input_shape[2], input_shape[3]), interpolation=cv2.INTER_LINEAR)  # (w, h)

if input_shape[1] ==3:
    mean_vals = [127.5, 127.5, 127.5]
    norm_vals = [1/127.5, 1/127.5, 1/127.5]
    img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    # cv2.imshow("img_in", img)
    img = img.astype(np.float32)# / 255.0

    img = (img - mean_vals) * norm_vals

    img = np.transpose(img, axes=(2, 0, 1))
else:
    mean_vals = [127.5]
    norm_vals = [1/127.5]
    img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # cv2.imshow("img_in", img)
    img = img.astype(np.float32)# / 255.0

    img = (img - mean_vals) * norm_vals

    img = np.expand_dims(img, axis=0)

# 将图像转换为ONNX Runtime需要的格式
input_data = np.expand_dims(img, axis=0)
input_data = input_data.astype(np.float32)  # 转换为浮点数

# 进行推理
onnx_outputs = sess.run(output_names, {input_name: input_data})



img_h = img.shape[0]
img_w = img.shape[1]
if input_shape[1] ==3:
    mat_in = ncnn.Mat.from_pixels(
        image,
        ncnn.Mat.PixelType.PIXEL_BGR2RGB,
        image.shape[1],
        image.shape[0]
    )
else:
    mat_in = ncnn.Mat.from_pixels(
        image,
        ncnn.Mat.PixelType.PIXEL_GRAY,
        image.shape[1],
        image.shape[0]
    )
mat_in.substract_mean_normalize(mean_vals, norm_vals)

# mat_in_numpy = mat_in.reshape(mat_in.w, mat_in.h, mat_in.c)

mat_in_numpy = np.array(mat_in)
diff = np.linalg.norm(mat_in_numpy - img) / np.linalg.norm(mat_in_numpy + img)
print(diff)
# mat_in.substract_mean_normalize([], norm_vals)
# mat_in.substract_mean_normalize(mean_vals, [])

ex = net.create_extractor()
# ex.set_num_threads(1)
ex.set_light_mode(True)
ex.input(0, mat_in)

mat_outs = []
eps = 0.00000001
for idx,output_name in  enumerate(output_names):
    print()
    print("output_name:",output_name)
    # if "/backbone/Mul_output_0" not in output_name:
    #     continue

    ret, mat_out = ex.extract(output_name)
    ncnn_out = np.array(mat_out)
    mat_outs.append(ncnn_out)
    print("onnx_outputs: ",onnx_outputs[idx].shape,onnx_outputs[idx])
    print("ncnn_out",ncnn_out.shape ,ncnn_out)
    # 比较两个模型的输出结果是否一致，返回一致性比率（相似度）
    diff = np.linalg.norm(onnx_outputs[idx] - ncnn_out) / np.linalg.norm(onnx_outputs[idx] + ncnn_out + eps)
    print("Difference: ", diff)  # 如果差异非常小（接近0），则说明两个模型的输出结果一致性很高（相似度很高）

# mat_out = mat_out.reshape(mat_out.w * mat_out.h * mat_out.c)


