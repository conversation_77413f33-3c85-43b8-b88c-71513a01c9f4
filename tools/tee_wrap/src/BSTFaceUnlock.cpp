#include "BSTFaceUnlock.h"
#include <stdlib.h>

#include <android/log.h>

#define LOG_TAG "BlackSesameFUTEE"

PUBLIC_API
const BSTFaceUnlockVersion* bstFaceUnlockGetVersion() {
    // BSTFaceUnlockVersion* v = new BSTFaceUnlockVersion();
    // return v;
    return bstFaceUnlockGetVersionInner();
};

PUBLIC_API
int bstFaceUnlockInit(
    const BSTFaceUnlockConfig* const config) {
    // return -1;
    return bstFaceUnlockInitInner(config);
}

PUBLIC_API
int bstFaceUnlockUninit() {
    // return -1;
    return bstFaceUnlockUninitInner();
}

PUBLIC_API
int bstFaceUnlockEnroll(
    const BSTFaceUnlockImage* const p_img_in,
    BSTFaceFeature* const           p_feature) {
    // return -1;
    int ret = bstFaceUnlockEnrollInner(p_img_in, p_feature);
    __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "auth ret : %d", ret);
    return ret;
}

PUBLIC_API
int bstFaceUnlockEnrollReset() {
    // return -1;
    return bstFaceUnlockEnrollResetInner();
}

PUBLIC_API
int bstFaceUnlockAuthenticate(
    const BSTFaceUnlockImage* const p_img_in,
    BSTFaceFeature* const           p_feature) {

    int ret = bstFaceUnlockAuthenticateInner(p_img_in, p_feature);
    __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "auth ret : %d", ret);
    return ret;
}

PUBLIC_API
int bstFaceUnlockAuthenticateReset() {
    // return -1;
    return bstFaceUnlockAuthenticateResetInner();
}

