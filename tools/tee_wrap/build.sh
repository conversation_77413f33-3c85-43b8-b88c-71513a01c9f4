#!/usr/bin/env bash

NDK=/home/<USER>/tools/android-ndk-r25c

rm -rf ./build-android

cp ../../lib/secureos-arm64-v8a/lib* ./lib/
cp ../../build-secureos-arm64-v8a/src/libBSTFaceAuthTEE.a ./lib/

##### android arm64-v8a
mkdir -p build
pushd build
cmake -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DCMAKE_BUILD_TYPE="Debug" -DANDROID_ABI="arm64-v8a" -DCMAKE_EXPORT_COMPILE_COMMANDS=1 ..
make -j
# make install
popd
