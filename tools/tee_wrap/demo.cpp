#ifndef MAIN_MOCK
#include "BSTFaceUnlock.h"
// #define STB_IMAGE_WRITE_IMPLEMENTATION
#include "CommonUtils.h"
#include "stb/stb_image_write.h"
#include <stdio.h>
#include <string>
#include <iostream>
#endif

#include <unistd.h>

#ifdef MAIN_MOCK
#include "stdio.h"
#include "template_ta.h"
#include "BSTFaceUnlock.h"
int main() {
    int height = 480;
    int width  = 640;

    const BSTFaceUnlockVersion* version = bstFaceUnlockGetVersion();

    int ret = 0;

    BSTFaceUnlockConfig config;
    config.config_type     = BST_FU_CONFIG_TYPE_BUILT_IN;
    config.cfg.config_path = NULL;
    ret                    = bstFaceUnlockInit(&config);
    if (ret != BST_FU_SUCCESS) {
        return -1;
    }

    unsigned char* frame = new unsigned char[int(width * height * 1.5)];

    BSTFaceUnlockImage input_img;
    BSTFaceFeature     feature;
    input_img.img_data.data     = (void*)frame;
    input_img.img_data.format   = BST_IMAGE_TYPE_NV21;
    input_img.img_data.width    = width;
    input_img.img_data.height   = height;
    input_img.img_data.rotation = 270;
    input_img.img_data.stride   = width;

    printf("Main          - I - enroll\n");
    ret = bstFaceUnlockEnroll(&input_img, &feature);
    printf("Main          - I - auth\n");
    ret = bstFaceUnlockAuthenticate(&input_img, &feature);

    ret = bstFaceUnlockUninit();

    delete[] frame;

    return 0;
}
#else
bool read_yuv(char const* imgpath, int width, int height, unsigned char* const enroll_img) {
    FILE* fp = fopen(imgpath, "rb+");
    if (NULL == fp) {
        return false;
    }
    fread(enroll_img, 1, width * height * 1.5, fp);
    fclose(fp);
    return true;
}

int main() {

    TIMER_START(TEST)
    usleep(1000);
    TIMER_STOP(TEST)

    printf("Main          - I - test begin \n");
    int height = 480;
    int width  = 640;

    const BSTFaceUnlockVersion* version = bstFaceUnlockGetVersion();
    printf("Main          - I - version: %s build: %s\n", version->lib_version, version->build_date);

    int ret = 0;

    BSTFaceUnlockConfig config;
    config.config_type     = BST_FU_CONFIG_TYPE_BUILT_IN;
    config.cfg.config_path = NULL;
    ret                    = bstFaceUnlockInit(&config);
    if (ret != BST_FU_SUCCESS) {
        printf("UnlockInit wrong\n");
        return -1;
    }

    unsigned char* frame = new unsigned char[int(width * height * 1.5)];
    read_yuv("../data/test_c009.nv21", width, height, frame);

    
    BSTFaceUnlockImage input_img;
    BSTFaceFeature     feature;
    input_img.img_data.data     = (void*)frame;
    input_img.img_data.format   = BST_IMAGE_TYPE_NV21;
    input_img.img_data.width    = width;
    input_img.img_data.height   = height;
    input_img.img_data.rotation = 270;
    input_img.img_data.stride   = width;
    input_img.meta_data.iso = 40;

    printf("Main          - I - enroll\n");
    ret = bstFaceUnlockEnroll(&input_img, &feature);
    printf("Main          - I - auth\n");
    ret = bstFaceUnlockAuthenticate(&input_img, &feature);

    // pause
    // int a_value;
    // std::cin >> a_value;

    ret = bstFaceUnlockUninit();
    ret = bstFaceUnlockUninit();

    delete[] frame;

    printf("Main          - I - test pass\n");
    return 0;
}
#endif