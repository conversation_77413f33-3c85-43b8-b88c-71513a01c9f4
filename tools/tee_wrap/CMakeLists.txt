cmake_minimum_required(VERSION 3.16)
project(minimal C CXX)

set(TARGET_PLATFORM "android-arm64-v8a")

# header/lib path
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib)

set(DEPS BSTFaceAuthTEE BSTInferTEE openlibm)
file(GLOB_RECURSE SRCS src/BSTFaceUnlock.cpp)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}  -g -O2 -Ofast \
		-fvisibility-inlines-hidden -fPIC -std=c++11 \
		-fno-use-cxa-atexit -fno-rtti -fno-exceptions  -static-openmp")
  
set(CMAKE_CXX_STANDARD 17)
# add_executable(minimal
#   ${SRCS}
# )
add_library(bstFaceUnlock SHARED 
  ${SRCS}
)
target_link_libraries(bstFaceUnlock
  ${DEPS}
  log
)
