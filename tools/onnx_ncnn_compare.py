import ncnn

import numpy as np
import onnxruntime as rt
import cv2
import onnx
# 加载模型
''' 检测
sess = rt.InferenceSession('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/best.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/face_detection_yolo_v0.6.param')
net.load_model('/home/<USER>/work/bst/BYD/yolov7/release/face/size_128/faceunlock/v0.6/face_detection_yolo_v0.6.bin')

'''
'''

'''
'''

# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.onnx')

net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.bin')

'''

# sess = rt.InferenceSession('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/checkpoint_best.onnx')


# 修改onnx模型输出
onnx_model = onnx.load('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/checkpoint_best.onnx')
# onnx_outputs = [x.name for x in ort_session.get_outputs()]


net = ncnn.Net()
net.load_param('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.param')
net.load_model('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur.bin')




for node in onnx_model.graph.node:
    for output in node.output:
        onnx_model.graph.output.extend([onnx.ValueInfoProto(name=output)])
sess = rt.InferenceSession(onnx_model.SerializeToString())
# net.opt.use_fp16_packed = False
# net.opt.use_fp16_storage = False
# net.opt.use_fp16_arithmetic = False
# net.opt.use_int8_packed = False
# net.opt.use_int8_storage = False
# net.opt.use_int8_arithmetic = False
# 获取输入输出的名字
input_name = sess.get_inputs()[0].name
for idx,out in enumerate(sess.get_inputs()):
    print("input idx:",idx,out.name)
input_shape = sess.get_inputs()[0].shape
for idx,out in enumerate(sess.get_outputs()):
    print("output idx:",idx,out.name)
output_name = sess.get_outputs()[78].name

# 读取图像
image = cv2.imread('/home/<USER>/work/bst/face_base/unlock/bst_face_unlock/cmake-build-debug/test/bst_net_in.png')
image = cv2.resize(image, (input_shape[2], input_shape[3]), interpolation=cv2.INTER_LINEAR)  # (w, h)
if input_shape[1] ==3:
    mean_vals = [127.5, 127.5, 127.5]
    norm_vals = [1/127.5, 1/127.5, 1/127.5]
    img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
else:
    mean_vals = [127.5]
    norm_vals = [1/127.5]
    img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    img = np.expand_dims(img, axis=2)
# cv2.imshow("img_in", img)
img = img.astype(np.float32)# / 255.0

img = (img - mean_vals) * norm_vals

img = np.transpose(img, axes=(2, 0, 1))
# 将图像转换为ONNX Runtime需要的格式
input_data = np.expand_dims(img, axis=0)
input_data = input_data.astype(np.float32)  # 转换为浮点数

# 进行推理
onnx_output = sess.run([output_name], {input_name: input_data})



img_h = img.shape[0]
img_w = img.shape[1]
if input_shape[1] ==3:
    mat_in = ncnn.Mat.from_pixels_resize(
        image,
        ncnn.Mat.PixelType.PIXEL_BGR2RGB,
        image.shape[1],
        image.shape[0],
        image.shape[1],
        image.shape[0],
    )
else:
    mat_in = ncnn.Mat.from_pixels_resize(
        image,
        ncnn.Mat.PixelType.PIXEL_BGR2GRAY,
        image.shape[1],
        image.shape[0],
        image.shape[1],
        image.shape[0],
    )
mat_in.substract_mean_normalize(mean_vals, norm_vals)

# mat_in_numpy = mat_in.reshape(mat_in.w, mat_in.h, mat_in.c)

mat_in_numpy = np.array(mat_in)
diff = np.linalg.norm(mat_in_numpy - img) / np.linalg.norm(mat_in_numpy + img)
print(diff)
# mat_in.substract_mean_normalize([], norm_vals)
# mat_in.substract_mean_normalize(mean_vals, [])

ex = net.create_extractor()
ex.set_num_threads(1)
ex.input(0, mat_in)

ret, mat_out = ex.extract(output_name)
ret, mask_attr = ex.extract("mask_attr")
ret, blur_attr = ex.extract("blur_attr")
ret, hm = ex.extract("hm")
ret, offset_x = ex.extract("offset_x")
ret, offset_y = ex.extract("offset_y")
ret, vis = ex.extract("vis")
ret, pose = ex.extract("pose")
ret, uncertain = ex.extract("uncertain")

# mat_out = mat_out.reshape(mat_out.w * mat_out.h * mat_out.c)

ncnn_out = np.array(mat_out)

# 输出结果
print(onnx_output)
print(ncnn_out)
# 比较两个模型的输出结果是否一致，返回一致性比率（相似度）
diff = np.linalg.norm(onnx_output - ncnn_out) / np.linalg.norm(onnx_output + ncnn_out)
print("Difference: ", diff)  # 如果差异非常小（接近0），则说明两个模型的输出结果一致性很高（相似度很高）