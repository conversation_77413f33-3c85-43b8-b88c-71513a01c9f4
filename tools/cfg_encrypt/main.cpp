#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "DES.h"

const char *DES_PARA_KEY = "BST_PARA_2189";
#define CFG_VERSION_LEN 64

int main(int argc, char *argv[])
{
	if (argc < 2)
	{
		printf("Encrypt Usage:\n");
		printf("\tEncrypt 0 version input output \n");
		printf("Decrypt Usage:\n");
		printf("\tEncrypt 1 input output \n");
		return -1;
	}

	int opt = 0;
	sscanf(argv[1], "%d", &opt);

	if (opt == 0)
	{
		if (argc < 5)
		{
			printf("Encrypt Usage:\n");
			printf("\tEncrypt 0 version input output \n");
			return -1;
		}

		printf("Encrypt...\n");
		printf("version is : %s\n", argv[2]);
		if (strlen(argv[2]) > CFG_VERSION_LEN)
		{
			printf("version lenght must be less than %d\n", CFG_VERSION_LEN);
			return -1;
		}
		printf("input: %s\n", argv[3]);
		printf("output: %s\n", argv[4]);
		
		FILE *fOut = fopen(argv[4], "wb");
		if (fOut == NULL)
		{
			printf("Open out file %s error \n", argv[4]);
			fclose(fOut);
			return -1;
		}

		char magic[4] = { 0x04, 0x05, 0x06, 0x07};
		fwrite(magic, 1, 4, fOut);

		char version[CFG_VERSION_LEN];
		memset(version, 0, CFG_VERSION_LEN);
		strcpy(version, argv[2]);
		fwrite(version, 1, CFG_VERSION_LEN, fOut);

		// int Offset = 0;

		int val;
		{
			FILE *fIn = fopen(argv[3], "rb");
			if (fIn == NULL)
			{
				printf("Open in file %s error \n", argv[3]);
				return -1;
			}

			fseek(fIn, 0, SEEK_END);
			int iFileLen = ftell(fIn);
			rewind(fIn);

			int dataLen = iFileLen;
			dataLen = (dataLen + 7) / 8 * 8 + 8;
			char *pIn = new char[dataLen];
			char *pOut = new char[dataLen];

			memset(pIn, '\0', dataLen);
			memset(pOut, '\0', dataLen);

			fread(pIn, 1, iFileLen, fIn);
			fclose(fIn);

			Using_DES(pOut, pIn, dataLen, strlen(DES_PARA_KEY), DES_PARA_KEY, DES_ENCRYPTION);

			// fseek(fOut, 4 + CFG_VERSION_LEN + 4, SEEK_SET);
			// val = Offset;      fwrite(&val, sizeof(int), 1, fOut);
			// val = dataLen;     fwrite(&val, sizeof(int), 1, fOut);

			fseek(fOut, 4 + CFG_VERSION_LEN, SEEK_SET);
			fwrite(pOut, 1, dataLen, fOut);

			delete[] pIn;
			delete[] pOut;
			
			// Offset += dataLen;
		}

		fclose(fOut);
	}
	else
	{
		if (argc < 4)
		{
			printf("Decrypt Usage:\n");
			printf("\tEncrypt 1 input output \n");
			return -1;
		}

		printf("Decrypt...\n");
		FILE *fIn = fopen(argv[2], "rb");
		if (fIn == NULL)
		{
			printf("Open file %s error \n", argv[2]);
			fclose(fIn);
			return -1;
		}

		char magic[4];
		fread(magic, 1, 4, fIn);
		
		if (!(magic[0] == 0x04 && magic[1] == 0x05 && magic[2] == 0x06 && magic[3] == 0x07))
		{
			fclose(fIn);
			printf("file %s is not an encrypted file\n", argv[2]);
			return -1;
		}

		// ciphertext
        // FILE* fp = fopen((char*)config.cfg.config_path, "rb");
        fseek(fIn, 0, SEEK_END);
        int file_len = ftell(fIn);
        rewind(fIn);
        int   data_len   = file_len - CFG_VERSION_LEN - 4;
        char* ciphertext = new char[file_len];
        char* plaintext  = new char[data_len + 1];
        fread(ciphertext, 1, file_len, fIn);
        fclose(fIn);

		char cfg_version[64 + 1];
		memset(cfg_version, 0, CFG_VERSION_LEN + 1);
        memcpy(cfg_version, ciphertext + 4, CFG_VERSION_LEN);
        printf("BlackSesame FaceUnlock Cfg Version : %s\n", cfg_version);

		int dec_len = data_len - data_len % 8;
        if (!Using_DES(plaintext, ciphertext + 4 + CFG_VERSION_LEN, dec_len, strlen(DES_PARA_KEY), DES_PARA_KEY, DES_DECRYPTION)) {
            printf("Decode config error\n");
        }
        for (int i = 0; i < data_len % 8; i++) {
            plaintext[dec_len + i] = ciphertext[dec_len + 36 + i];
        }
        plaintext[data_len] = '\0';


		FILE* fp = fopen(argv[3], "wb");
		if (NULL == fp)
		{
			printf("Open out file %s error \n", argv[3]);
		}
		else
		{
			fwrite(plaintext, 1, data_len, fp);
			fclose(fp);
		}

		delete[] ciphertext;
		delete[] plaintext;
	}
	return 0;
}