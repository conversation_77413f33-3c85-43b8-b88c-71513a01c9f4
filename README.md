face sdk full tee 开发仓库

# Version

Model       VersionModel
P811        .2
U655AA      .3
X9A         .4

# 回复客户

## dump 方法

1. 日志开关
adb shell setprop debug.bstfu.log.enable 1
2. dump 图片
adb shell setprop debug.bstfu.dumpyuv 1
dump路径：/data/vendor/bst_face_unlock/images
3. 如果无法保存dump图片，麻烦执行如下命令：
adb root
adb shell setenforce 0
adb remount

# 性能测试

基于 2.3.0.4 版本，安卓部署


6769H 总体约100ms

| Model    | NCNN 16 | NCNN 32 | TFL 16 | TFL 32 |
| -------- | ------- | ------- | ------ | ------ |
| Detect   | 8.29    | 21.59   | 8.872  | 12.679 |
| Landmark | 2.30    | 3.58    | 3.180  | 5.924  |
| Live     | 50.29   | 54.39   | NA     | 41.205 |
| Feature  | 14.427  | 28.57   | 14.407 | 25.589 |

6765 总体约200ms

| Model    | NCNN 32 6765 | TFL 32 6765 | TFL 16 | TFL 32 |
| -------- | ------------ | ----------- | ------ | ------ |
| Detect   | 22.94        | 21.80       | 8.872  | 12.679 |
| Landmark | 6.21         | 5.57        | 3.180  | 5.924  |
| Live     | 76.95        | 96.23       | NA     | 41.205 |
| Feature  | 35.06        | 47.77       | 14.407 | 25.589 |

内存共占用约 

# 日志开关

adb shell setprop debug.bstfu.log.enable 1

# dump

adb shell setprop debug.bstfu.dumpyuv 1


# 模型转换步骤

onnx2ncnn
```bash
./onnx/onnx2ncnn /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/detector/v0.7/size_192_v0.7.onnx /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/detector/v0.7/size_192_v0.7.param /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/detector/v0.7/size_192_v0.7.bin
```
nccn 模型转header文件
```
ncnn2mem yolov7-lite-s.param yolov7-lite-s.bin detect.id.h detect.mem.h
```

# ncnn github 版本 build 配置

version : ncnn-20230223

## android arm64 (mult-thread cpuonly)

```
cmake -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-24 .. 
```

## android sim-secureos arm64

```
cmake -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Tools/android-ndk-r25c/build/cmake/android.toolchain.cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-21 -DNCNN_PLATFORM_API=OFF -DNCNN_THREADS=OFF -DNCNN_OPENMP=OFF -DNCNN_SIMPLEOMP=OFF -DNCNN_SIMPLESTL=OFF -DNCNN_BUILD_TESTS=OFF -DNCNN_BUILD_BENCHMARK=OFF -DNCNN_BUILD_TOOLS=OFF -DNCNN_BUILD_EXAMPLES=OFF -DNCNN_AVX2=OFF -DNCNN_ARM82=OFF ..
```

## darwin/linux

### .a

```
cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Debug" -DNCNN_FORCE_NO_CPPRT=OFF -DNCNN_DISABLE_RTTI=ON -DNCNN_DISABLE_EXCEPTION=ON -DNCNN_STDIO=ON -DNCNN_SIMPLEOCV=ON -DNCNN_STRING=ON -DNCNN_PLATFORM_API=OFF -DNCNN_THREADS=OFF -DNCNN_OPENMP=OFF -DNCNN_SIMPLEOMP=OFF -DNCNN_VULKAN=OFF -DNCNN_RUNTIME_CPU=ON -DNCNN_SIMPLESTL=OFF -DNCNN_BUILD_TESTS=ON -DNCNN_BUILD_BENCHMARK=OFF -DNCNN_BUILD_TOOLS=OFF -DNCNN_BUILD_EXAMPLES=ON ..
```

### tools

```
cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Debug" -DNCNN_FORCE_NO_CPPRT=OFF -DNCNN_DISABLE_RTTI=ON -DNCNN_DISABLE_EXCEPTION=ON -DNCNN_STDIO=ON -DNCNN_SIMPLEOCV=ON -DNCNN_STRING=ON -DNCNN_PLATFORM_API=OFF -DNCNN_THREADS=OFF -DNCNN_OPENMP=OFF -DNCNN_SIMPLEOMP=OFF -DNCNN_VULKAN=OFF -DNCNN_RUNTIME_CPU=ON -DNCNN_SIMPLESTL=OFF -DNCNN_BUILD_TESTS=ON -DNCNN_BUILD_BENCHMARK=OFF -DNCNN_BUILD_TOOLS=ON -DNCNN_BUILD_EXAMPLES=ON ..
```