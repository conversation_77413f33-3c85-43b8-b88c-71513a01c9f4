#!/usr/bin/env bash

# SM-A226L MT6833
# apk : /data/app/~~OgTXGNbmejFbs3v5ZQz9yA==/com.bst.facerecognize-AKQdNyv__Mv6a_AIASghJA==/lib/arm64
# TARGET=/data/app/~~OgTXGNbmejFbs3v5ZQz9yA==/com.bst.facerecognize-AKQdNyv__Mv6a_AIASghJA==/lib/arm64
TARGET=/data/app/~~rF1hpHpjY-tWF1WlnYsMiA==/com.bst.facerecognize-fLemrbmb0DMAKvmTzCgroA==/lib/arm64

if [ $# -eq 0 ]
then
    echo "push2apk.sh [-s][-a][-as]" 
    exit
fi

# -c   Clean
if [ $1 ] && [ $1 = "-s" ]
then
    echo "push secureos arm64-v8a ..."
    adb shell "rm -rf $TARGET/*"
fi

# -s    Push SecureOS Target
if [ $1 ] && [ $1 = "-s" ]
then
    echo "push secureos arm64-v8a ..."
    adb push build-secureos-arm64-v8a/src/libbstFaceUnlock.so $TARGET
fi

# -a    Push Android Target
if [ $1 ] && [ $1 = "-a" ]
then
    echo "push android arm64-v8a ..."
    adb push build-android-arm64-v8a/src/libbstFaceUnlock.so $TARGET
fi

# -as    Push Android Sim-SecureOS Target
if [ $1 ] && [ $1 = "-as" ]
then
    echo "push android-sim-secureos arm64-v8a ..."
    adb push build-android-sim-secureos-arm64-v8a/src/libbstFaceUnlock.so $TARGET
fi
