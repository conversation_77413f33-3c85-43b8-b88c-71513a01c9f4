#ifndef COMMON_DEF_H
#define COMMON_DEF_H

#include "platform.h"
#include "math.h"

#define NUM_KEYPOINTS 0

struct Point {
    float x;
    float y;
    float prob;

    void rotate(double theta, const Point& about) {
        double angle_rad = theta * M_PI / 180.0;
        double x_shifted = x - about.x;
        double y_shifted = y - about.y;
        double x_rotated = x_shifted * cos(angle_rad) - y_shifted * sin(angle_rad);
        double y_rotated = x_shifted * sin(angle_rad) + y_shifted * cos(angle_rad);
        x                = x_rotated + about.x;
        y                = y_rotated + about.y;
    }
};

struct Object {
    float x1;
    float y1;
    float x2;
    float y2;
    float score;
    int32_t     class_id;
    Point landmark[NUM_KEYPOINTS];
};

struct BBox {
    int   x, y, w, h;
    float score;
    BBox()
        : x(0), y(0), w(0), h(0), score(0.) {
    }
    BBox(int bx, int by, int bw, int bh, float bscore)
        : x(bx), y(by), w(bw), h(bh), score(bscore) {
    }
};

#ifndef BST_BBOX
#define BST_BBOX
struct Bbox {
    float score;
    float x1;
    float y1;
    float x2;
    float y2;
    float area;
    float ppoint[12]; // xy xy xy xy xy
    float regreCoord[4];
};
#endif

#ifndef DET_BOX_TAG
#define DET_BOX_TAG
typedef struct DETBox {
    BBox  bbox;
    float conf;
    int   class_id;

    bool               head_is_valid;
    std::vector<float> dfl32softmax; // l t r b

    DETBox() {
        bbox.x        = 0;
        bbox.y        = 0;
        bbox.w        = 0;
        bbox.h        = 0;
        conf          = 0;
        class_id      = 0;
        head_is_valid = false;
    };

    DETBox(int x, int y, int w, int h, float _conf) {
        bbox.x   = x;
        bbox.y   = y;
        bbox.w   = w;
        bbox.h   = h;
        conf     = _conf;
        class_id = 0;
    };
    DETBox(int x, int y, int w, int h, float _conf, int _clsid) {
        bbox.x   = x;
        bbox.y   = y;
        bbox.w   = w;
        bbox.h   = h;
        conf     = _conf;
        class_id = _clsid;
    };
} DETBox;
#endif

typedef struct LmkInfo {
    float              uncertain;
    float              mask_prob; // 遮挡程度
    float              blur_prob; // 模糊程度
    std::vector<Point> pts;
    std::vector<float> probs;
    std::vector<float> poses; // yaw,pitch,roll
    std::vector<float> vis_probs;
    Object             face_box;
    int        eye_open_status;
    int        nose_status;
} LmkInfo;

#endif // COMMON_DEF_H