#ifndef __BSTFACEUNLOCK_H__
#define __BSTFACEUNLOCK_H__

#ifdef __cplusplus
extern "C" {
#endif

// #define MOCK_TEE

// clang-format off

#define _In_
#define _In_opt_
#define _Inout_
#define _Out_

/* Image data type defination */
#define BST_IMAGE_TYPE_NV12 0
#define BST_IMAGE_TYPE_NV21 1

/* Face Feature Dimension */
#define FEATURE_DIM_SDK 1024

/* SDK Config type definition */
#define BST_FU_CONFIG_TYPE_FILE   0 /* config by file */
#define BST_FU_CONFIG_TYPE_BUFFER 1 /* config by buffer */
#define BST_FU_CONFIG_TYPE_BUILT_IN 2 /* built-in config */

/* Error Code */
/* Success */
#define BST_FU_SUCCESS                      0x0000  /* 0 success */
/* Fatal Error */
#define BST_FU_E_NOT_INITED                 0xE001  /* 57345 not inited */
#define BST_FU_E_INVALID_CONFIG             0xE002  /* 57346 invalid config */
#define BST_FU_E_INVALID_INPUT              0xE003  /* 57347 invalid input */
#define BST_FU_E_INVALID_MODEL              0xE004  /* 57348 invalid model param */
#define BST_FU_E_OOM                        0xE005  /* 57349 out of memory */
#define BST_FU_E_INNER_ERROR                0xE006  /* 57350 inner fatal error */
/* Auth/Enroll Fail */
#define BST_FU_F_TIMEOUT                    0xF001  /* 61441 timeout */
#define BST_FU_F_ATTACK                     0xF002  /* 61442 attack detected */
#define BST_FU_F_ENROLL_MPERSON             0xF003  /* 61443 enroll fail - multi person detected */
/* Auth/Enroll Continue */
#define BST_FU_CONTINUE                     0xC001  /* 49153 feature extracted, continue */
#define BST_FU_C_FACE_NOT_FOUND             0xC002  /* 49154 face not found */
#define BST_FU_C_NEAR_BORDER                0xC003  /* 49155 face near border of screen */
#define BST_FU_C_NEAR_BORDER_TOP            0xC013  /* 49171 face near border of screen */
#define BST_FU_C_NEAR_BORDER_BOTTOM         0xC023  /* 49187 face near border of screen */
#define BST_FU_C_NEAR_BORDER_LEFT           0xC033  /* 49203 face near border of screen */
#define BST_FU_C_NEAR_BORDER_RIGHT          0xC043  /* 49219 face near border of screen */
#define BST_FU_C_NEAR_BORDER_TOP_LEFT       0xC053  /* 49253 face near border of screen */
#define BST_FU_C_NEAR_BORDER_TOP_RIGHT      0xC063  /* 49251 face near border of screen */
#define BST_FU_C_NEAR_BORDER_BOTTOM_LEFT    0xC073  /* 49267 face near border of screen */
#define BST_FU_C_NEAR_BORDER_BOTTOM_RIGHT   0xC083  /* 49283 face near border of screen */
#define BST_FU_C_OCCLUDED                   0xC004  /* 49156 face occluded */
#define BST_FU_C_TOO_DARK                   0xC005  /* 49157 input image too dark */
#define BST_FU_C_TOO_BRIGHT                 0xC006  /* 49158 input image too bright */
#define BST_FU_C_TOO_BLUR                   0xC007  /* 49159 input image too blur */
#define BST_FU_C_POSE_ABNORMAL              0xC008  /* 49160 abnoraml body position */
#define BST_FU_C_CLOSED_EYE                 0xC009  /* 49161 closed eye detected */
#define BST_FU_C_SUSPECTED                  0xC00A  /* 49162 suspected attack detected */

// clang-format on

/* Configuration Infomation Defination */
typedef struct {
    union {
        const char* config_path; /* faceUnlock configuration file */
        struct {
            const void*  buffer; /* faceUnlock configuration buffer */
            unsigned int size;
        } config_buffer;
    } cfg;
    int config_type;

} BSTFaceUnlockConfig;

/* Version Defination */
typedef struct {
    unsigned int base;           /* codebase version */
    unsigned int major;          /* major version */
    unsigned int minor;          /* minor version */
    unsigned int build;          /* build version */
    const char*  lib_version;    /* version of c/cpp lib */
    const char*  config_version; /* version of config */
    const char*  build_date;     /* latest build date */
} BSTFaceUnlockVersion;

/* Frame Defination */
typedef struct {
    unsigned int format;   /* image format */
    unsigned int width;    /* image width */
    unsigned int height;   /* image height */
    unsigned int stride;   /* image stride */
    unsigned int rotation; /* degree of rotation */
    void*        data;     /* image data ptr */
} BSTFaceUnlockFrameData;

/* Meta Defination */
typedef struct {
    int          iso;
    unsigned int reserved[15]; /* reserved */
} BSTImageMetaData;

/* Image Defination */
typedef struct {
    BSTFaceUnlockFrameData img_data;  /* image data */
    BSTImageMetaData       meta_data; /* reserved data */
} BSTFaceUnlockImage;

/* Face Feature Defination */
typedef struct {
    float feature[FEATURE_DIM_SDK]; /* feature */
    float threshold;                /* threshold */
} BSTFaceFeature;

/* Retrive FaceUnlock Lib Version */
#ifdef MOCK_TEE
const BSTFaceUnlockVersion* bstFaceUnlockGetVersionInner();
#else
const BSTFaceUnlockVersion* bstFaceUnlockGetVersion();
#endif

/* Initialize BST FaceUnlock */
#ifdef MOCK_TEE
int bstFaceUnlockInitInner(
#else
int bstFaceUnlockInit(
#endif
    _In_ const BSTFaceUnlockConfig* const config /* config parameters */
);

/* Un-initialize BST FaceUnlock */
#ifdef MOCK_TEE
int bstFaceUnlockUninitInner();
#else
int bstFaceUnlockUninit();
#endif

/* Enroll */
#ifdef MOCK_TEE
int bstFaceUnlockEnrollInner(
#else
int bstFaceUnlockEnroll(
#endif
    _In_ const BSTFaceUnlockImage* const p_img_in, /* image */
    _Out_ BSTFaceFeature* const          p_feature /* feature */
);

/* Reset Enroll Status */
#ifdef MOCK_TEE
int bstFaceUnlockEnrollResetInner();
#else
int bstFaceUnlockEnrollReset();
#endif

/* Authenticate */
#ifdef MOCK_TEE
int bstFaceUnlockAuthenticateInner(
#else
int bstFaceUnlockAuthenticate(
#endif
    _In_ const BSTFaceUnlockImage* const p_img_in, /* image */
    _Out_ BSTFaceFeature* const          p_feature /* feature */
);

/* Reset Authenticate Status */
#ifdef MOCK_TEE
int bstFaceUnlockAuthenticateResetInner();
#else
int bstFaceUnlockAuthenticateReset();
#endif

#ifdef __cplusplus
}
#endif

#endif // ifndef __BSTFACEUNLOCK_H__