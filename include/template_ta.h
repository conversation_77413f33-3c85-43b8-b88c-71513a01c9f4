#pragma once

#include <sys/resource.h>

/********* !!!! WARNING !!!! ***************
 * Please make sure such header is not included
 * in more than one source-file of TA
 *******************************************/
//b52944c3-22d6-432c-9f77-b2960ad7a00c
#define TA_PROP_UUID                {0x00000000, 0x4875, 0x4171, {0x49, 0x6e, 0x46, 0x61, 0x63, 0x65, 0x68, 0x71}}
#define TA_PROP_SINGLE_INSTANCE     TRUE
#define TA_PROP_MULTISESSION        TRUE
#define TA_PROP_INSTANCE_KEEPALIVE  FALSE
#define TA_PROP_DATASIZE            RLIM_INFINITY
#define TA_PROP_STACKSIZE           (5 * 1024 * 1024)
#define TA_PROP_GROUP_ID            "samsung_ta"
#define TA_PROP_VERSION             "ver. none      "

/* !!!! WARNING !!!! Do not write your code in this file !!!! */
#include <ta_property.h>