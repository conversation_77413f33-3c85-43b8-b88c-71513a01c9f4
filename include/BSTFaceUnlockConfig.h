#ifndef __BSTFACEUNLOCK_CONFIG_H__
#define __BSTFACEUNLOCK_CONFIG_H__

#include "BSTFaceUnlock.h"
#include "Json.h"
#include "DES.h"
#include "CommonUtils.h"

#ifdef __ANDROID__
#include <sys/system_properties.h>
#endif // __ANDROID__

typedef struct CommonConfig {
    bool        log_enable = false;
    bool        verfy_manu = false;
    bool        verfy_prod = false;
    bool        verfy_time = false;
    std::string manufacturer;
    std::string product_name;
    std::string time_end;

    bool        dump_enable = false;
    std::string default_dump_path;
} CommonConfig;

typedef struct FilterConfig {
    bool bright_filter_enable = true;
    bool blur_filter_enable   = true;
    bool occ_filter_enable    = true;
    bool live_filter_enable   = true;
    bool blink_filter_enable  = true; // blink_filter_enable
    bool angel_filter_enable  = true; // angel_filter_enable

    float match_thres           = 0.55;
    float match_thres_withmask  = 0.4;
    float match_thres_big_pitch = 0.4;
    int match_thres_up_pitch_thres = 25;
    int match_thres_down_pitch_thres = 20;
    int match_thres_left_pitch_thres = 20;
    int match_thres_right_pitch_thres = 20;

    float face_dark_level_1_thres   = 20.; // 人脸暗光阈值1, iSO小于iso_dark_level_1_thres时使用
    float face_dark_level_2_thres   = 40.; // 人脸暗光阈值2,   iSO大于于iso_dark_level_1_thres时使用
    float iso_dark_thres   = 6400.; // ISO暗光阈值1, iSO小于iso_dark_level_1_thres时使用
    float iso_dark_level_1_thres   = 2000.; // ISO暗光阈值2, iSO大于iso_dark_level_1_thres时使用
    float live_face_dark_thres = 3.; // 人脸暗光估计阈值
    float live_bg_dark_thres   = 3.; //  背景暗光估计阈值
    float bright_thres = 200.;
    float model_blur_thres   = 0.8; // 模糊阈值，值越大越模糊
    float quality_blur_thres   = 0.8; // 模糊阈值，值越大越模糊
    float  live_dark_thres= 0.955; // 人脸暗光模型真人置信度阈值
    float  live_normal_thres= 0.955; // 人脸正常模型真人置信度阈值
    float  live_ISO_thres= 500;     // 人脸ISO阈值，用于判断是否为低光照片，值越大环境越暗
    float blink_thres  = 0.8;
    float mask_hole_thres = 0.5;
    float mask_thres   = 0.5;
    float left_yaw_thres    = 20; /// 左旋转
    float up_pitch_thres  = 15;   /// 抬头
    float right_yaw_thres    = 20; /// 右旋转
    float down_pitch_thres  = 15;  /// 低头角度头
    int motion_height_divide_count = 4; // 人脸运动检测分割高度，值越大分割越细
    int motion_width_divide_count = 4;  // 人脸运动检测分割宽度，值越大分割越细
    int motion_abs_thres = 3; // 人脸运动检测绝对阈值，值越大运动越明显
    float motion_rel_thres = 0.02; // 人脸运动检测相对阈值，值越大运动越明显

    int timeout_second                  = 30;
    int timeout_frame                   = 600;
    int max_livingD_suspected_live_attack = 3;
    int max_livingC_suspected_live_attack = 3;
    int max_suspected_live_attack       = 5;
    int max_suspected_live_attack_range = 10;
    int max_continue_real               = 3;
    int after_suspected_continue_real   = 2;


    int border_left   = 0;
    int border_right  = 0;
    int border_top    = 0;
    int border_bottom = 0;
    float min_face = 0.;
    void PrintConfig() {
        printf("bright_filter_enable = %d, blur_filter_enable = %d, occ_filter_enable = %d, live_filter_enable = %d, blink_filter_enable = %d, angel_filter_enable = %d\n",
               bright_filter_enable, blur_filter_enable, occ_filter_enable, live_filter_enable, blink_filter_enable, angel_filter_enable);
    }
} FilterConfig;

typedef struct FaceUnlockConfig {
    CommonConfig common_config;
    FilterConfig enroll_config;
    FilterConfig authen_config;
} FaceUnlockConfig;

bool ParseConfig(FaceUnlockConfig& fu_config, const char* const json);

bool LoadConfig(const BSTFaceUnlockConfig& config, FaceUnlockConfig& fu_config, char* cfg_version_out);

#endif // ifndef __BSTFACEUNLOCK_CONFIG_H__