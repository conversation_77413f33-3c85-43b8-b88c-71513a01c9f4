#ifndef COMMON_UTILS_H
#define COMMON_UTILS_H

#include "CommonDef.h"

#ifndef SECUREOS
#include <string>
#include <vector>
#include "math.h"
#else
// #include "openlibm/openlibm.h"
#include "math.h"
#endif
#include <stdio.h>

#include "mat.h"
#include "simpleocv.h"
#ifndef SECUREOS
#include "stb/stb_image_write.h"
#include "stb/stb_image.h"
#include <sys/time.h>
#ifdef __ANDROID__
#include <android/log.h>
#include <sys/system_properties.h>
#endif
#endif

#define LMK_NUM 5 // 关键点个数
#define CHANNEL 3

namespace bstutils {

inline void nms(std::vector<Object>& input_boxes,float iou_thd) {
    std::vector<float> vArea(input_boxes.size());
    for (int i = 0; i < int(input_boxes.size()); ++i) {
        vArea[i] = (input_boxes[i].x2 - input_boxes[i].x1 + 1)
                   * (input_boxes[i].y2 - input_boxes[i].y1 + 1);
    }
    for (int i = 0; i < int(input_boxes.size()); ++i) {
        for (int j = i + 1; j < int(input_boxes.size());) {
            float xx1   = std::max(input_boxes[i].x1, input_boxes[j].x1);
            float yy1   = std::max(input_boxes[i].y1, input_boxes[j].y1);
            float xx2   = std::min(input_boxes[i].x2, input_boxes[j].x2);
            float yy2   = std::min(input_boxes[i].y2, input_boxes[j].y2);
            float w     = std::max(float(0), xx2 - xx1 + 1);
            float h     = std::max(float(0), yy2 - yy1 + 1);
            float inter = w * h;
            float ovr   = inter / (vArea[i] + vArea[j] - inter);
            if (ovr >= iou_thd) {
                input_boxes.erase(input_boxes.begin() + j);
                vArea.erase(vArea.begin() + j);
            } else {
                j++;
            }
        }
    }
}

// 多类别NMS函数 - 不使用std::map的实现
inline void nms_multi_class(std::vector<Object>& input_boxes, float iou_thd) {
    if (input_boxes.empty()) {
        return;
    }
    
    // 先按类别ID排序，相同类别的检测框会聚集在一起
    for (size_t i = 0; i < input_boxes.size(); ++i) {
        for (size_t j = i + 1; j < input_boxes.size(); ++j) {
            if (input_boxes[i].class_id > input_boxes[j].class_id) {
                Object temp = input_boxes[i];
                input_boxes[i] = input_boxes[j];
                input_boxes[j] = temp;
            }
        }
    }
    
    // 对每个类别分别进行NMS
    size_t start_idx = 0;
    size_t current_class_id = input_boxes[0].class_id;
    
    for (size_t i = 1; i <= input_boxes.size(); ++i) {
        // 当遇到不同类别或到达末尾时，对当前类别进行NMS
        if (i == input_boxes.size() || input_boxes[i].class_id != current_class_id) {
            // 对当前类别进行NMS
            std::vector<float> vArea(i - start_idx);
            for (size_t j = 0; j < i - start_idx; ++j) {
                vArea[j] = (input_boxes[start_idx + j].x2 - input_boxes[start_idx + j].x1 + 1)
                           * (input_boxes[start_idx + j].y2 - input_boxes[start_idx + j].y1 + 1);
            }
            
            for (size_t j = 0; j < i - start_idx; ++j) {
                for (size_t k = j + 1; k < i - start_idx;) {
                    float xx1   = std::max(input_boxes[start_idx + j].x1, input_boxes[start_idx + k].x1);
                    float yy1   = std::max(input_boxes[start_idx + j].y1, input_boxes[start_idx + k].y1);
                    float xx2   = std::min(input_boxes[start_idx + j].x2, input_boxes[start_idx + k].x2);
                    float yy2   = std::min(input_boxes[start_idx + j].y2, input_boxes[start_idx + k].y2);
                    float w     = std::max(0.0f, xx2 - xx1 + 1);
                    float h     = std::max(0.0f, yy2 - yy1 + 1);
                    float inter = w * h;
                    float ovr   = inter / (vArea[j] + vArea[k] - inter);
                    if (ovr >= iou_thd) {
                        input_boxes.erase(input_boxes.begin() + start_idx + k);
                        vArea.erase(vArea.begin() + k);
                        --i; // 调整索引
                    } else {
                        k++;
                    }
                }
            }
            
            // 更新下一个类别的起始位置
            if (i < input_boxes.size()) {
                start_idx = i;
                current_class_id = input_boxes[i].class_id;
            }
        }
    }
}


inline bool cmp(Object b1, Object b2) {
    return b1.score > b2.score;
}
float min(float a, float b);

float min(float a, float b, float c, float d);

float max(float a, float b);

float max(float a, float b, float c, float d);

bool pointInsideBox(Point pt, Object box);

bool expand_box(Object box,Object &expand_box,float ratio = 1.0);

template<typename RandomIt, typename Compare>
void bubble_sort(RandomIt first, RandomIt last, Compare comp) {
    // TODO: heap sort should be used here, but we simply use bubble sort now
    for (RandomIt i = first; i < last; ++i) {
        // bubble sort
        for (RandomIt j = last - 1; j > first; --j) {
            if (comp(*j, *(j - 1))) {
                std::swap(*j, *(j - 1));
            }
        }
    }
}

void BGR2Gray(const unsigned char* bgr, int w, int h, unsigned char* gray);
void RGB2Gray(const unsigned char* bgr, int w, int h, unsigned char* gray);
int  rotate_c3(unsigned char* inImg, unsigned char* outImg, int in_width, int in_height, int type);
void rotate_yuv_nv21_270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original);
void rotate_y270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original);
void rotate_uv270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original);
void nv21_to_rgb_keepsize(unsigned char* pData, int width, int height, int stride, int uv_stride, unsigned char* rgbDataPtr);
void nv12_to_rgb_keepsize(unsigned char* pData, int width, int height, int stride, int uv_stride, unsigned char* rgbDataPtr);

void norm_and_zeropadding(unsigned char* inImg, unsigned char* outImg, int inWidth, int inHeight, int outWidth, int outHeight);

void CropImageC3(const unsigned char* src, int srcw, int srch, unsigned char* dst, int rx, int ry, int rw, int rh);
void CropImageC1(const unsigned char* src, int srcw, int srch, unsigned char* dst, int rx, int ry, int rw, int rh);

// dump image
void DrawPoint(unsigned char* src, int srcw, int srch, int srcc, int dx, int dy, int size, int r, int g, int b);
void DrawRect(unsigned char* src, int srcw, int srch, int srcc, int x1, int y1, int x2, int y2, int size, int r, int g, int b);

void BSTAG_CalAffineTransformData_float(float* pt1_x, float* pt1_y, float* pt2_x, float* pt2_y, int npt, float& rot_s_x, float& rot_s_y,
                                        float& move_x, float& move_y);

void BSTAG_AffineTransformImage_Sam_Bilinear_C3(float rot_s_x, float rot_s_y, float move_x, float move_y,
                                                unsigned char* image, int ht, int wd, const unsigned char* ori_image, int oriht, int oriwd);

void get_rotation_matrix(float angle, float scale, float dx, float dy, float* tm);

void invert_affine_transform(const float* tm, float* tm_inv);
void warpaffine_bilinear_c3(const unsigned char* src, int srcw, int srch, int srcstride, unsigned char* dst, int w, int h, int stride, const float* tm, int type, unsigned int v);

void lighting_aug(const unsigned char* src_img, unsigned char* dst_img, int img_size);


static inline float sigmoid(float x) {
    return static_cast<float>(1.f / (1.f + exp(-x)));
}

static inline void softmax(float* pData, int len, int& max_index) {
    float maxData  = pData[0];
    float maxValue = 0;
    max_index      = 0;
    for (int i = 1; i < len; i++) {
        maxData = std::max(maxData, pData[i]);
    }

    float sum = 0;
    for (int i = 0; i < len; i++) {
        pData[i] = exp(pData[i] - maxData);
        sum += pData[i];
    }

    for (int i = 0; i < len; i++) {
        pData[i] = pData[i] / sum;
        if (maxValue <= pData[i]) {
            maxValue  = pData[i];
            max_index = i;
        }
    }
}
static inline void softmax(float* pData, int len) {
    float maxData = pData[0];

    for (int i = 1; i < len; i++) {
        maxData = std::max(maxData, pData[i]);
    }

    float sum = 0;
    for (int i = 0; i < len; i++) {
        pData[i] = exp(pData[i] - maxData);
        sum += pData[i];
    }

    for (int i = 0; i < len; i++) {
        pData[i] = pData[i] / sum;
    }
}
// dump image
#ifndef SECUREOS
/***
     * dump unsigned char* to png
     * @param name
     * @param img
     * @param w
     * @param h
     * @param c
     */
inline void save_image_png(const std::string& name, unsigned char* img, int w, int h, int c) {
    std::string outname = name + ".png";
    stbi_write_png(outname.c_str(), w, h, c, img, 0);
}

inline unsigned char* load_image(const std::string& name, int& w, int& h, int& c) {
    unsigned char* img = stbi_load(name.c_str(), &w, &h, &c, 0);
    if (img == nullptr) {
        printf("load image failed, name: %s\n", name.c_str());
        exit(-1);
    }
    return img;
}

inline void free_image(unsigned char* img) {
    stbi_image_free(img);
}

/***
 * dump ncnn Mat to png
 * @param name
 * @param img
 * @param type
        PIXEL_RGB  = 1,
        PIXEL_BGR  = 2,
        PIXEL_GRAY = 3,
        PIXEL_RGBA = 4,
        PIXEL_BGRA = 5,
 * @return
 */
inline int save_image_png(const std::string& name, ncnn::Mat img, int type) {
    std::vector<unsigned char> ncnn_input(img.w * img.h * img.c, 0);

    img.to_pixels(ncnn_input.data(), type);

    std::string outname = name + ".png";
    stbi_write_png(outname.c_str(), img.w, img.h, img.c, ncnn_input.data(), 0);
    return 0;
}

#else
inline void save_image_png(const std::string& name, const unsigned char* img, int w, int h, int c) {
    return;
}
#endif

// log
void update_log_setting(bool always_on = false);
#define LOG_TAG        "BlackSesameFU"
#ifndef SECUREOS // SECUREOS = OFF
#ifdef __ANDROID__
#define PROP_VALUE_MAX 92
extern bool g_print_info;
#define LOGI(...)                                                        \
    {                                                                    \
        if (bstutils::g_print_info) {                                    \
            __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__); \
        }                                                                \
    }
#define LOGE(...)                                                     \
    {                                                                 \
        __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__); \
    }
#define LOGF(...)                                                     \
    {                                                                 \
        __android_log_print(ANDROID_LOG_FATAL, LOG_TAG, __VA_ARGS__); \
    }
#else // not android
#define LOGI(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - I - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
#define LOGE(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - E - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
#define LOGF(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - F - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
#endif // __ANDROID__
#else  // SECUREOS = ON
#define LOGI(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - I - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
#define LOGE(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - E - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
#define LOGF(...)            \
    {                        \
        printf(LOG_TAG);     \
        printf(" - F - ");       \
        printf(__VA_ARGS__); \
        printf("\n");        \
    }
// #define LOGI(...) \
//     {}
// #define LOGE(...) \
//     {}
// #define LOGF(...) \
//     {}
#endif

// timer
#ifndef SECUREOS // SECUREOS = OFF
#define TIMER_START(TAG)            \
    struct timeval TAG##_begin_res; \
    gettimeofday(&TAG##_begin_res, NULL);
#define TIMER_STOP(TAG)                    \
    struct timeval TAG##_end_res;          \
    gettimeofday(&TAG##_end_res, NULL);    \
    LOGI("Timer [%-70s Cost] = %f ms", #TAG, \
         ((double)TAG##_end_res.tv_sec * 1000.0 + (double)TAG##_end_res.tv_usec / 1000.0) - ((double)TAG##_begin_res.tv_sec * 1000.0 + (double)TAG##_begin_res.tv_usec / 1000.0));
#else // SECUREOS = ON
#define TIMER_START(TAG) \
    LOGI("Timer %s_begin", #TAG);
#define TIMER_STOP(TAG) \
    LOGI("Timer %s_end", #TAG);
#endif

inline void pretty_copy(const ncnn::Mat& m, float* dst) {
    for (int q = 0; q < m.c; q++) {
        const float* ptr = m.channel(q);
        memcpy(dst, ptr, sizeof(float) * m.w * m.d * m.h);
        dst += m.w * m.d * m.h;
    }
}


/* Compare two feature */
void bstFeatureCompare(
     const float* const feature1,  /* Feature */
     const float* const feature2,  /* Feature */
     int                dimension, /* Dimension of Feature */
     float &      score      /* Score */
);

void ColorHistogramEqualizationOriginal(unsigned char * img,int biHeight,int biWidth);
void ColorHistogramEqualization(unsigned char* img, int biHeight, int biWidth, bool do_sharpening = false, float sharpening_strength = 0.5f);
// inline void pretty_print(const ncnn::Mat& m) {
//     for (int q = 0; q < m.c; q++) {
//         const float* ptr = m.channel(q);
//         for (int z = 0; z < m.d; z++) {
//             for (int y = 0; y < m.h; y++) {
//                 for (int x = 0; x < m.w; x++) {
//                     printf("%f ", ptr[x]);
//                 }
//                 ptr += m.w;
//                 printf("\n");
//             }
//             printf("\n");
//         }
//         printf("------------------------\n");
//     }
// }

//
//inline void visualize(const char* title, const ncnn::Mat& m)
//{
//    std::vector<cv::Mat> normed_feats(m.c);
//
//    for (int i=0; i<m.c; i++)
//    {
//        cv::Mat tmp(m.h, m.w, CV_32FC1, (void*)(const float*)m.channel(i));
//
//        cv::normalize(tmp, normed_feats[i], 0, 255, cv::NORM_MINMAX, CV_8U);
//
//        cv::cvtColor(normed_feats[i], normed_feats[i], cv::COLOR_GRAY2BGR);
//
//        // check NaN
//        for (int y=0; y<m.h; y++)
//        {
//            const float* tp = tmp.ptr<float>(y);
//            uchar* sp = normed_feats[i].ptr<uchar>(y);
//            for (int x=0; x<m.w; x++)
//            {
//                float v = tp[x];
//                if (v != v)
//                {
//                    sp[0] = 0;
//                    sp[1] = 0;
//                    sp[2] = 255;
//                }
//
//                sp += 3;
//            }
//        }
//    }
//
//    int tw = m.w < 10 ? 32 : m.w < 20 ? 16 : m.w < 40 ? 8 : m.w < 80 ? 4 : m.w < 160 ? 2 : 1;
//    int th = (m.c - 1) / tw + 1;
//
//    cv::Mat show_map(m.h * th, m.w * tw, CV_8UC3);
//    show_map = cv::Scalar(127);
//
//    // tile
//    for (int i=0; i<m.c; i++)
//    {
//        int ty = i / tw;
//        int tx = i % tw;
//
//        normed_feats[i].copyTo(show_map(cv::Rect(tx * m.w, ty * m.h, m.w, m.h)));
//    }
//
//    cv::resize(show_map, show_map, cv::Size(0,0), 2, 2, cv::INTER_NEAREST);
//    cv::imshow(title, show_map);
//}

} // namespace bstutils

#endif // COMMON_UTILS_H