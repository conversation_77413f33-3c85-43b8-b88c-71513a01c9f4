/**  
 * All rights Reserved, Designed By MI
 * @projectName bstFaceUnlock
 * @title     ModuleQualityAssessor   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 上午1:33  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//

#ifndef BSTFACEUNLOCK_MODULEQUALITYASSESSOR_H
#define BSTFACEUNLOCK_MODULEQUALITYASSESSOR_H

#include "CommonDef.h"
#include "CommonUtils.h"

typedef struct QualityOutput {
    float blurriness; // 值越大越模糊 0～1
    float face_brightness; // 值越大越亮
    float bg_brightness;
    float variance_score;
} QualityOutput;

class ModuleQualityAssessorParam {
public:
    bool check_light;
    int  low_light;
    int  high_light;

    bool  check_clarity;
    float clarity_thd;

    ModuleQualityAssessorParam() {
        // check_size = true;
        // min_size   = 40;
        // max_size   = 2048;

        check_light = true;
        low_light   = 40;
        high_light  = 180;

        check_clarity = true;
        clarity_thd   = 0.5f;
    }
};



float GetGrayVariance(unsigned int width, unsigned int height, unsigned char* img);

void CvtRGBToGray(unsigned char* rgbImg, int width, int height, unsigned char* grayImg);

class ModuleQualityAssessor {
public:
    virtual ~ModuleQualityAssessor();

    int  init();
    int  release();
    bool check(unsigned char* img, int w, int h, int c, const BBox& face, bool check_bg_light,bool check_clarity, QualityOutput& quality_output);


    float getQualityScore(QualityOutput quality_output, LmkInfo info);

private:
    float getBrightness(unsigned char* gray_face, int w, int h);

private:
    ModuleQualityAssessorParam param_;
    int                        net_in_w_;
    int                        net_in_h_;
    int                        net_in_c_;
};

#endif //BSTFACEUNLOCK_MODULEQUALITYASSESSOR_H
