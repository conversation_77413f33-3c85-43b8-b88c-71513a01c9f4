#ifndef __MODULE_DETECT_H__
#define __MODULE_DETECT_H__

#include <stdio.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
#include "BSTFaceUnlockConfig.h"

bool judge_border(const BBox& box, int width, int height, const FilterConfig& cfg, int& ret);
bool outside_border(const BBox& box, int width, int height, const FilterConfig& cfg);
struct ModuleDetectParam {
    float iou_thd;
    float conf_enroll_thd;
    float conf_auth_thd;
    int   max_scale_num;

    int   net_input_size;

    ModuleDetectParam()
        : iou_thd(0.65f), conf_enroll_thd(0.4f),conf_auth_thd(0.25f) , max_scale_num(4), net_input_size(192) {
    }
};

class ModuleDetect {
public:
    int  init();
    bool perfom(const unsigned char* const img, int w, int h, int c, std::vector<BBox>& objects, bool isEnroll, float min_face);


    int release();

private:

    void decode(const ncnn::Mat& data, const int* const anchor, std::vector<Object>& prebox, int stride,float conf_thd);
    bool preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* const dst, int out_w, int out_h, std::vector<float>& pad_info);

    ModuleDetectParam m_param;
    ncnn::Net         m_net;

    ///[4, 5, 6, 8, 10, 12], [15, 19, 23, 30, 39, 52], [72, 97, 123, 164, 209, 297]
    float             scale[3]     = {1 / 255.f, 1 / 255.f, 1 / 255.f};
    int               m_anchor0[6] = {4, 5, 6, 8, 10, 12};
    int               m_anchor1[6] = {15, 19, 23, 30, 39, 52};
    int               m_anchor2[6]   = {72, 97, 123, 164, 209, 297};
};

#endif // ifndef __MODULE_DETECT_H__