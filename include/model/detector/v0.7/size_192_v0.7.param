7767517
137 159
Input                    in0                      0 1 in0
Convolution              convrelu_0               1 1 in0 1 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 9=1
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_88                1 1 3 4 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_3                   1 1 4 5 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_0                    2 1 5 2 6 0=0
ReLU                     relu_53                  1 1 6 7
Split                    splitncnn_1              1 2 7 8 9
ConvolutionDepthWise     convdw_89                1 1 9 10 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_4                   1 1 10 11 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_1                    2 1 11 8 12 0=0
ReLU                     relu_54                  1 1 12 13
Split                    splitncnn_2              1 2 13 14 15
ConvolutionDepthWise     convdw_90                1 1 15 16 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Convolution              conv_5                   1 1 16 17 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
Pooling                  maxpool2d_45             1 1 14 18 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_6                   1 1 18 19 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
BinaryOp                 add_2                    2 1 17 19 20 0=0
ReLU                     relu_55                  1 1 20 21
Split                    splitncnn_3              1 2 21 22 23
ConvolutionDepthWise     convdw_91                1 1 23 24 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_7                   1 1 24 25 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_3                    2 1 25 22 26 0=0
ReLU                     relu_56                  1 1 26 27
Split                    splitncnn_4              1 2 27 28 29
ConvolutionDepthWise     convdw_92                1 1 29 30 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_8                   1 1 30 31 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_4                    2 1 31 28 32 0=0
ReLU                     relu_57                  1 1 32 33
Split                    splitncnn_5              1 2 33 34 35
ConvolutionDepthWise     convdw_93                1 1 35 36 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              conv_9                   1 1 36 37 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
Pooling                  maxpool2d_46             1 1 34 38 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_10                  1 1 38 39 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_5                    2 1 37 39 40 0=0
ReLU                     relu_58                  1 1 40 41
Split                    splitncnn_6              1 2 41 42 43
ConvolutionDepthWise     convdw_94                1 1 43 44 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_11                  1 1 44 45 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_6                    2 1 45 42 46 0=0
ReLU                     relu_59                  1 1 46 47
Split                    splitncnn_7              1 3 47 48 49 50
ConvolutionDepthWise     convdw_95                1 1 50 51 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              convrelu_1               1 1 51 52 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1280 9=1
Convolution              conv_13                  1 1 52 53 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_14                  1 1 53 54 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_47             1 1 49 55 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_15                  1 1 55 56 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2880
BinaryOp                 add_7                    2 1 54 56 57 0=0
ReLU                     relu_61                  1 1 57 58
Split                    splitncnn_8              1 2 58 59 60
ConvolutionDepthWise     convdw_96                1 1 60 61 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_2               1 1 61 62 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_17                  1 1 62 63 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_18                  1 1 63 64 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_8                    2 1 64 59 65 0=0
ReLU                     relu_63                  1 1 65 66
Split                    splitncnn_9              1 2 66 67 68
ConvolutionDepthWise     convdw_97                1 1 68 69 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_3               1 1 69 70 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_20                  1 1 70 71 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_21                  1 1 71 72 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_9                    2 1 72 67 73 0=0
ReLU                     relu_65                  1 1 73 74
Split                    splitncnn_10             1 3 74 75 76 77
ConvolutionDepthWise     convdw_98                1 1 77 78 0=72 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 7=72
Convolution              convrelu_4               1 1 78 79 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_23                  1 1 79 80 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_24                  1 1 80 81 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_48             1 1 76 82 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_25                  1 1 82 83 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5184
BinaryOp                 add_10                   2 1 81 83 84 0=0
ReLU                     relu_67                  1 1 84 85
Split                    splitncnn_11             1 2 85 86 87
ConvolutionDepthWise     convdw_99                1 1 87 88 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_5               1 1 88 89 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_27                  1 1 89 90 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_28                  1 1 90 91 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_11                   2 1 91 86 92 0=0
ReLU                     relu_69                  1 1 92 93
Split                    splitncnn_12             1 2 93 94 95
ConvolutionDepthWise     convdw_100               1 1 95 96 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_6               1 1 96 97 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_30                  1 1 97 98 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_31                  1 1 98 99 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_12                   2 1 99 94 100 0=0
ReLU                     relu_71                  1 1 100 101
Convolution              conv_32                  1 1 101 102 0=36 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2592
Swish                    silu_0                   1 1 102 103
Split                    splitncnn_13             1 2 103 104 105
Pooling                  maxpool2d_49             1 1 105 106 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_14             1 2 106 107 108
Pooling                  maxpool2d_50             1 1 108 109 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_15             1 2 109 110 111
Pooling                  maxpool2d_51             1 1 111 112 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Concat                   cat_0                    4 1 104 107 110 112 113 0=0
Convolution              conv_33                  1 1 113 114 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6912
Swish                    silu_1                   1 1 114 115
Split                    splitncnn_16             1 2 115 116 117
Convolution              convrelu_7               1 1 117 118 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Interp                   upsample_86              1 1 118 119 0=2 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_1                    2 1 119 75 120 0=0
ConvolutionDepthWise     convdw_101               1 1 120 121 0=120 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1080 7=120
Swish                    silu_74                  1 1 121 122
Convolution              conv_35                  1 1 122 123 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5760
Swish                    silu_75                  1 1 123 124
Split                    splitncnn_17             1 2 124 125 126
Convolution              convrelu_8               1 1 126 127 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Interp                   upsample_87              1 1 127 128 0=2 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_2                    2 1 128 48 129 0=0
ConvolutionDepthWise     convdw_102               1 1 129 130 0=88 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=792 7=88
Swish                    silu_76                  1 1 130 131
Convolution              conv_37                  1 1 131 132 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4224
Swish                    silu_77                  1 1 132 133
Split                    splitncnn_18             1 2 133 134 135
ConvolutionDepthWise     convdw_103               1 1 135 136 0=48 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=432 7=48
Swish                    silu_78                  1 1 136 137
Convolution              conv_38                  1 1 137 138 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Swish                    silu_79                  1 1 138 139
BinaryOp                 add_13                   2 1 139 125 140 0=0
ConvolutionDepthWise     convdw_104               1 1 140 141 0=48 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=432 7=48
Swish                    silu_80                  1 1 141 142
Convolution              conv_39                  1 1 142 143 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Swish                    silu_81                  1 1 143 144
Split                    splitncnn_19             1 2 144 145 146
ConvolutionDepthWise     convdw_105               1 1 146 147 0=48 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=432 7=48
Swish                    silu_82                  1 1 147 148
Convolution              conv_40                  1 1 148 149 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Swish                    silu_83                  1 1 149 150
BinaryOp                 add_14                   2 1 150 116 151 0=0
ConvolutionDepthWise     convdw_106               1 1 151 152 0=48 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=432 7=48
Swish                    silu_84                  1 1 152 153
Convolution              conv_41                  1 1 153 154 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Swish                    silu_85                  1 1 154 155
Convolution              conv_42                  1 1 134 out0 0=18 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=864
Convolution              conv_43                  1 1 145 out1 0=18 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=864
Convolution              conv_44                  1 1 155 out2 0=18 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=864
