#ifndef NCNN_INCLUDE_GUARD_size_192_v0_7_id_h
#define NCNN_INCLUDE_GUARD_size_192_v0_7_id_h
namespace size_192_v0_7_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_convrelu_0 = 1;
const int BLOB_1 = 1;
const int LAYER_splitncnn_0 = 2;
const int BLOB_2 = 2;
const int BLOB_3 = 3;
const int LAYER_convdw_88 = 3;
const int BLOB_4 = 4;
const int LAYER_conv_3 = 4;
const int BLOB_5 = 5;
const int LAYER_add_0 = 5;
const int BLOB_6 = 6;
const int LAYER_relu_53 = 6;
const int BLOB_7 = 7;
const int LAYER_splitncnn_1 = 7;
const int BLOB_8 = 8;
const int BLOB_9 = 9;
const int LAYER_convdw_89 = 8;
const int BLOB_10 = 10;
const int LAYER_conv_4 = 9;
const int BLOB_11 = 11;
const int LAYER_add_1 = 10;
const int BLOB_12 = 12;
const int LAYER_relu_54 = 11;
const int BLOB_13 = 13;
const int LAYER_splitncnn_2 = 12;
const int BLOB_14 = 14;
const int BLOB_15 = 15;
const int LAYER_convdw_90 = 13;
const int BLOB_16 = 16;
const int LAYER_conv_5 = 14;
const int BLOB_17 = 17;
const int LAYER_maxpool2d_45 = 15;
const int BLOB_18 = 18;
const int LAYER_conv_6 = 16;
const int BLOB_19 = 19;
const int LAYER_add_2 = 17;
const int BLOB_20 = 20;
const int LAYER_relu_55 = 18;
const int BLOB_21 = 21;
const int LAYER_splitncnn_3 = 19;
const int BLOB_22 = 22;
const int BLOB_23 = 23;
const int LAYER_convdw_91 = 20;
const int BLOB_24 = 24;
const int LAYER_conv_7 = 21;
const int BLOB_25 = 25;
const int LAYER_add_3 = 22;
const int BLOB_26 = 26;
const int LAYER_relu_56 = 23;
const int BLOB_27 = 27;
const int LAYER_splitncnn_4 = 24;
const int BLOB_28 = 28;
const int BLOB_29 = 29;
const int LAYER_convdw_92 = 25;
const int BLOB_30 = 30;
const int LAYER_conv_8 = 26;
const int BLOB_31 = 31;
const int LAYER_add_4 = 27;
const int BLOB_32 = 32;
const int LAYER_relu_57 = 28;
const int BLOB_33 = 33;
const int LAYER_splitncnn_5 = 29;
const int BLOB_34 = 34;
const int BLOB_35 = 35;
const int LAYER_convdw_93 = 30;
const int BLOB_36 = 36;
const int LAYER_conv_9 = 31;
const int BLOB_37 = 37;
const int LAYER_maxpool2d_46 = 32;
const int BLOB_38 = 38;
const int LAYER_conv_10 = 33;
const int BLOB_39 = 39;
const int LAYER_add_5 = 34;
const int BLOB_40 = 40;
const int LAYER_relu_58 = 35;
const int BLOB_41 = 41;
const int LAYER_splitncnn_6 = 36;
const int BLOB_42 = 42;
const int BLOB_43 = 43;
const int LAYER_convdw_94 = 37;
const int BLOB_44 = 44;
const int LAYER_conv_11 = 38;
const int BLOB_45 = 45;
const int LAYER_add_6 = 39;
const int BLOB_46 = 46;
const int LAYER_relu_59 = 40;
const int BLOB_47 = 47;
const int LAYER_splitncnn_7 = 41;
const int BLOB_48 = 48;
const int BLOB_49 = 49;
const int BLOB_50 = 50;
const int LAYER_convdw_95 = 42;
const int BLOB_51 = 51;
const int LAYER_convrelu_1 = 43;
const int BLOB_52 = 52;
const int LAYER_conv_13 = 44;
const int BLOB_53 = 53;
const int LAYER_conv_14 = 45;
const int BLOB_54 = 54;
const int LAYER_maxpool2d_47 = 46;
const int BLOB_55 = 55;
const int LAYER_conv_15 = 47;
const int BLOB_56 = 56;
const int LAYER_add_7 = 48;
const int BLOB_57 = 57;
const int LAYER_relu_61 = 49;
const int BLOB_58 = 58;
const int LAYER_splitncnn_8 = 50;
const int BLOB_59 = 59;
const int BLOB_60 = 60;
const int LAYER_convdw_96 = 51;
const int BLOB_61 = 61;
const int LAYER_convrelu_2 = 52;
const int BLOB_62 = 62;
const int LAYER_conv_17 = 53;
const int BLOB_63 = 63;
const int LAYER_conv_18 = 54;
const int BLOB_64 = 64;
const int LAYER_add_8 = 55;
const int BLOB_65 = 65;
const int LAYER_relu_63 = 56;
const int BLOB_66 = 66;
const int LAYER_splitncnn_9 = 57;
const int BLOB_67 = 67;
const int BLOB_68 = 68;
const int LAYER_convdw_97 = 58;
const int BLOB_69 = 69;
const int LAYER_convrelu_3 = 59;
const int BLOB_70 = 70;
const int LAYER_conv_20 = 60;
const int BLOB_71 = 71;
const int LAYER_conv_21 = 61;
const int BLOB_72 = 72;
const int LAYER_add_9 = 62;
const int BLOB_73 = 73;
const int LAYER_relu_65 = 63;
const int BLOB_74 = 74;
const int LAYER_splitncnn_10 = 64;
const int BLOB_75 = 75;
const int BLOB_76 = 76;
const int BLOB_77 = 77;
const int LAYER_convdw_98 = 65;
const int BLOB_78 = 78;
const int LAYER_convrelu_4 = 66;
const int BLOB_79 = 79;
const int LAYER_conv_23 = 67;
const int BLOB_80 = 80;
const int LAYER_conv_24 = 68;
const int BLOB_81 = 81;
const int LAYER_maxpool2d_48 = 69;
const int BLOB_82 = 82;
const int LAYER_conv_25 = 70;
const int BLOB_83 = 83;
const int LAYER_add_10 = 71;
const int BLOB_84 = 84;
const int LAYER_relu_67 = 72;
const int BLOB_85 = 85;
const int LAYER_splitncnn_11 = 73;
const int BLOB_86 = 86;
const int BLOB_87 = 87;
const int LAYER_convdw_99 = 74;
const int BLOB_88 = 88;
const int LAYER_convrelu_5 = 75;
const int BLOB_89 = 89;
const int LAYER_conv_27 = 76;
const int BLOB_90 = 90;
const int LAYER_conv_28 = 77;
const int BLOB_91 = 91;
const int LAYER_add_11 = 78;
const int BLOB_92 = 92;
const int LAYER_relu_69 = 79;
const int BLOB_93 = 93;
const int LAYER_splitncnn_12 = 80;
const int BLOB_94 = 94;
const int BLOB_95 = 95;
const int LAYER_convdw_100 = 81;
const int BLOB_96 = 96;
const int LAYER_convrelu_6 = 82;
const int BLOB_97 = 97;
const int LAYER_conv_30 = 83;
const int BLOB_98 = 98;
const int LAYER_conv_31 = 84;
const int BLOB_99 = 99;
const int LAYER_add_12 = 85;
const int BLOB_100 = 100;
const int LAYER_relu_71 = 86;
const int BLOB_101 = 101;
const int LAYER_conv_32 = 87;
const int BLOB_102 = 102;
const int LAYER_silu_0 = 88;
const int BLOB_103 = 103;
const int LAYER_splitncnn_13 = 89;
const int BLOB_104 = 104;
const int BLOB_105 = 105;
const int LAYER_maxpool2d_49 = 90;
const int BLOB_106 = 106;
const int LAYER_splitncnn_14 = 91;
const int BLOB_107 = 107;
const int BLOB_108 = 108;
const int LAYER_maxpool2d_50 = 92;
const int BLOB_109 = 109;
const int LAYER_splitncnn_15 = 93;
const int BLOB_110 = 110;
const int BLOB_111 = 111;
const int LAYER_maxpool2d_51 = 94;
const int BLOB_112 = 112;
const int LAYER_cat_0 = 95;
const int BLOB_113 = 113;
const int LAYER_conv_33 = 96;
const int BLOB_114 = 114;
const int LAYER_silu_1 = 97;
const int BLOB_115 = 115;
const int LAYER_splitncnn_16 = 98;
const int BLOB_116 = 116;
const int BLOB_117 = 117;
const int LAYER_convrelu_7 = 99;
const int BLOB_118 = 118;
const int LAYER_upsample_86 = 100;
const int BLOB_119 = 119;
const int LAYER_cat_1 = 101;
const int BLOB_120 = 120;
const int LAYER_convdw_101 = 102;
const int BLOB_121 = 121;
const int LAYER_silu_74 = 103;
const int BLOB_122 = 122;
const int LAYER_conv_35 = 104;
const int BLOB_123 = 123;
const int LAYER_silu_75 = 105;
const int BLOB_124 = 124;
const int LAYER_splitncnn_17 = 106;
const int BLOB_125 = 125;
const int BLOB_126 = 126;
const int LAYER_convrelu_8 = 107;
const int BLOB_127 = 127;
const int LAYER_upsample_87 = 108;
const int BLOB_128 = 128;
const int LAYER_cat_2 = 109;
const int BLOB_129 = 129;
const int LAYER_convdw_102 = 110;
const int BLOB_130 = 130;
const int LAYER_silu_76 = 111;
const int BLOB_131 = 131;
const int LAYER_conv_37 = 112;
const int BLOB_132 = 132;
const int LAYER_silu_77 = 113;
const int BLOB_133 = 133;
const int LAYER_splitncnn_18 = 114;
const int BLOB_134 = 134;
const int BLOB_135 = 135;
const int LAYER_convdw_103 = 115;
const int BLOB_136 = 136;
const int LAYER_silu_78 = 116;
const int BLOB_137 = 137;
const int LAYER_conv_38 = 117;
const int BLOB_138 = 138;
const int LAYER_silu_79 = 118;
const int BLOB_139 = 139;
const int LAYER_add_13 = 119;
const int BLOB_140 = 140;
const int LAYER_convdw_104 = 120;
const int BLOB_141 = 141;
const int LAYER_silu_80 = 121;
const int BLOB_142 = 142;
const int LAYER_conv_39 = 122;
const int BLOB_143 = 143;
const int LAYER_silu_81 = 123;
const int BLOB_144 = 144;
const int LAYER_splitncnn_19 = 124;
const int BLOB_145 = 145;
const int BLOB_146 = 146;
const int LAYER_convdw_105 = 125;
const int BLOB_147 = 147;
const int LAYER_silu_82 = 126;
const int BLOB_148 = 148;
const int LAYER_conv_40 = 127;
const int BLOB_149 = 149;
const int LAYER_silu_83 = 128;
const int BLOB_150 = 150;
const int LAYER_add_14 = 129;
const int BLOB_151 = 151;
const int LAYER_convdw_106 = 130;
const int BLOB_152 = 152;
const int LAYER_silu_84 = 131;
const int BLOB_153 = 153;
const int LAYER_conv_41 = 132;
const int BLOB_154 = 154;
const int LAYER_silu_85 = 133;
const int BLOB_155 = 155;
const int LAYER_conv_42 = 134;
const int BLOB_out0 = 156;
const int LAYER_conv_43 = 135;
const int BLOB_out1 = 157;
const int LAYER_conv_44 = 136;
const int BLOB_out2 = 158;
} // namespace size_192_v0_7_param_id
#endif // NCNN_INCLUDE_GUARD_size_192_v0_7_id_h
