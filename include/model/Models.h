#ifndef BSTFACEUNLOCK_MODELS_H
#define BSTFACEUNLOCK_MODELS_H

struct Models {
    int model_num;
    Models();
    const unsigned char * detect_param_bin;
    const unsigned char * detect_bin;
    const unsigned char * paper_screen_detect_param_bin;
    const unsigned char * paper_screen_detect_bin;
    const unsigned char * pipfacelmk_param_bin;
    const unsigned char * pipfacelmk_bin;
    const unsigned char * normal_living_param_bin;
    const unsigned char * normal_living_bin;
    const unsigned char * dark_living_param_bin;
    const unsigned char * dark_living_bin;
    const unsigned char * big_angel_living_param_bin;
    const unsigned char * big_angel_living_bin;
    const unsigned char * blink_param_bin;
    const unsigned char * blink_bin;
    const unsigned char * nose_param_bin;
    const unsigned char * nose_bin;
    const unsigned char * recognizer_param_bin;
    const unsigned char * recognizer_bin;
};

#endif //BSTFACEUNLOCK_MODELS_H
