7767517
611 695
Input                    in0                      0 1 in0
Convolution              conv_58                  1 1 in0 1 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1728
PReLU                    prelu_119                1 1 1 2 0=64
Split                    splitncnn_0              1 2 2 3 4
Convolution              conv_59                  1 1 4 5 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_120                1 1 5 6 0=1
Split                    splitncnn_1              1 2 6 7 8
ConvolutionDepthWise     convdw_383               1 1 8 9 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_121                1 1 9 10 0=1
Concat                   cat_0                    2 1 7 10 11 0=0
ConvolutionDepthWise     convdw_384               1 1 11 12 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_122                1 1 12 13 0=128
Convolution              conv_60                  1 1 13 14 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_2              1 2 14 15 16
Pooling                  gap_0                    1 1 16 17 0=1 4=1
Reshape                  reshape_237              1 1 17 18 0=1 1=1 2=-1
Squeeze                  squeeze_267              1 1 18 19 -23303=1,-1
Permute                  transpose_296            1 1 19 20 0=1
Convolution1D            conv1d_29                1 1 20 21 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_297            1 1 21 22 0=1
ExpandDims               unsqueeze_354            1 1 22 23 -23303=1,-1
Sigmoid                  sigmoid_208              1 1 23 24
BinaryOp                 mul_0                    2 1 15 24 25 0=2
BinaryOp                 add_1                    2 1 3 25 26 0=0
Split                    splitncnn_3              1 2 26 27 28
Convolution              conv_61                  1 1 28 29 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_123                1 1 29 30 0=1
Split                    splitncnn_4              1 2 30 31 32
ConvolutionDepthWise     convdw_385               1 1 32 33 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_124                1 1 33 34 0=1
Concat                   cat_1                    2 1 31 34 35 0=0
ConvolutionDepthWise     convdw_386               1 1 35 36 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_125                1 1 36 37 0=128
Convolution              conv_62                  1 1 37 38 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_5              1 2 38 39 40
Pooling                  gap_1                    1 1 40 41 0=1 4=1
Reshape                  reshape_238              1 1 41 42 0=1 1=1 2=-1
Squeeze                  squeeze_268              1 1 42 43 -23303=1,-1
Permute                  transpose_298            1 1 43 44 0=1
Convolution1D            conv1d_30                1 1 44 45 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_299            1 1 45 46 0=1
ExpandDims               unsqueeze_355            1 1 46 47 -23303=1,-1
Sigmoid                  sigmoid_209              1 1 47 48
BinaryOp                 mul_2                    2 1 39 48 49 0=2
BinaryOp                 add_3                    2 1 27 49 50 0=0
Convolution              conv_63                  1 1 50 51 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_126                1 1 51 52 0=1
Split                    splitncnn_6              1 2 52 53 54
ConvolutionDepthWise     convdw_387               1 1 54 55 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_127                1 1 55 56 0=1
Concat                   cat_2                    2 1 53 56 57 0=0
ConvolutionDepthWise     convdw_388               1 1 57 58 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1152 7=128
PReLU                    prelu_128                1 1 58 59 0=128
Convolution              conv_64                  1 1 59 60 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_7              1 2 60 61 62
Pooling                  gap_2                    1 1 62 63 0=1 4=1
Reshape                  reshape_239              1 1 63 64 0=1 1=1 2=-1
Squeeze                  squeeze_269              1 1 64 65 -23303=1,-1
Permute                  transpose_300            1 1 65 66 0=1
Convolution1D            conv1d_31                1 1 66 67 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_301            1 1 67 68 0=1
ExpandDims               unsqueeze_356            1 1 68 69 -23303=1,-1
Sigmoid                  sigmoid_210              1 1 69 70
BinaryOp                 mul_4                    2 1 61 70 71 0=2
Split                    splitncnn_8              1 2 71 72 73
Convolution              conv_65                  1 1 73 74 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_129                1 1 74 75 0=1
Split                    splitncnn_9              1 2 75 76 77
ConvolutionDepthWise     convdw_389               1 1 77 78 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_130                1 1 78 79 0=1
Concat                   cat_3                    2 1 76 79 80 0=0
ConvolutionDepthWise     convdw_390               1 1 80 81 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_131                1 1 81 82 0=128
Convolution              conv_66                  1 1 82 83 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_10             1 2 83 84 85
Pooling                  gap_3                    1 1 85 86 0=1 4=1
Reshape                  reshape_240              1 1 86 87 0=1 1=1 2=-1
Squeeze                  squeeze_270              1 1 87 88 -23303=1,-1
Permute                  transpose_302            1 1 88 89 0=1
Convolution1D            conv1d_32                1 1 89 90 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_303            1 1 90 91 0=1
ExpandDims               unsqueeze_357            1 1 91 92 -23303=1,-1
Sigmoid                  sigmoid_211              1 1 92 93
BinaryOp                 mul_5                    2 1 84 93 94 0=2
BinaryOp                 add_6                    2 1 72 94 95 0=0
Split                    splitncnn_11             1 2 95 96 97
Convolution              conv_67                  1 1 97 98 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_132                1 1 98 99 0=1
Split                    splitncnn_12             1 2 99 100 101
ConvolutionDepthWise     convdw_391               1 1 101 102 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_133                1 1 102 103 0=1
Concat                   cat_4                    2 1 100 103 104 0=0
ConvolutionDepthWise     convdw_392               1 1 104 105 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_134                1 1 105 106 0=128
Convolution              conv_68                  1 1 106 107 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_13             1 2 107 108 109
Pooling                  gap_4                    1 1 109 110 0=1 4=1
Reshape                  reshape_241              1 1 110 111 0=1 1=1 2=-1
Squeeze                  squeeze_271              1 1 111 112 -23303=1,-1
Permute                  transpose_304            1 1 112 113 0=1
Convolution1D            conv1d_33                1 1 113 114 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_305            1 1 114 115 0=1
ExpandDims               unsqueeze_358            1 1 115 116 -23303=1,-1
Sigmoid                  sigmoid_212              1 1 116 117
BinaryOp                 mul_7                    2 1 108 117 118 0=2
BinaryOp                 add_8                    2 1 96 118 119 0=0
Split                    splitncnn_14             1 2 119 120 121
Convolution              conv_69                  1 1 121 122 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_135                1 1 122 123 0=1
Split                    splitncnn_15             1 2 123 124 125
ConvolutionDepthWise     convdw_393               1 1 125 126 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_136                1 1 126 127 0=1
Concat                   cat_5                    2 1 124 127 128 0=0
ConvolutionDepthWise     convdw_394               1 1 128 129 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_137                1 1 129 130 0=128
Convolution              conv_70                  1 1 130 131 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_16             1 2 131 132 133
Pooling                  gap_5                    1 1 133 134 0=1 4=1
Reshape                  reshape_242              1 1 134 135 0=1 1=1 2=-1
Squeeze                  squeeze_272              1 1 135 136 -23303=1,-1
Permute                  transpose_306            1 1 136 137 0=1
Convolution1D            conv1d_34                1 1 137 138 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_307            1 1 138 139 0=1
ExpandDims               unsqueeze_359            1 1 139 140 -23303=1,-1
Sigmoid                  sigmoid_213              1 1 140 141
BinaryOp                 mul_9                    2 1 132 141 142 0=2
BinaryOp                 add_10                   2 1 120 142 143 0=0
Split                    splitncnn_17             1 2 143 144 145
Convolution              conv_71                  1 1 145 146 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_138                1 1 146 147 0=1
Split                    splitncnn_18             1 2 147 148 149
ConvolutionDepthWise     convdw_395               1 1 149 150 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_139                1 1 150 151 0=1
Concat                   cat_6                    2 1 148 151 152 0=0
ConvolutionDepthWise     convdw_396               1 1 152 153 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_140                1 1 153 154 0=128
Convolution              conv_72                  1 1 154 155 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_19             1 2 155 156 157
Pooling                  gap_6                    1 1 157 158 0=1 4=1
Reshape                  reshape_243              1 1 158 159 0=1 1=1 2=-1
Squeeze                  squeeze_273              1 1 159 160 -23303=1,-1
Permute                  transpose_308            1 1 160 161 0=1
Convolution1D            conv1d_35                1 1 161 162 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_309            1 1 162 163 0=1
ExpandDims               unsqueeze_360            1 1 163 164 -23303=1,-1
Sigmoid                  sigmoid_214              1 1 164 165
BinaryOp                 mul_11                   2 1 156 165 166 0=2
BinaryOp                 add_12                   2 1 144 166 167 0=0
Split                    splitncnn_20             1 2 167 168 169
Convolution              conv_73                  1 1 169 170 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_141                1 1 170 171 0=1
Split                    splitncnn_21             1 2 171 172 173
ConvolutionDepthWise     convdw_397               1 1 173 174 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_142                1 1 174 175 0=1
Concat                   cat_7                    2 1 172 175 176 0=0
ConvolutionDepthWise     convdw_398               1 1 176 177 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_143                1 1 177 178 0=128
Convolution              conv_74                  1 1 178 179 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_22             1 2 179 180 181
Pooling                  gap_7                    1 1 181 182 0=1 4=1
Reshape                  reshape_244              1 1 182 183 0=1 1=1 2=-1
Squeeze                  squeeze_274              1 1 183 184 -23303=1,-1
Permute                  transpose_310            1 1 184 185 0=1
Convolution1D            conv1d_36                1 1 185 186 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_311            1 1 186 187 0=1
ExpandDims               unsqueeze_361            1 1 187 188 -23303=1,-1
Sigmoid                  sigmoid_215              1 1 188 189
BinaryOp                 mul_13                   2 1 180 189 190 0=2
BinaryOp                 add_14                   2 1 168 190 191 0=0
Split                    splitncnn_23             1 2 191 192 193
Convolution              conv_75                  1 1 193 194 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_144                1 1 194 195 0=1
Split                    splitncnn_24             1 2 195 196 197
ConvolutionDepthWise     convdw_399               1 1 197 198 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_145                1 1 198 199 0=1
Concat                   cat_8                    2 1 196 199 200 0=0
ConvolutionDepthWise     convdw_400               1 1 200 201 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_146                1 1 201 202 0=128
Convolution              conv_76                  1 1 202 203 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_25             1 2 203 204 205
Pooling                  gap_8                    1 1 205 206 0=1 4=1
Reshape                  reshape_245              1 1 206 207 0=1 1=1 2=-1
Squeeze                  squeeze_275              1 1 207 208 -23303=1,-1
Permute                  transpose_312            1 1 208 209 0=1
Convolution1D            conv1d_37                1 1 209 210 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_313            1 1 210 211 0=1
ExpandDims               unsqueeze_362            1 1 211 212 -23303=1,-1
Sigmoid                  sigmoid_216              1 1 212 213
BinaryOp                 mul_15                   2 1 204 213 214 0=2
BinaryOp                 add_16                   2 1 192 214 215 0=0
Split                    splitncnn_26             1 2 215 216 217
Convolution              conv_77                  1 1 217 218 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_147                1 1 218 219 0=1
Split                    splitncnn_27             1 2 219 220 221
ConvolutionDepthWise     convdw_401               1 1 221 222 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_148                1 1 222 223 0=1
Concat                   cat_9                    2 1 220 223 224 0=0
ConvolutionDepthWise     convdw_402               1 1 224 225 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_149                1 1 225 226 0=128
Convolution              conv_78                  1 1 226 227 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_28             1 2 227 228 229
Pooling                  gap_9                    1 1 229 230 0=1 4=1
Reshape                  reshape_246              1 1 230 231 0=1 1=1 2=-1
Squeeze                  squeeze_276              1 1 231 232 -23303=1,-1
Permute                  transpose_314            1 1 232 233 0=1
Convolution1D            conv1d_38                1 1 233 234 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_315            1 1 234 235 0=1
ExpandDims               unsqueeze_363            1 1 235 236 -23303=1,-1
Sigmoid                  sigmoid_217              1 1 236 237
BinaryOp                 mul_17                   2 1 228 237 238 0=2
BinaryOp                 add_18                   2 1 216 238 239 0=0
Split                    splitncnn_29             1 2 239 240 241
Convolution              conv_79                  1 1 241 242 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
PReLU                    prelu_150                1 1 242 243 0=1
Split                    splitncnn_30             1 2 243 244 245
ConvolutionDepthWise     convdw_403               1 1 245 246 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_151                1 1 246 247 0=1
Concat                   cat_10                   2 1 244 247 248 0=0
ConvolutionDepthWise     convdw_404               1 1 248 249 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_152                1 1 249 250 0=128
Convolution              conv_80                  1 1 250 251 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_31             1 2 251 252 253
Pooling                  gap_10                   1 1 253 254 0=1 4=1
Reshape                  reshape_247              1 1 254 255 0=1 1=1 2=-1
Squeeze                  squeeze_277              1 1 255 256 -23303=1,-1
Permute                  transpose_316            1 1 256 257 0=1
Convolution1D            conv1d_39                1 1 257 258 0=1 1=3 2=1 3=1 4=1 5=0 6=3
Permute                  transpose_317            1 1 258 259 0=1
ExpandDims               unsqueeze_364            1 1 259 260 -23303=1,-1
Sigmoid                  sigmoid_218              1 1 260 261
BinaryOp                 mul_19                   2 1 252 261 262 0=2
BinaryOp                 add_20                   2 1 240 262 263 0=0
Convolution              conv_81                  1 1 263 264 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_153                1 1 264 265 0=1
Split                    splitncnn_32             1 2 265 266 267
ConvolutionDepthWise     convdw_405               1 1 267 268 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_154                1 1 268 269 0=1
Concat                   cat_11                   2 1 266 269 270 0=0
ConvolutionDepthWise     convdw_406               1 1 270 271 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2304 7=256
PReLU                    prelu_155                1 1 271 272 0=256
Convolution              conv_82                  1 1 272 273 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_33             1 2 273 274 275
Pooling                  gap_11                   1 1 275 276 0=1 4=1
Reshape                  reshape_248              1 1 276 277 0=1 1=1 2=-1
Squeeze                  squeeze_278              1 1 277 278 -23303=1,-1
Permute                  transpose_318            1 1 278 279 0=1
Convolution1D            conv1d_40                1 1 279 280 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_319            1 1 280 281 0=1
ExpandDims               unsqueeze_365            1 1 281 282 -23303=1,-1
Sigmoid                  sigmoid_219              1 1 282 283
BinaryOp                 mul_21                   2 1 274 283 284 0=2
Split                    splitncnn_34             1 2 284 285 286
Convolution              conv_83                  1 1 286 287 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_156                1 1 287 288 0=1
Split                    splitncnn_35             1 2 288 289 290
ConvolutionDepthWise     convdw_407               1 1 290 291 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_157                1 1 291 292 0=1
Concat                   cat_12                   2 1 289 292 293 0=0
ConvolutionDepthWise     convdw_408               1 1 293 294 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_158                1 1 294 295 0=256
Convolution              conv_84                  1 1 295 296 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_36             1 2 296 297 298
Pooling                  gap_12                   1 1 298 299 0=1 4=1
Reshape                  reshape_249              1 1 299 300 0=1 1=1 2=-1
Squeeze                  squeeze_279              1 1 300 301 -23303=1,-1
Permute                  transpose_320            1 1 301 302 0=1
Convolution1D            conv1d_41                1 1 302 303 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_321            1 1 303 304 0=1
ExpandDims               unsqueeze_366            1 1 304 305 -23303=1,-1
Sigmoid                  sigmoid_220              1 1 305 306
BinaryOp                 mul_22                   2 1 297 306 307 0=2
BinaryOp                 add_23                   2 1 285 307 308 0=0
Split                    splitncnn_37             1 2 308 309 310
Convolution              conv_85                  1 1 310 311 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_159                1 1 311 312 0=1
Split                    splitncnn_38             1 2 312 313 314
ConvolutionDepthWise     convdw_409               1 1 314 315 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_160                1 1 315 316 0=1
Concat                   cat_13                   2 1 313 316 317 0=0
ConvolutionDepthWise     convdw_410               1 1 317 318 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_161                1 1 318 319 0=256
Convolution              conv_86                  1 1 319 320 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_39             1 2 320 321 322
Pooling                  gap_13                   1 1 322 323 0=1 4=1
Reshape                  reshape_250              1 1 323 324 0=1 1=1 2=-1
Squeeze                  squeeze_280              1 1 324 325 -23303=1,-1
Permute                  transpose_322            1 1 325 326 0=1
Convolution1D            conv1d_42                1 1 326 327 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_323            1 1 327 328 0=1
ExpandDims               unsqueeze_367            1 1 328 329 -23303=1,-1
Sigmoid                  sigmoid_221              1 1 329 330
BinaryOp                 mul_24                   2 1 321 330 331 0=2
BinaryOp                 add_25                   2 1 309 331 332 0=0
Split                    splitncnn_40             1 2 332 333 334
Convolution              conv_87                  1 1 334 335 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_162                1 1 335 336 0=1
Split                    splitncnn_41             1 2 336 337 338
ConvolutionDepthWise     convdw_411               1 1 338 339 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_163                1 1 339 340 0=1
Concat                   cat_14                   2 1 337 340 341 0=0
ConvolutionDepthWise     convdw_412               1 1 341 342 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_164                1 1 342 343 0=256
Convolution              conv_88                  1 1 343 344 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_42             1 2 344 345 346
Pooling                  gap_14                   1 1 346 347 0=1 4=1
Reshape                  reshape_251              1 1 347 348 0=1 1=1 2=-1
Squeeze                  squeeze_281              1 1 348 349 -23303=1,-1
Permute                  transpose_324            1 1 349 350 0=1
Convolution1D            conv1d_43                1 1 350 351 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_325            1 1 351 352 0=1
ExpandDims               unsqueeze_368            1 1 352 353 -23303=1,-1
Sigmoid                  sigmoid_222              1 1 353 354
BinaryOp                 mul_26                   2 1 345 354 355 0=2
BinaryOp                 add_27                   2 1 333 355 356 0=0
Split                    splitncnn_43             1 2 356 357 358
Convolution              conv_89                  1 1 358 359 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_165                1 1 359 360 0=1
Split                    splitncnn_44             1 2 360 361 362
ConvolutionDepthWise     convdw_413               1 1 362 363 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_166                1 1 363 364 0=1
Concat                   cat_15                   2 1 361 364 365 0=0
ConvolutionDepthWise     convdw_414               1 1 365 366 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_167                1 1 366 367 0=256
Convolution              conv_90                  1 1 367 368 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_45             1 2 368 369 370
Pooling                  gap_15                   1 1 370 371 0=1 4=1
Reshape                  reshape_252              1 1 371 372 0=1 1=1 2=-1
Squeeze                  squeeze_282              1 1 372 373 -23303=1,-1
Permute                  transpose_326            1 1 373 374 0=1
Convolution1D            conv1d_44                1 1 374 375 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_327            1 1 375 376 0=1
ExpandDims               unsqueeze_369            1 1 376 377 -23303=1,-1
Sigmoid                  sigmoid_223              1 1 377 378
BinaryOp                 mul_28                   2 1 369 378 379 0=2
BinaryOp                 add_29                   2 1 357 379 380 0=0
Split                    splitncnn_46             1 2 380 381 382
Convolution              conv_91                  1 1 382 383 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_168                1 1 383 384 0=1
Split                    splitncnn_47             1 2 384 385 386
ConvolutionDepthWise     convdw_415               1 1 386 387 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_169                1 1 387 388 0=1
Concat                   cat_16                   2 1 385 388 389 0=0
ConvolutionDepthWise     convdw_416               1 1 389 390 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_170                1 1 390 391 0=256
Convolution              conv_92                  1 1 391 392 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_48             1 2 392 393 394
Pooling                  gap_16                   1 1 394 395 0=1 4=1
Reshape                  reshape_253              1 1 395 396 0=1 1=1 2=-1
Squeeze                  squeeze_283              1 1 396 397 -23303=1,-1
Permute                  transpose_328            1 1 397 398 0=1
Convolution1D            conv1d_45                1 1 398 399 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_329            1 1 399 400 0=1
ExpandDims               unsqueeze_370            1 1 400 401 -23303=1,-1
Sigmoid                  sigmoid_224              1 1 401 402
BinaryOp                 mul_30                   2 1 393 402 403 0=2
BinaryOp                 add_31                   2 1 381 403 404 0=0
Split                    splitncnn_49             1 2 404 405 406
Convolution              conv_93                  1 1 406 407 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_171                1 1 407 408 0=1
Split                    splitncnn_50             1 2 408 409 410
ConvolutionDepthWise     convdw_417               1 1 410 411 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_172                1 1 411 412 0=1
Concat                   cat_17                   2 1 409 412 413 0=0
ConvolutionDepthWise     convdw_418               1 1 413 414 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_173                1 1 414 415 0=256
Convolution              conv_94                  1 1 415 416 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_51             1 2 416 417 418
Pooling                  gap_17                   1 1 418 419 0=1 4=1
Reshape                  reshape_254              1 1 419 420 0=1 1=1 2=-1
Squeeze                  squeeze_284              1 1 420 421 -23303=1,-1
Permute                  transpose_330            1 1 421 422 0=1
Convolution1D            conv1d_46                1 1 422 423 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_331            1 1 423 424 0=1
ExpandDims               unsqueeze_371            1 1 424 425 -23303=1,-1
Sigmoid                  sigmoid_225              1 1 425 426
BinaryOp                 mul_32                   2 1 417 426 427 0=2
BinaryOp                 add_33                   2 1 405 427 428 0=0
Split                    splitncnn_52             1 2 428 429 430
Convolution              conv_95                  1 1 430 431 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_174                1 1 431 432 0=1
Split                    splitncnn_53             1 2 432 433 434
ConvolutionDepthWise     convdw_419               1 1 434 435 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_175                1 1 435 436 0=1
Concat                   cat_18                   2 1 433 436 437 0=0
ConvolutionDepthWise     convdw_420               1 1 437 438 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_176                1 1 438 439 0=256
Convolution              conv_96                  1 1 439 440 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_54             1 2 440 441 442
Pooling                  gap_18                   1 1 442 443 0=1 4=1
Reshape                  reshape_255              1 1 443 444 0=1 1=1 2=-1
Squeeze                  squeeze_285              1 1 444 445 -23303=1,-1
Permute                  transpose_332            1 1 445 446 0=1
Convolution1D            conv1d_47                1 1 446 447 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_333            1 1 447 448 0=1
ExpandDims               unsqueeze_372            1 1 448 449 -23303=1,-1
Sigmoid                  sigmoid_226              1 1 449 450
BinaryOp                 mul_34                   2 1 441 450 451 0=2
BinaryOp                 add_35                   2 1 429 451 452 0=0
Split                    splitncnn_55             1 2 452 453 454
Convolution              conv_97                  1 1 454 455 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_177                1 1 455 456 0=1
Split                    splitncnn_56             1 2 456 457 458
ConvolutionDepthWise     convdw_421               1 1 458 459 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_178                1 1 459 460 0=1
Concat                   cat_19                   2 1 457 460 461 0=0
ConvolutionDepthWise     convdw_422               1 1 461 462 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_179                1 1 462 463 0=256
Convolution              conv_98                  1 1 463 464 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_57             1 2 464 465 466
Pooling                  gap_19                   1 1 466 467 0=1 4=1
Reshape                  reshape_256              1 1 467 468 0=1 1=1 2=-1
Squeeze                  squeeze_286              1 1 468 469 -23303=1,-1
Permute                  transpose_334            1 1 469 470 0=1
Convolution1D            conv1d_48                1 1 470 471 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_335            1 1 471 472 0=1
ExpandDims               unsqueeze_373            1 1 472 473 -23303=1,-1
Sigmoid                  sigmoid_227              1 1 473 474
BinaryOp                 mul_36                   2 1 465 474 475 0=2
BinaryOp                 add_37                   2 1 453 475 476 0=0
Split                    splitncnn_58             1 2 476 477 478
Convolution              conv_99                  1 1 478 479 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_180                1 1 479 480 0=1
Split                    splitncnn_59             1 2 480 481 482
ConvolutionDepthWise     convdw_423               1 1 482 483 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_181                1 1 483 484 0=1
Concat                   cat_20                   2 1 481 484 485 0=0
ConvolutionDepthWise     convdw_424               1 1 485 486 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_182                1 1 486 487 0=256
Convolution              conv_100                 1 1 487 488 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_60             1 2 488 489 490
Pooling                  gap_20                   1 1 490 491 0=1 4=1
Reshape                  reshape_257              1 1 491 492 0=1 1=1 2=-1
Squeeze                  squeeze_287              1 1 492 493 -23303=1,-1
Permute                  transpose_336            1 1 493 494 0=1
Convolution1D            conv1d_49                1 1 494 495 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_337            1 1 495 496 0=1
ExpandDims               unsqueeze_374            1 1 496 497 -23303=1,-1
Sigmoid                  sigmoid_228              1 1 497 498
BinaryOp                 mul_38                   2 1 489 498 499 0=2
BinaryOp                 add_39                   2 1 477 499 500 0=0
Split                    splitncnn_61             1 2 500 501 502
Convolution              conv_101                 1 1 502 503 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_183                1 1 503 504 0=1
Split                    splitncnn_62             1 2 504 505 506
ConvolutionDepthWise     convdw_425               1 1 506 507 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_184                1 1 507 508 0=1
Concat                   cat_21                   2 1 505 508 509 0=0
ConvolutionDepthWise     convdw_426               1 1 509 510 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_185                1 1 510 511 0=256
Convolution              conv_102                 1 1 511 512 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_63             1 2 512 513 514
Pooling                  gap_21                   1 1 514 515 0=1 4=1
Reshape                  reshape_258              1 1 515 516 0=1 1=1 2=-1
Squeeze                  squeeze_288              1 1 516 517 -23303=1,-1
Permute                  transpose_338            1 1 517 518 0=1
Convolution1D            conv1d_50                1 1 518 519 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_339            1 1 519 520 0=1
ExpandDims               unsqueeze_375            1 1 520 521 -23303=1,-1
Sigmoid                  sigmoid_229              1 1 521 522
BinaryOp                 mul_40                   2 1 513 522 523 0=2
BinaryOp                 add_41                   2 1 501 523 524 0=0
Split                    splitncnn_64             1 2 524 525 526
Convolution              conv_103                 1 1 526 527 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_186                1 1 527 528 0=1
Split                    splitncnn_65             1 2 528 529 530
ConvolutionDepthWise     convdw_427               1 1 530 531 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_187                1 1 531 532 0=1
Concat                   cat_22                   2 1 529 532 533 0=0
ConvolutionDepthWise     convdw_428               1 1 533 534 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_188                1 1 534 535 0=256
Convolution              conv_104                 1 1 535 536 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_66             1 2 536 537 538
Pooling                  gap_22                   1 1 538 539 0=1 4=1
Reshape                  reshape_259              1 1 539 540 0=1 1=1 2=-1
Squeeze                  squeeze_289              1 1 540 541 -23303=1,-1
Permute                  transpose_340            1 1 541 542 0=1
Convolution1D            conv1d_51                1 1 542 543 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_341            1 1 543 544 0=1
ExpandDims               unsqueeze_376            1 1 544 545 -23303=1,-1
Sigmoid                  sigmoid_230              1 1 545 546
BinaryOp                 mul_42                   2 1 537 546 547 0=2
BinaryOp                 add_43                   2 1 525 547 548 0=0
Split                    splitncnn_67             1 2 548 549 550
Convolution              conv_105                 1 1 550 551 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_189                1 1 551 552 0=1
Split                    splitncnn_68             1 2 552 553 554
ConvolutionDepthWise     convdw_429               1 1 554 555 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_190                1 1 555 556 0=1
Concat                   cat_23                   2 1 553 556 557 0=0
ConvolutionDepthWise     convdw_430               1 1 557 558 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_191                1 1 558 559 0=256
Convolution              conv_106                 1 1 559 560 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_69             1 2 560 561 562
Pooling                  gap_23                   1 1 562 563 0=1 4=1
Reshape                  reshape_260              1 1 563 564 0=1 1=1 2=-1
Squeeze                  squeeze_290              1 1 564 565 -23303=1,-1
Permute                  transpose_342            1 1 565 566 0=1
Convolution1D            conv1d_52                1 1 566 567 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_343            1 1 567 568 0=1
ExpandDims               unsqueeze_377            1 1 568 569 -23303=1,-1
Sigmoid                  sigmoid_231              1 1 569 570
BinaryOp                 mul_44                   2 1 561 570 571 0=2
BinaryOp                 add_45                   2 1 549 571 572 0=0
Convolution              conv_107                 1 1 572 573 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_192                1 1 573 574 0=1
Split                    splitncnn_70             1 2 574 575 576
ConvolutionDepthWise     convdw_431               1 1 576 577 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_193                1 1 577 578 0=1
Concat                   cat_24                   2 1 575 578 579 0=0
ConvolutionDepthWise     convdw_432               1 1 579 580 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=4608 7=512
PReLU                    prelu_194                1 1 580 581 0=512
Convolution              conv_108                 1 1 581 582 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Split                    splitncnn_71             1 2 582 583 584
Pooling                  gap_24                   1 1 584 585 0=1 4=1
Reshape                  reshape_261              1 1 585 586 0=1 1=1 2=-1
Squeeze                  squeeze_291              1 1 586 587 -23303=1,-1
Permute                  transpose_344            1 1 587 588 0=1
Convolution1D            conv1d_53                1 1 588 589 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_345            1 1 589 590 0=1
ExpandDims               unsqueeze_378            1 1 590 591 -23303=1,-1
Sigmoid                  sigmoid_232              1 1 591 592
BinaryOp                 mul_46                   2 1 583 592 593 0=2
Split                    splitncnn_72             1 2 593 594 595
Convolution              conv_109                 1 1 595 596 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_195                1 1 596 597 0=1
Split                    splitncnn_73             1 2 597 598 599
ConvolutionDepthWise     convdw_433               1 1 599 600 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_196                1 1 600 601 0=1
Concat                   cat_25                   2 1 598 601 602 0=0
ConvolutionDepthWise     convdw_434               1 1 602 603 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_197                1 1 603 604 0=256
Convolution              conv_110                 1 1 604 605 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_74             1 2 605 606 607
Pooling                  gap_25                   1 1 607 608 0=1 4=1
Reshape                  reshape_262              1 1 608 609 0=1 1=1 2=-1
Squeeze                  squeeze_292              1 1 609 610 -23303=1,-1
Permute                  transpose_346            1 1 610 611 0=1
Convolution1D            conv1d_54                1 1 611 612 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_347            1 1 612 613 0=1
ExpandDims               unsqueeze_379            1 1 613 614 -23303=1,-1
Sigmoid                  sigmoid_233              1 1 614 615
BinaryOp                 mul_47                   2 1 606 615 616 0=2
BinaryOp                 add_48                   2 1 594 616 617 0=0
Split                    splitncnn_75             1 2 617 618 619
Convolution              conv_111                 1 1 619 620 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_198                1 1 620 621 0=1
Split                    splitncnn_76             1 2 621 622 623
ConvolutionDepthWise     convdw_435               1 1 623 624 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_199                1 1 624 625 0=1
Concat                   cat_26                   2 1 622 625 626 0=0
ConvolutionDepthWise     convdw_436               1 1 626 627 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_200                1 1 627 628 0=256
Convolution              conv_112                 1 1 628 629 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_77             1 2 629 630 631
Pooling                  gap_26                   1 1 631 632 0=1 4=1
Reshape                  reshape_263              1 1 632 633 0=1 1=1 2=-1
Squeeze                  squeeze_293              1 1 633 634 -23303=1,-1
Permute                  transpose_348            1 1 634 635 0=1
Convolution1D            conv1d_55                1 1 635 636 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_349            1 1 636 637 0=1
ExpandDims               unsqueeze_380            1 1 637 638 -23303=1,-1
Sigmoid                  sigmoid_234              1 1 638 639
BinaryOp                 mul_49                   2 1 630 639 640 0=2
BinaryOp                 add_50                   2 1 618 640 641 0=0
Split                    splitncnn_78             1 2 641 642 643
Convolution              conv_113                 1 1 643 644 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_201                1 1 644 645 0=1
Split                    splitncnn_79             1 2 645 646 647
ConvolutionDepthWise     convdw_437               1 1 647 648 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_202                1 1 648 649 0=1
Concat                   cat_27                   2 1 646 649 650 0=0
ConvolutionDepthWise     convdw_438               1 1 650 651 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_203                1 1 651 652 0=256
Convolution              conv_114                 1 1 652 653 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_80             1 2 653 654 655
Pooling                  gap_27                   1 1 655 656 0=1 4=1
Reshape                  reshape_264              1 1 656 657 0=1 1=1 2=-1
Squeeze                  squeeze_294              1 1 657 658 -23303=1,-1
Permute                  transpose_350            1 1 658 659 0=1
Convolution1D            conv1d_56                1 1 659 660 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_351            1 1 660 661 0=1
ExpandDims               unsqueeze_381            1 1 661 662 -23303=1,-1
Sigmoid                  sigmoid_235              1 1 662 663
BinaryOp                 mul_51                   2 1 654 663 664 0=2
BinaryOp                 add_52                   2 1 642 664 665 0=0
Split                    splitncnn_81             1 2 665 666 667
Convolution              conv_115                 1 1 667 668 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_204                1 1 668 669 0=1
Split                    splitncnn_82             1 2 669 670 671
ConvolutionDepthWise     convdw_439               1 1 671 672 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_205                1 1 672 673 0=1
Concat                   cat_28                   2 1 670 673 674 0=0
ConvolutionDepthWise     convdw_440               1 1 674 675 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_206                1 1 675 676 0=256
Convolution              conv_116                 1 1 676 677 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_83             1 2 677 678 679
Pooling                  gap_28                   1 1 679 680 0=1 4=1
Reshape                  reshape_265              1 1 680 681 0=1 1=1 2=-1
Squeeze                  squeeze_295              1 1 681 682 -23303=1,-1
Permute                  transpose_352            1 1 682 683 0=1
Convolution1D            conv1d_57                1 1 683 684 0=1 1=5 2=1 3=1 4=2 5=0 6=5
Permute                  transpose_353            1 1 684 685 0=1
ExpandDims               unsqueeze_382            1 1 685 686 -23303=1,-1
Sigmoid                  sigmoid_236              1 1 686 687
BinaryOp                 mul_53                   2 1 678 687 688 0=2
BinaryOp                 add_54                   2 1 666 688 689 0=0
Convolution              conv_117                 1 1 689 690 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
PReLU                    prelu_207                1 1 690 691 0=512
ConvolutionDepthWise     convdw_441               1 1 691 692 0=512 1=7 11=7 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=25088 7=512
Reshape                  view_266                 1 1 692 693 0=512
InnerProduct             linear_118               1 1 693 out0 0=512 1=1 2=262144
