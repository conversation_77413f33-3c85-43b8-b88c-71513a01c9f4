#ifndef NCNN_INCLUDE_GUARD_backbone_epoch_24_loss_7_0121_id_h
#define NCNN_INCLUDE_GUARD_backbone_epoch_24_loss_7_0121_id_h
namespace backbone_epoch_24_loss_7_0121_param_id {
const int LAYER_input_1 = 0;
const int BLOB_input_1 = 0;
const int LAYER__conv1_conv_Conv = 1;
const int BLOB__conv1_conv_Conv_output_0 = 1;
const int LAYER__conv1_prelu_PRelu = 2;
const int BLOB__conv1_prelu_PRelu_output_0 = 2;
const int LAYER__conv2_dw_conv_Conv = 3;
const int BLOB__conv2_dw_conv_Conv_output_0 = 3;
const int LAYER__conv2_dw_prelu_PRelu = 4;
const int BLOB__conv2_dw_prelu_PRelu_output_0 = 4;
const int LAYER__conv_23_conv_conv_Conv = 5;
const int BLOB__conv_23_conv_conv_Conv_output_0 = 5;
const int LAYER__conv_23_conv_prelu_PRelu = 6;
const int BLOB__conv_23_conv_prelu_PRelu_output_0 = 6;
const int LAYER__conv_23_conv_dw_conv_Conv = 7;
const int BLOB__conv_23_conv_dw_conv_Conv_output_0 = 7;
const int LAYER__conv_23_conv_dw_prelu_PRelu = 8;
const int BLOB__conv_23_conv_dw_prelu_PRelu_output_0 = 8;
const int LAYER__conv_23_project_conv_Conv = 9;
const int BLOB__conv_23_project_conv_Conv_output_0 = 9;
const int LAYER_splitncnn_0 = 10;
const int BLOB__conv_23_project_conv_Conv_output_0_splitncnn_0 = 10;
const int BLOB__conv_23_project_conv_Conv_output_0_splitncnn_1 = 11;
const int LAYER__conv_3_model_model_0_conv_conv_Conv = 11;
const int BLOB__conv_3_model_model_0_conv_conv_Conv_output_0 = 12;
const int LAYER__conv_3_model_model_0_conv_prelu_PRelu = 12;
const int BLOB__conv_3_model_model_0_conv_prelu_PRelu_output_0 = 13;
const int LAYER__conv_3_model_model_0_conv_dw_conv_Conv = 13;
const int BLOB__conv_3_model_model_0_conv_dw_conv_Conv_output_0 = 14;
const int LAYER__conv_3_model_model_0_conv_dw_prelu_PRelu = 14;
const int BLOB__conv_3_model_model_0_conv_dw_prelu_PRelu_output_0 = 15;
const int LAYER__conv_3_model_model_0_project_conv_Conv = 15;
const int BLOB__conv_3_model_model_0_project_conv_Conv_output_0 = 16;
const int LAYER__conv_3_model_model_0_Add = 16;
const int BLOB__conv_3_model_model_0_Add_output_0 = 17;
const int LAYER_splitncnn_1 = 17;
const int BLOB__conv_3_model_model_0_Add_output_0_splitncnn_0 = 18;
const int BLOB__conv_3_model_model_0_Add_output_0_splitncnn_1 = 19;
const int LAYER__conv_3_model_model_1_conv_conv_Conv = 18;
const int BLOB__conv_3_model_model_1_conv_conv_Conv_output_0 = 20;
const int LAYER__conv_3_model_model_1_conv_prelu_PRelu = 19;
const int BLOB__conv_3_model_model_1_conv_prelu_PRelu_output_0 = 21;
const int LAYER__conv_3_model_model_1_conv_dw_conv_Conv = 20;
const int BLOB__conv_3_model_model_1_conv_dw_conv_Conv_output_0 = 22;
const int LAYER__conv_3_model_model_1_conv_dw_prelu_PRelu = 21;
const int BLOB__conv_3_model_model_1_conv_dw_prelu_PRelu_output_0 = 23;
const int LAYER__conv_3_model_model_1_project_conv_Conv = 22;
const int BLOB__conv_3_model_model_1_project_conv_Conv_output_0 = 24;
const int LAYER__conv_3_model_model_1_Add = 23;
const int BLOB__conv_3_model_model_1_Add_output_0 = 25;
const int LAYER_splitncnn_2 = 24;
const int BLOB__conv_3_model_model_1_Add_output_0_splitncnn_0 = 26;
const int BLOB__conv_3_model_model_1_Add_output_0_splitncnn_1 = 27;
const int LAYER__conv_3_model_model_2_conv_conv_Conv = 25;
const int BLOB__conv_3_model_model_2_conv_conv_Conv_output_0 = 28;
const int LAYER__conv_3_model_model_2_conv_prelu_PRelu = 26;
const int BLOB__conv_3_model_model_2_conv_prelu_PRelu_output_0 = 29;
const int LAYER__conv_3_model_model_2_conv_dw_conv_Conv = 27;
const int BLOB__conv_3_model_model_2_conv_dw_conv_Conv_output_0 = 30;
const int LAYER__conv_3_model_model_2_conv_dw_prelu_PRelu = 28;
const int BLOB__conv_3_model_model_2_conv_dw_prelu_PRelu_output_0 = 31;
const int LAYER__conv_3_model_model_2_project_conv_Conv = 29;
const int BLOB__conv_3_model_model_2_project_conv_Conv_output_0 = 32;
const int LAYER__conv_3_model_model_2_Add = 30;
const int BLOB__conv_3_model_model_2_Add_output_0 = 33;
const int LAYER_splitncnn_3 = 31;
const int BLOB__conv_3_model_model_2_Add_output_0_splitncnn_0 = 34;
const int BLOB__conv_3_model_model_2_Add_output_0_splitncnn_1 = 35;
const int LAYER__conv_3_model_model_3_conv_conv_Conv = 32;
const int BLOB__conv_3_model_model_3_conv_conv_Conv_output_0 = 36;
const int LAYER__conv_3_model_model_3_conv_prelu_PRelu = 33;
const int BLOB__conv_3_model_model_3_conv_prelu_PRelu_output_0 = 37;
const int LAYER__conv_3_model_model_3_conv_dw_conv_Conv = 34;
const int BLOB__conv_3_model_model_3_conv_dw_conv_Conv_output_0 = 38;
const int LAYER__conv_3_model_model_3_conv_dw_prelu_PRelu = 35;
const int BLOB__conv_3_model_model_3_conv_dw_prelu_PRelu_output_0 = 39;
const int LAYER__conv_3_model_model_3_project_conv_Conv = 36;
const int BLOB__conv_3_model_model_3_project_conv_Conv_output_0 = 40;
const int LAYER__conv_3_model_model_3_Add = 37;
const int BLOB__conv_3_model_model_3_Add_output_0 = 41;
const int LAYER__conv_34_conv_conv_Conv = 38;
const int BLOB__conv_34_conv_conv_Conv_output_0 = 42;
const int LAYER__conv_34_conv_prelu_PRelu = 39;
const int BLOB__conv_34_conv_prelu_PRelu_output_0 = 43;
const int LAYER__conv_34_conv_dw_conv_Conv = 40;
const int BLOB__conv_34_conv_dw_conv_Conv_output_0 = 44;
const int LAYER__conv_34_conv_dw_prelu_PRelu = 41;
const int BLOB__conv_34_conv_dw_prelu_PRelu_output_0 = 45;
const int LAYER__conv_34_project_conv_Conv = 42;
const int BLOB__conv_34_project_conv_Conv_output_0 = 46;
const int LAYER_splitncnn_4 = 43;
const int BLOB__conv_34_project_conv_Conv_output_0_splitncnn_0 = 47;
const int BLOB__conv_34_project_conv_Conv_output_0_splitncnn_1 = 48;
const int LAYER__conv_4_model_model_0_conv_conv_Conv = 44;
const int BLOB__conv_4_model_model_0_conv_conv_Conv_output_0 = 49;
const int LAYER__conv_4_model_model_0_conv_prelu_PRelu = 45;
const int BLOB__conv_4_model_model_0_conv_prelu_PRelu_output_0 = 50;
const int LAYER__conv_4_model_model_0_conv_dw_conv_Conv = 46;
const int BLOB__conv_4_model_model_0_conv_dw_conv_Conv_output_0 = 51;
const int LAYER__conv_4_model_model_0_conv_dw_prelu_PRelu = 47;
const int BLOB__conv_4_model_model_0_conv_dw_prelu_PRelu_output_0 = 52;
const int LAYER__conv_4_model_model_0_project_conv_Conv = 48;
const int BLOB__conv_4_model_model_0_project_conv_Conv_output_0 = 53;
const int LAYER__conv_4_model_model_0_Add = 49;
const int BLOB__conv_4_model_model_0_Add_output_0 = 54;
const int LAYER_splitncnn_5 = 50;
const int BLOB__conv_4_model_model_0_Add_output_0_splitncnn_0 = 55;
const int BLOB__conv_4_model_model_0_Add_output_0_splitncnn_1 = 56;
const int LAYER__conv_4_model_model_1_conv_conv_Conv = 51;
const int BLOB__conv_4_model_model_1_conv_conv_Conv_output_0 = 57;
const int LAYER__conv_4_model_model_1_conv_prelu_PRelu = 52;
const int BLOB__conv_4_model_model_1_conv_prelu_PRelu_output_0 = 58;
const int LAYER__conv_4_model_model_1_conv_dw_conv_Conv = 53;
const int BLOB__conv_4_model_model_1_conv_dw_conv_Conv_output_0 = 59;
const int LAYER__conv_4_model_model_1_conv_dw_prelu_PRelu = 54;
const int BLOB__conv_4_model_model_1_conv_dw_prelu_PRelu_output_0 = 60;
const int LAYER__conv_4_model_model_1_project_conv_Conv = 55;
const int BLOB__conv_4_model_model_1_project_conv_Conv_output_0 = 61;
const int LAYER__conv_4_model_model_1_Add = 56;
const int BLOB__conv_4_model_model_1_Add_output_0 = 62;
const int LAYER_splitncnn_6 = 57;
const int BLOB__conv_4_model_model_1_Add_output_0_splitncnn_0 = 63;
const int BLOB__conv_4_model_model_1_Add_output_0_splitncnn_1 = 64;
const int LAYER__conv_4_model_model_2_conv_conv_Conv = 58;
const int BLOB__conv_4_model_model_2_conv_conv_Conv_output_0 = 65;
const int LAYER__conv_4_model_model_2_conv_prelu_PRelu = 59;
const int BLOB__conv_4_model_model_2_conv_prelu_PRelu_output_0 = 66;
const int LAYER__conv_4_model_model_2_conv_dw_conv_Conv = 60;
const int BLOB__conv_4_model_model_2_conv_dw_conv_Conv_output_0 = 67;
const int LAYER__conv_4_model_model_2_conv_dw_prelu_PRelu = 61;
const int BLOB__conv_4_model_model_2_conv_dw_prelu_PRelu_output_0 = 68;
const int LAYER__conv_4_model_model_2_project_conv_Conv = 62;
const int BLOB__conv_4_model_model_2_project_conv_Conv_output_0 = 69;
const int LAYER__conv_4_model_model_2_Add = 63;
const int BLOB__conv_4_model_model_2_Add_output_0 = 70;
const int LAYER_splitncnn_7 = 64;
const int BLOB__conv_4_model_model_2_Add_output_0_splitncnn_0 = 71;
const int BLOB__conv_4_model_model_2_Add_output_0_splitncnn_1 = 72;
const int LAYER__conv_4_model_model_3_conv_conv_Conv = 65;
const int BLOB__conv_4_model_model_3_conv_conv_Conv_output_0 = 73;
const int LAYER__conv_4_model_model_3_conv_prelu_PRelu = 66;
const int BLOB__conv_4_model_model_3_conv_prelu_PRelu_output_0 = 74;
const int LAYER__conv_4_model_model_3_conv_dw_conv_Conv = 67;
const int BLOB__conv_4_model_model_3_conv_dw_conv_Conv_output_0 = 75;
const int LAYER__conv_4_model_model_3_conv_dw_prelu_PRelu = 68;
const int BLOB__conv_4_model_model_3_conv_dw_prelu_PRelu_output_0 = 76;
const int LAYER__conv_4_model_model_3_project_conv_Conv = 69;
const int BLOB__conv_4_model_model_3_project_conv_Conv_output_0 = 77;
const int LAYER__conv_4_model_model_3_Add = 70;
const int BLOB__conv_4_model_model_3_Add_output_0 = 78;
const int LAYER_splitncnn_8 = 71;
const int BLOB__conv_4_model_model_3_Add_output_0_splitncnn_0 = 79;
const int BLOB__conv_4_model_model_3_Add_output_0_splitncnn_1 = 80;
const int LAYER__conv_4_model_model_4_conv_conv_Conv = 72;
const int BLOB__conv_4_model_model_4_conv_conv_Conv_output_0 = 81;
const int LAYER__conv_4_model_model_4_conv_prelu_PRelu = 73;
const int BLOB__conv_4_model_model_4_conv_prelu_PRelu_output_0 = 82;
const int LAYER__conv_4_model_model_4_conv_dw_conv_Conv = 74;
const int BLOB__conv_4_model_model_4_conv_dw_conv_Conv_output_0 = 83;
const int LAYER__conv_4_model_model_4_conv_dw_prelu_PRelu = 75;
const int BLOB__conv_4_model_model_4_conv_dw_prelu_PRelu_output_0 = 84;
const int LAYER__conv_4_model_model_4_project_conv_Conv = 76;
const int BLOB__conv_4_model_model_4_project_conv_Conv_output_0 = 85;
const int LAYER__conv_4_model_model_4_Add = 77;
const int BLOB__conv_4_model_model_4_Add_output_0 = 86;
const int LAYER_splitncnn_9 = 78;
const int BLOB__conv_4_model_model_4_Add_output_0_splitncnn_0 = 87;
const int BLOB__conv_4_model_model_4_Add_output_0_splitncnn_1 = 88;
const int LAYER__conv_4_model_model_5_conv_conv_Conv = 79;
const int BLOB__conv_4_model_model_5_conv_conv_Conv_output_0 = 89;
const int LAYER__conv_4_model_model_5_conv_prelu_PRelu = 80;
const int BLOB__conv_4_model_model_5_conv_prelu_PRelu_output_0 = 90;
const int LAYER__conv_4_model_model_5_conv_dw_conv_Conv = 81;
const int BLOB__conv_4_model_model_5_conv_dw_conv_Conv_output_0 = 91;
const int LAYER__conv_4_model_model_5_conv_dw_prelu_PRelu = 82;
const int BLOB__conv_4_model_model_5_conv_dw_prelu_PRelu_output_0 = 92;
const int LAYER__conv_4_model_model_5_project_conv_Conv = 83;
const int BLOB__conv_4_model_model_5_project_conv_Conv_output_0 = 93;
const int LAYER__conv_4_model_model_5_Add = 84;
const int BLOB__conv_4_model_model_5_Add_output_0 = 94;
const int LAYER__conv_45_conv_conv_Conv = 85;
const int BLOB__conv_45_conv_conv_Conv_output_0 = 95;
const int LAYER__conv_45_conv_prelu_PRelu = 86;
const int BLOB__conv_45_conv_prelu_PRelu_output_0 = 96;
const int LAYER__conv_45_conv_dw_conv_Conv = 87;
const int BLOB__conv_45_conv_dw_conv_Conv_output_0 = 97;
const int LAYER__conv_45_conv_dw_prelu_PRelu = 88;
const int BLOB__conv_45_conv_dw_prelu_PRelu_output_0 = 98;
const int LAYER__conv_45_project_conv_Conv = 89;
const int BLOB__conv_45_project_conv_Conv_output_0 = 99;
const int LAYER_splitncnn_10 = 90;
const int BLOB__conv_45_project_conv_Conv_output_0_splitncnn_0 = 100;
const int BLOB__conv_45_project_conv_Conv_output_0_splitncnn_1 = 101;
const int LAYER__conv_5_model_model_0_conv_conv_Conv = 91;
const int BLOB__conv_5_model_model_0_conv_conv_Conv_output_0 = 102;
const int LAYER__conv_5_model_model_0_conv_prelu_PRelu = 92;
const int BLOB__conv_5_model_model_0_conv_prelu_PRelu_output_0 = 103;
const int LAYER__conv_5_model_model_0_conv_dw_conv_Conv = 93;
const int BLOB__conv_5_model_model_0_conv_dw_conv_Conv_output_0 = 104;
const int LAYER__conv_5_model_model_0_conv_dw_prelu_PRelu = 94;
const int BLOB__conv_5_model_model_0_conv_dw_prelu_PRelu_output_0 = 105;
const int LAYER__conv_5_model_model_0_project_conv_Conv = 95;
const int BLOB__conv_5_model_model_0_project_conv_Conv_output_0 = 106;
const int LAYER__conv_5_model_model_0_Add = 96;
const int BLOB__conv_5_model_model_0_Add_output_0 = 107;
const int LAYER_splitncnn_11 = 97;
const int BLOB__conv_5_model_model_0_Add_output_0_splitncnn_0 = 108;
const int BLOB__conv_5_model_model_0_Add_output_0_splitncnn_1 = 109;
const int LAYER__conv_5_model_model_1_conv_conv_Conv = 98;
const int BLOB__conv_5_model_model_1_conv_conv_Conv_output_0 = 110;
const int LAYER__conv_5_model_model_1_conv_prelu_PRelu = 99;
const int BLOB__conv_5_model_model_1_conv_prelu_PRelu_output_0 = 111;
const int LAYER__conv_5_model_model_1_conv_dw_conv_Conv = 100;
const int BLOB__conv_5_model_model_1_conv_dw_conv_Conv_output_0 = 112;
const int LAYER__conv_5_model_model_1_conv_dw_prelu_PRelu = 101;
const int BLOB__conv_5_model_model_1_conv_dw_prelu_PRelu_output_0 = 113;
const int LAYER__conv_5_model_model_1_project_conv_Conv = 102;
const int BLOB__conv_5_model_model_1_project_conv_Conv_output_0 = 114;
const int LAYER__conv_5_model_model_1_Add = 103;
const int BLOB__conv_5_model_model_1_Add_output_0 = 115;
const int LAYER__conv_6_sep_conv_Conv = 104;
const int BLOB__conv_6_sep_conv_Conv_output_0 = 116;
const int LAYER__conv_6_sep_prelu_PRelu = 105;
const int BLOB__conv_6_sep_prelu_PRelu_output_0 = 117;
const int LAYER__output_layer_conv_6_dw_conv_Conv = 106;
const int BLOB__output_layer_conv_6_dw_conv_Conv_output_0 = 118;
const int LAYER__output_layer_conv_6_flatten_Reshape = 107;
const int BLOB__output_layer_conv_6_flatten_Reshape_output_0 = 119;
const int LAYER__output_layer_linear_MatMul = 108;
const int BLOB__output_layer_linear_MatMul_output_0 = 120;
const int LAYER__output_layer_bn_BatchNormalization = 109;
const int BLOB_553 = 121;
} // namespace backbone_epoch_24_loss_7_0121_param_id
#endif // NCNN_INCLUDE_GUARD_backbone_epoch_24_loss_7_0121_id_h
