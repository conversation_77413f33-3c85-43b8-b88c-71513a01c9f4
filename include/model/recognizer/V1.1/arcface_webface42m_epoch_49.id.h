#ifndef NCNN_INCLUDE_GUARD_arcface_webface42m_epoch_49_id_h
#define NCNN_INCLUDE_GUARD_arcface_webface42m_epoch_49_id_h
namespace arcface_webface42m_epoch_49_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_conv_0 = 1;
const int BLOB_1 = 1;
const int LAYER_prelu_33 = 2;
const int BLOB_2 = 2;
const int LAYER_convdw_67 = 3;
const int BLOB_3 = 3;
const int LAYER_prelu_34 = 4;
const int BLOB_4 = 4;
const int LAYER_conv_1 = 5;
const int BLOB_5 = 5;
const int LAYER_prelu_35 = 6;
const int BLOB_6 = 6;
const int LAYER_convdw_68 = 7;
const int BLOB_7 = 7;
const int LAYER_prelu_36 = 8;
const int BLOB_8 = 8;
const int LAYER_conv_2 = 9;
const int BLOB_9 = 9;
const int LAYER_splitncnn_0 = 10;
const int BLOB_10 = 10;
const int BLOB_11 = 11;
const int LAYER_conv_3 = 11;
const int BLOB_12 = 12;
const int LAYER_prelu_37 = 12;
const int BLOB_13 = 13;
const int LAYER_convdw_69 = 13;
const int BLOB_14 = 14;
const int LAYER_prelu_38 = 14;
const int BLOB_15 = 15;
const int LAYER_conv_4 = 15;
const int BLOB_16 = 16;
const int LAYER_add_0 = 16;
const int BLOB_17 = 17;
const int LAYER_splitncnn_1 = 17;
const int BLOB_18 = 18;
const int BLOB_19 = 19;
const int LAYER_conv_5 = 18;
const int BLOB_20 = 20;
const int LAYER_prelu_39 = 19;
const int BLOB_21 = 21;
const int LAYER_convdw_70 = 20;
const int BLOB_22 = 22;
const int LAYER_prelu_40 = 21;
const int BLOB_23 = 23;
const int LAYER_conv_6 = 22;
const int BLOB_24 = 24;
const int LAYER_add_1 = 23;
const int BLOB_25 = 25;
const int LAYER_splitncnn_2 = 24;
const int BLOB_26 = 26;
const int BLOB_27 = 27;
const int LAYER_conv_7 = 25;
const int BLOB_28 = 28;
const int LAYER_prelu_41 = 26;
const int BLOB_29 = 29;
const int LAYER_convdw_71 = 27;
const int BLOB_30 = 30;
const int LAYER_prelu_42 = 28;
const int BLOB_31 = 31;
const int LAYER_conv_8 = 29;
const int BLOB_32 = 32;
const int LAYER_add_2 = 30;
const int BLOB_33 = 33;
const int LAYER_splitncnn_3 = 31;
const int BLOB_34 = 34;
const int BLOB_35 = 35;
const int LAYER_conv_9 = 32;
const int BLOB_36 = 36;
const int LAYER_prelu_43 = 33;
const int BLOB_37 = 37;
const int LAYER_convdw_72 = 34;
const int BLOB_38 = 38;
const int LAYER_prelu_44 = 35;
const int BLOB_39 = 39;
const int LAYER_conv_10 = 36;
const int BLOB_40 = 40;
const int LAYER_add_3 = 37;
const int BLOB_41 = 41;
const int LAYER_conv_11 = 38;
const int BLOB_42 = 42;
const int LAYER_prelu_45 = 39;
const int BLOB_43 = 43;
const int LAYER_convdw_73 = 40;
const int BLOB_44 = 44;
const int LAYER_prelu_46 = 41;
const int BLOB_45 = 45;
const int LAYER_conv_12 = 42;
const int BLOB_46 = 46;
const int LAYER_splitncnn_4 = 43;
const int BLOB_47 = 47;
const int BLOB_48 = 48;
const int LAYER_conv_13 = 44;
const int BLOB_49 = 49;
const int LAYER_prelu_47 = 45;
const int BLOB_50 = 50;
const int LAYER_convdw_74 = 46;
const int BLOB_51 = 51;
const int LAYER_prelu_48 = 47;
const int BLOB_52 = 52;
const int LAYER_conv_14 = 48;
const int BLOB_53 = 53;
const int LAYER_add_4 = 49;
const int BLOB_54 = 54;
const int LAYER_splitncnn_5 = 50;
const int BLOB_55 = 55;
const int BLOB_56 = 56;
const int LAYER_conv_15 = 51;
const int BLOB_57 = 57;
const int LAYER_prelu_49 = 52;
const int BLOB_58 = 58;
const int LAYER_convdw_75 = 53;
const int BLOB_59 = 59;
const int LAYER_prelu_50 = 54;
const int BLOB_60 = 60;
const int LAYER_conv_16 = 55;
const int BLOB_61 = 61;
const int LAYER_add_5 = 56;
const int BLOB_62 = 62;
const int LAYER_splitncnn_6 = 57;
const int BLOB_63 = 63;
const int BLOB_64 = 64;
const int LAYER_conv_17 = 58;
const int BLOB_65 = 65;
const int LAYER_prelu_51 = 59;
const int BLOB_66 = 66;
const int LAYER_convdw_76 = 60;
const int BLOB_67 = 67;
const int LAYER_prelu_52 = 61;
const int BLOB_68 = 68;
const int LAYER_conv_18 = 62;
const int BLOB_69 = 69;
const int LAYER_add_6 = 63;
const int BLOB_70 = 70;
const int LAYER_splitncnn_7 = 64;
const int BLOB_71 = 71;
const int BLOB_72 = 72;
const int LAYER_conv_19 = 65;
const int BLOB_73 = 73;
const int LAYER_prelu_53 = 66;
const int BLOB_74 = 74;
const int LAYER_convdw_77 = 67;
const int BLOB_75 = 75;
const int LAYER_prelu_54 = 68;
const int BLOB_76 = 76;
const int LAYER_conv_20 = 69;
const int BLOB_77 = 77;
const int LAYER_add_7 = 70;
const int BLOB_78 = 78;
const int LAYER_splitncnn_8 = 71;
const int BLOB_79 = 79;
const int BLOB_80 = 80;
const int LAYER_conv_21 = 72;
const int BLOB_81 = 81;
const int LAYER_prelu_55 = 73;
const int BLOB_82 = 82;
const int LAYER_convdw_78 = 74;
const int BLOB_83 = 83;
const int LAYER_prelu_56 = 75;
const int BLOB_84 = 84;
const int LAYER_conv_22 = 76;
const int BLOB_85 = 85;
const int LAYER_add_8 = 77;
const int BLOB_86 = 86;
const int LAYER_splitncnn_9 = 78;
const int BLOB_87 = 87;
const int BLOB_88 = 88;
const int LAYER_conv_23 = 79;
const int BLOB_89 = 89;
const int LAYER_prelu_57 = 80;
const int BLOB_90 = 90;
const int LAYER_convdw_79 = 81;
const int BLOB_91 = 91;
const int LAYER_prelu_58 = 82;
const int BLOB_92 = 92;
const int LAYER_conv_24 = 83;
const int BLOB_93 = 93;
const int LAYER_add_9 = 84;
const int BLOB_94 = 94;
const int LAYER_conv_25 = 85;
const int BLOB_95 = 95;
const int LAYER_prelu_59 = 86;
const int BLOB_96 = 96;
const int LAYER_convdw_80 = 87;
const int BLOB_97 = 97;
const int LAYER_prelu_60 = 88;
const int BLOB_98 = 98;
const int LAYER_conv_26 = 89;
const int BLOB_99 = 99;
const int LAYER_splitncnn_10 = 90;
const int BLOB_100 = 100;
const int BLOB_101 = 101;
const int LAYER_conv_27 = 91;
const int BLOB_102 = 102;
const int LAYER_prelu_61 = 92;
const int BLOB_103 = 103;
const int LAYER_convdw_81 = 93;
const int BLOB_104 = 104;
const int LAYER_prelu_62 = 94;
const int BLOB_105 = 105;
const int LAYER_conv_28 = 95;
const int BLOB_106 = 106;
const int LAYER_add_10 = 96;
const int BLOB_107 = 107;
const int LAYER_splitncnn_11 = 97;
const int BLOB_108 = 108;
const int BLOB_109 = 109;
const int LAYER_conv_29 = 98;
const int BLOB_110 = 110;
const int LAYER_prelu_63 = 99;
const int BLOB_111 = 111;
const int LAYER_convdw_82 = 100;
const int BLOB_112 = 112;
const int LAYER_prelu_64 = 101;
const int BLOB_113 = 113;
const int LAYER_conv_30 = 102;
const int BLOB_114 = 114;
const int LAYER_add_11 = 103;
const int BLOB_115 = 115;
const int LAYER_conv_31 = 104;
const int BLOB_116 = 116;
const int LAYER_prelu_65 = 105;
const int BLOB_117 = 117;
const int LAYER_convdw_83 = 106;
const int BLOB_118 = 118;
const int LAYER_view_66 = 107;
const int BLOB_119 = 119;
const int LAYER_linear_32 = 108;
const int BLOB_out0 = 120;
} // namespace arcface_webface42m_epoch_49_param_id
#endif // NCNN_INCLUDE_GUARD_arcface_webface42m_epoch_49_id_h
