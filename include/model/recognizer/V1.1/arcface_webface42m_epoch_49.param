7767517
109 121
Input                    in0                      0 1 in0
Convolution              conv_0                   1 1 in0 1 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1728
PReLU                    prelu_33                 1 1 1 2 0=64
ConvolutionDepthWise     convdw_67                1 1 2 3 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
PReLU                    prelu_34                 1 1 3 4 0=64
Convolution              conv_1                   1 1 4 5 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_35                 1 1 5 6 0=128
ConvolutionDepthWise     convdw_68                1 1 6 7 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1152 7=128
PReLU                    prelu_36                 1 1 7 8 0=128
Convolution              conv_2                   1 1 8 9 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
Split                    splitncnn_0              1 2 9 10 11
Convolution              conv_3                   1 1 11 12 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_37                 1 1 12 13 0=128
ConvolutionDepthWise     convdw_69                1 1 13 14 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_38                 1 1 14 15 0=128
Convolution              conv_4                   1 1 15 16 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
BinaryOp                 add_0                    2 1 10 16 17 0=0
Split                    splitncnn_1              1 2 17 18 19
Convolution              conv_5                   1 1 19 20 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_39                 1 1 20 21 0=128
ConvolutionDepthWise     convdw_70                1 1 21 22 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_40                 1 1 22 23 0=128
Convolution              conv_6                   1 1 23 24 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
BinaryOp                 add_1                    2 1 18 24 25 0=0
Split                    splitncnn_2              1 2 25 26 27
Convolution              conv_7                   1 1 27 28 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_41                 1 1 28 29 0=128
ConvolutionDepthWise     convdw_71                1 1 29 30 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_42                 1 1 30 31 0=128
Convolution              conv_8                   1 1 31 32 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
BinaryOp                 add_2                    2 1 26 32 33 0=0
Split                    splitncnn_3              1 2 33 34 35
Convolution              conv_9                   1 1 35 36 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
PReLU                    prelu_43                 1 1 36 37 0=128
ConvolutionDepthWise     convdw_72                1 1 37 38 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
PReLU                    prelu_44                 1 1 38 39 0=128
Convolution              conv_10                  1 1 39 40 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=8192
BinaryOp                 add_3                    2 1 34 40 41 0=0
Convolution              conv_11                  1 1 41 42 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
PReLU                    prelu_45                 1 1 42 43 0=256
ConvolutionDepthWise     convdw_73                1 1 43 44 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2304 7=256
PReLU                    prelu_46                 1 1 44 45 0=256
Convolution              conv_12                  1 1 45 46 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Split                    splitncnn_4              1 2 46 47 48
Convolution              conv_13                  1 1 48 49 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_47                 1 1 49 50 0=256
ConvolutionDepthWise     convdw_74                1 1 50 51 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_48                 1 1 51 52 0=256
Convolution              conv_14                  1 1 52 53 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_4                    2 1 47 53 54 0=0
Split                    splitncnn_5              1 2 54 55 56
Convolution              conv_15                  1 1 56 57 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_49                 1 1 57 58 0=256
ConvolutionDepthWise     convdw_75                1 1 58 59 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_50                 1 1 59 60 0=256
Convolution              conv_16                  1 1 60 61 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_5                    2 1 55 61 62 0=0
Split                    splitncnn_6              1 2 62 63 64
Convolution              conv_17                  1 1 64 65 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_51                 1 1 65 66 0=256
ConvolutionDepthWise     convdw_76                1 1 66 67 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_52                 1 1 67 68 0=256
Convolution              conv_18                  1 1 68 69 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_6                    2 1 63 69 70 0=0
Split                    splitncnn_7              1 2 70 71 72
Convolution              conv_19                  1 1 72 73 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_53                 1 1 73 74 0=256
ConvolutionDepthWise     convdw_77                1 1 74 75 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_54                 1 1 75 76 0=256
Convolution              conv_20                  1 1 76 77 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_7                    2 1 71 77 78 0=0
Split                    splitncnn_8              1 2 78 79 80
Convolution              conv_21                  1 1 80 81 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_55                 1 1 81 82 0=256
ConvolutionDepthWise     convdw_78                1 1 82 83 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_56                 1 1 83 84 0=256
Convolution              conv_22                  1 1 84 85 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_8                    2 1 79 85 86 0=0
Split                    splitncnn_9              1 2 86 87 88
Convolution              conv_23                  1 1 88 89 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_57                 1 1 89 90 0=256
ConvolutionDepthWise     convdw_79                1 1 90 91 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_58                 1 1 91 92 0=256
Convolution              conv_24                  1 1 92 93 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_9                    2 1 87 93 94 0=0
Convolution              conv_25                  1 1 94 95 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
PReLU                    prelu_59                 1 1 95 96 0=512
ConvolutionDepthWise     convdw_80                1 1 96 97 0=512 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=4608 7=512
PReLU                    prelu_60                 1 1 97 98 0=512
Convolution              conv_26                  1 1 98 99 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Split                    splitncnn_10             1 2 99 100 101
Convolution              conv_27                  1 1 101 102 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_61                 1 1 102 103 0=256
ConvolutionDepthWise     convdw_81                1 1 103 104 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_62                 1 1 104 105 0=256
Convolution              conv_28                  1 1 105 106 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_10                   2 1 100 106 107 0=0
Split                    splitncnn_11             1 2 107 108 109
Convolution              conv_29                  1 1 109 110 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
PReLU                    prelu_63                 1 1 110 111 0=256
ConvolutionDepthWise     convdw_82                1 1 111 112 0=256 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=2304 7=256
PReLU                    prelu_64                 1 1 112 113 0=256
Convolution              conv_30                  1 1 113 114 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_11                   2 1 108 114 115 0=0
Convolution              conv_31                  1 1 115 116 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
PReLU                    prelu_65                 1 1 116 117 0=512
ConvolutionDepthWise     convdw_83                1 1 117 118 0=512 1=7 11=7 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=25088 7=512
Reshape                  view_66                  1 1 118 119 0=512
InnerProduct             linear_32                1 1 119 out0 0=512 1=1 2=262144
