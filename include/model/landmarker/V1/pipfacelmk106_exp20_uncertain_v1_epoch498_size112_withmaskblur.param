7767517
87 101
Input            input.1                  0 1 input.1
Convolution      Conv_0                   1 1 input.1 286 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=288
ReLU             Relu_1                   1 1 286 169
Convolution      Conv_2                   1 1 169 289 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=9216
ReLU             Relu_3                   1 1 289 172
Convolution      Conv_4                   1 1 172 292 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2048
ReLU             Relu_5                   1 1 292 175
Split            splitncnn_0              1 2 175 175_splitncnn_0 175_splitncnn_1
Convolution      Conv_6                   1 1 175_splitncnn_1 295 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
ReLU             Relu_7                   1 1 295 178
Convolution      Conv_8                   1 1 178 298 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
BinaryOp         Add_9                    2 1 298 175_splitncnn_0 181 0=0
ReLU             Relu_10                  1 1 181 182
Split            splitncnn_1              1 2 182 182_splitncnn_0 182_splitncnn_1
Convolution      Conv_11                  1 1 182_splitncnn_1 301 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
ReLU             Relu_12                  1 1 301 185
Convolution      Conv_13                  1 1 185 304 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
BinaryOp         Add_14                   2 1 304 182_splitncnn_0 188 0=0
ReLU             Relu_15                  1 1 188 189
Split            splitncnn_2              1 2 189 189_splitncnn_0 189_splitncnn_1
Convolution      Conv_16                  1 1 189_splitncnn_1 307 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=36864
ReLU             Relu_17                  1 1 307 192
Convolution      Conv_18                  1 1 192 310 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
BinaryOp         Add_19                   2 1 310 189_splitncnn_0 195 0=0
ReLU             Relu_20                  1 1 195 196
Convolution      Conv_21                  1 1 196 313 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=36864
ReLU             Relu_22                  1 1 313 199
Convolution      Conv_23                  1 1 199 316 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=8192
ReLU             Relu_24                  1 1 316 202
Split            splitncnn_3              1 2 202 202_splitncnn_0 202_splitncnn_1
Convolution      Conv_25                  1 1 202_splitncnn_1 319 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
ReLU             Relu_26                  1 1 319 205
Convolution      Conv_27                  1 1 205 322 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
BinaryOp         Add_28                   2 1 322 202_splitncnn_0 208 0=0
ReLU             Relu_29                  1 1 208 209
Split            splitncnn_4              1 2 209 209_splitncnn_0 209_splitncnn_1
Convolution      Conv_30                  1 1 209_splitncnn_1 325 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=147456
ReLU             Relu_31                  1 1 325 212
Convolution      Conv_32                  1 1 212 328 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
BinaryOp         Add_33                   2 1 328 209_splitncnn_0 215 0=0
ReLU             Relu_34                  1 1 215 216
Convolution      Conv_35                  1 1 216 331 0=128 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=147456
ReLU             Relu_36                  1 1 331 219
Convolution      Conv_37                  1 1 219 334 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
ReLU             Relu_38                  1 1 334 222
Split            splitncnn_5              1 2 222 222_splitncnn_0 222_splitncnn_1
ConvolutionDepthWise Conv_39                  1 1 222_splitncnn_1 337 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=294912 7=2
ReLU             Relu_40                  1 1 337 225
Convolution      Conv_41                  1 1 225 340 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
BinaryOp         Add_42                   2 1 340 222_splitncnn_0 228 0=0
ReLU             Relu_43                  1 1 228 229
Split            splitncnn_6              1 2 229 229_splitncnn_0 229_splitncnn_1
ConvolutionDepthWise Conv_44                  1 1 229_splitncnn_1 343 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=294912 7=2
ReLU             Relu_45                  1 1 343 232
Convolution      Conv_46                  1 1 232 346 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
BinaryOp         Add_47                   2 1 346 229_splitncnn_0 235 0=0
ReLU             Relu_48                  1 1 235 236
Split            splitncnn_7              1 8 236 236_splitncnn_0 236_splitncnn_1 236_splitncnn_2 236_splitncnn_3 236_splitncnn_4 236_splitncnn_5 236_splitncnn_6 236_splitncnn_7
ConvolutionDepthWise Conv_49                  1 1 236_splitncnn_7 349 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=2304 7=256
Swish            Mul_51                   1 1 349 240
Convolution      Conv_52                  1 1 240 352 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_54                   1 1 352 244
Pooling          GlobalAveragePool_55     1 1 244 245 0=1 4=1
Reshape          Reshape_56               1 1 245 251 0=-1
InnerProduct     Gemm_57                  1 1 251 blur_attr 0=2 1=1 2=256
ConvolutionDepthWise Conv_58                  1 1 236_splitncnn_6 355 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=2304 7=256
Swish            Mul_60                   1 1 355 256
Convolution      Conv_61                  1 1 256 358 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Swish            Mul_63                   1 1 358 260
Pooling          GlobalAveragePool_64     1 1 260 261 0=1 4=1
Reshape          Reshape_65               1 1 261 267 0=-1
InnerProduct     Gemm_66                  1 1 267 mask_attr 0=2 1=1 2=256
Convolution      Conv_67                  1 1 236_splitncnn_5 269 0=106 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=27136
Sigmoid          Sigmoid_68               1 1 269 hm
Convolution      Conv_69                  1 1 236_splitncnn_4 271 0=106 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=27136
Sigmoid          Sigmoid_70               1 1 271 offset_x
Convolution      Conv_71                  1 1 236_splitncnn_3 273 0=106 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=27136
Sigmoid          Sigmoid_72               1 1 273 offset_y
Convolution      Conv_73                  1 1 236_splitncnn_2 275 0=106 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=27136
Sigmoid          Sigmoid_74               1 1 275 vis
Padding          Pad_76                   1 1 236_splitncnn_1 278 0=0 1=0 2=0 3=0 4=0 5=0.000000e+00 7=0 8=0
Pooling          AveragePool_77           1 1 278 279 0=1 1=7 11=7 2=7 12=7 3=0 13=0 14=0 15=0 5=1 6=0
Convolution      Conv_78                  1 1 279 pose 0=3 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=768
Padding          Pad_80                   1 1 236_splitncnn_0 282 0=0 1=0 2=0 3=0 4=0 5=0.000000e+00 7=0 8=0
Pooling          AveragePool_81           1 1 282 283 0=1 1=7 11=7 2=7 12=7 3=0 13=0 14=0 15=0 5=1 6=0
Convolution      Conv_82                  1 1 283 284 0=1 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=256
ReLU             Relu_83                  1 1 284 uncertain
