#ifndef NCNN_INCLUDE_GUARD_pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur_id_h
#define NCNN_INCLUDE_GUARD_pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur_id_h
namespace pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur_param_id {
const int LAYER_input_1 = 0;
const int BLOB_input_1 = 0;
const int LAYER_Conv_0 = 1;
const int BLOB_286 = 1;
const int LAYER_Relu_1 = 2;
const int BLOB_169 = 2;
const int LAYER_Conv_2 = 3;
const int BLOB_289 = 3;
const int LAYER_Relu_3 = 4;
const int BLOB_172 = 4;
const int LAYER_Conv_4 = 5;
const int BLOB_292 = 5;
const int LAYER_Relu_5 = 6;
const int BLOB_175 = 6;
const int LAYER_splitncnn_0 = 7;
const int BLOB_175_splitncnn_0 = 7;
const int BLOB_175_splitncnn_1 = 8;
const int LAYER_Conv_6 = 8;
const int BLOB_295 = 9;
const int LAYER_Relu_7 = 9;
const int BLOB_178 = 10;
const int LAYER_Conv_8 = 10;
const int BLOB_298 = 11;
const int LAYER_Add_9 = 11;
const int BLOB_181 = 12;
const int LAYER_Relu_10 = 12;
const int BLOB_182 = 13;
const int LAYER_splitncnn_1 = 13;
const int BLOB_182_splitncnn_0 = 14;
const int BLOB_182_splitncnn_1 = 15;
const int LAYER_Conv_11 = 14;
const int BLOB_301 = 16;
const int LAYER_Relu_12 = 15;
const int BLOB_185 = 17;
const int LAYER_Conv_13 = 16;
const int BLOB_304 = 18;
const int LAYER_Add_14 = 17;
const int BLOB_188 = 19;
const int LAYER_Relu_15 = 18;
const int BLOB_189 = 20;
const int LAYER_splitncnn_2 = 19;
const int BLOB_189_splitncnn_0 = 21;
const int BLOB_189_splitncnn_1 = 22;
const int LAYER_Conv_16 = 20;
const int BLOB_307 = 23;
const int LAYER_Relu_17 = 21;
const int BLOB_192 = 24;
const int LAYER_Conv_18 = 22;
const int BLOB_310 = 25;
const int LAYER_Add_19 = 23;
const int BLOB_195 = 26;
const int LAYER_Relu_20 = 24;
const int BLOB_196 = 27;
const int LAYER_Conv_21 = 25;
const int BLOB_313 = 28;
const int LAYER_Relu_22 = 26;
const int BLOB_199 = 29;
const int LAYER_Conv_23 = 27;
const int BLOB_316 = 30;
const int LAYER_Relu_24 = 28;
const int BLOB_202 = 31;
const int LAYER_splitncnn_3 = 29;
const int BLOB_202_splitncnn_0 = 32;
const int BLOB_202_splitncnn_1 = 33;
const int LAYER_Conv_25 = 30;
const int BLOB_319 = 34;
const int LAYER_Relu_26 = 31;
const int BLOB_205 = 35;
const int LAYER_Conv_27 = 32;
const int BLOB_322 = 36;
const int LAYER_Add_28 = 33;
const int BLOB_208 = 37;
const int LAYER_Relu_29 = 34;
const int BLOB_209 = 38;
const int LAYER_splitncnn_4 = 35;
const int BLOB_209_splitncnn_0 = 39;
const int BLOB_209_splitncnn_1 = 40;
const int LAYER_Conv_30 = 36;
const int BLOB_325 = 41;
const int LAYER_Relu_31 = 37;
const int BLOB_212 = 42;
const int LAYER_Conv_32 = 38;
const int BLOB_328 = 43;
const int LAYER_Add_33 = 39;
const int BLOB_215 = 44;
const int LAYER_Relu_34 = 40;
const int BLOB_216 = 45;
const int LAYER_Conv_35 = 41;
const int BLOB_331 = 46;
const int LAYER_Relu_36 = 42;
const int BLOB_219 = 47;
const int LAYER_Conv_37 = 43;
const int BLOB_334 = 48;
const int LAYER_Relu_38 = 44;
const int BLOB_222 = 49;
const int LAYER_splitncnn_5 = 45;
const int BLOB_222_splitncnn_0 = 50;
const int BLOB_222_splitncnn_1 = 51;
const int LAYER_Conv_39 = 46;
const int BLOB_337 = 52;
const int LAYER_Relu_40 = 47;
const int BLOB_225 = 53;
const int LAYER_Conv_41 = 48;
const int BLOB_340 = 54;
const int LAYER_Add_42 = 49;
const int BLOB_228 = 55;
const int LAYER_Relu_43 = 50;
const int BLOB_229 = 56;
const int LAYER_splitncnn_6 = 51;
const int BLOB_229_splitncnn_0 = 57;
const int BLOB_229_splitncnn_1 = 58;
const int LAYER_Conv_44 = 52;
const int BLOB_343 = 59;
const int LAYER_Relu_45 = 53;
const int BLOB_232 = 60;
const int LAYER_Conv_46 = 54;
const int BLOB_346 = 61;
const int LAYER_Add_47 = 55;
const int BLOB_235 = 62;
const int LAYER_Relu_48 = 56;
const int BLOB_236 = 63;
const int LAYER_splitncnn_7 = 57;
const int BLOB_236_splitncnn_0 = 64;
const int BLOB_236_splitncnn_1 = 65;
const int BLOB_236_splitncnn_2 = 66;
const int BLOB_236_splitncnn_3 = 67;
const int BLOB_236_splitncnn_4 = 68;
const int BLOB_236_splitncnn_5 = 69;
const int BLOB_236_splitncnn_6 = 70;
const int BLOB_236_splitncnn_7 = 71;
const int LAYER_Conv_49 = 58;
const int BLOB_349 = 72;
const int LAYER_Mul_51 = 59;
const int BLOB_240 = 73;
const int LAYER_Conv_52 = 60;
const int BLOB_352 = 74;
const int LAYER_Mul_54 = 61;
const int BLOB_244 = 75;
const int LAYER_GlobalAveragePool_55 = 62;
const int BLOB_245 = 76;
const int LAYER_Reshape_56 = 63;
const int BLOB_251 = 77;
const int LAYER_Gemm_57 = 64;
const int BLOB_blur_attr = 78;
const int LAYER_Conv_58 = 65;
const int BLOB_355 = 79;
const int LAYER_Mul_60 = 66;
const int BLOB_256 = 80;
const int LAYER_Conv_61 = 67;
const int BLOB_358 = 81;
const int LAYER_Mul_63 = 68;
const int BLOB_260 = 82;
const int LAYER_GlobalAveragePool_64 = 69;
const int BLOB_261 = 83;
const int LAYER_Reshape_65 = 70;
const int BLOB_267 = 84;
const int LAYER_Gemm_66 = 71;
const int BLOB_mask_attr = 85;
const int LAYER_Conv_67 = 72;
const int BLOB_269 = 86;
const int LAYER_Sigmoid_68 = 73;
const int BLOB_hm = 87;
const int LAYER_Conv_69 = 74;
const int BLOB_271 = 88;
const int LAYER_Sigmoid_70 = 75;
const int BLOB_offset_x = 89;
const int LAYER_Conv_71 = 76;
const int BLOB_273 = 90;
const int LAYER_Sigmoid_72 = 77;
const int BLOB_offset_y = 91;
const int LAYER_Conv_73 = 78;
const int BLOB_275 = 92;
const int LAYER_Sigmoid_74 = 79;
const int BLOB_vis = 93;
const int LAYER_Pad_76 = 80;
const int BLOB_278 = 94;
const int LAYER_AveragePool_77 = 81;
const int BLOB_279 = 95;
const int LAYER_Conv_78 = 82;
const int BLOB_pose = 96;
const int LAYER_Pad_80 = 83;
const int BLOB_282 = 97;
const int LAYER_AveragePool_81 = 84;
const int BLOB_283 = 98;
const int LAYER_Conv_82 = 85;
const int BLOB_284 = 99;
const int LAYER_Relu_83 = 86;
const int BLOB_uncertain = 100;
} // namespace pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur_param_id
#endif // NCNN_INCLUDE_GUARD_pipfacelmk106_exp20_uncertain_v1_epoch498_size112_withmaskblur_id_h
