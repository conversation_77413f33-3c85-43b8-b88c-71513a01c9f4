7767517
67 80
Input                    in0                      0 1 in0
Convolution              convrelu_0               1 1 in0 1 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=864 9=1
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_42                1 1 3 4 0=32 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=800 7=32
Convolution              conv_4                   1 1 4 5 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2048
Pooling                  maxpool2d_24             1 1 2 6 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_5                   1 1 6 7 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2048
BinaryOp                 add_0                    2 1 5 7 8 0=0
ReLU                     relu_28                  1 1 8 9
Split                    splitncnn_1              1 2 9 10 11
ConvolutionDepthWise     convdw_43                1 1 11 12 0=64 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=1600 7=64
Convolution              conv_6                   1 1 12 13 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
BinaryOp                 add_1                    2 1 13 10 14 0=0
ReLU                     relu_29                  1 1 14 15
Split                    splitncnn_2              1 2 15 16 17
ConvolutionDepthWise     convdw_44                1 1 17 18 0=64 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=1600 7=64
Convolution              conv_7                   1 1 18 19 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
BinaryOp                 add_2                    2 1 19 16 20 0=0
ReLU                     relu_30                  1 1 20 21
Split                    splitncnn_3              1 2 21 22 23
ConvolutionDepthWise     convdw_45                1 1 23 24 0=64 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=1600 7=64
Convolution              conv_8                   1 1 24 25 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6144
Pooling                  maxpool2d_25             1 1 22 26 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_9                   1 1 26 27 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6144
BinaryOp                 add_3                    2 1 25 27 28 0=0
ReLU                     relu_31                  1 1 28 29
Split                    splitncnn_4              1 2 29 30 31
ConvolutionDepthWise     convdw_46                1 1 31 32 0=96 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=2400 7=96
Convolution              conv_10                  1 1 32 33 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=9216
BinaryOp                 add_4                    2 1 33 30 34 0=0
ReLU                     relu_32                  1 1 34 35
Split                    splitncnn_5              1 2 35 36 37
ConvolutionDepthWise     convdw_47                1 1 37 38 0=96 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=2400 7=96
Convolution              conv_11                  1 1 38 39 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=9216
BinaryOp                 add_5                    2 1 39 36 40 0=0
ReLU                     relu_33                  1 1 40 41
Split                    splitncnn_6              1 2 41 42 43
ConvolutionDepthWise     convdw_48                1 1 43 44 0=96 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=2400 7=96
Convolution              convrelu_1               1 1 44 45 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3072 9=1
ConvolutionDepthWise     convdw_49                1 1 45 46 0=32 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=800 7=32
Convolution              conv_13                  1 1 46 47 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
Pooling                  maxpool2d_26             1 1 42 48 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_14                  1 1 48 49 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=12288
BinaryOp                 add_6                    2 1 47 49 50 0=0
ReLU                     relu_35                  1 1 50 51
Split                    splitncnn_7              1 2 51 52 53
ConvolutionDepthWise     convdw_50                1 1 53 54 0=128 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=3200 7=128
Convolution              convrelu_2               1 1 54 55 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=1
ConvolutionDepthWise     convdw_51                1 1 55 56 0=32 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=800 7=32
Convolution              conv_16                  1 1 56 57 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
BinaryOp                 add_7                    2 1 57 52 58 0=0
ReLU                     relu_37                  1 1 58 59
Split                    splitncnn_8              1 2 59 60 61
ConvolutionDepthWise     convdw_52                1 1 61 62 0=128 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=3200 7=128
Convolution              convrelu_3               1 1 62 63 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=1
ConvolutionDepthWise     convdw_53                1 1 63 64 0=32 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=800 7=32
Convolution              conv_18                  1 1 64 65 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
BinaryOp                 add_8                    2 1 65 60 66 0=0
ReLU                     relu_39                  1 1 66 67
Split                    splitncnn_9              1 4 67 68 69 70 71
Convolution              convsigmoid_4            1 1 71 out0 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=640 9=4
Convolution              convsigmoid_5            1 1 70 out1 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=640 9=4
Convolution              convsigmoid_6            1 1 69 out2 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=640 9=4
Pooling                  avgpool2d_2              1 1 68 75 0=1 1=7 11=7 12=7 13=0 2=7 3=0 5=1 6=1
Split                    splitncnn_10             1 2 75 76 77
Convolution              conv_22                  1 1 77 out3 0=3 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=384
Convolution              convsigmoid_7            1 1 76 out4 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=256 9=4
