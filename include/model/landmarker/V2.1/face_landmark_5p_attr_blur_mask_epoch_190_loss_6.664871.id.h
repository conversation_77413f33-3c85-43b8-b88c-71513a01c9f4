#ifndef NCNN_INCLUDE_GUARD_face_landmark_5p_attr_blur_mask_epoch_190_loss_6_664871_id_h
#define NCNN_INCLUDE_GUARD_face_landmark_5p_attr_blur_mask_epoch_190_loss_6_664871_id_h
namespace face_landmark_5p_attr_blur_mask_epoch_190_loss_6_664871_param_id {
const int LAYER_input_1 = 0;
const int BLOB_input_1 = 0;
const int LAYER___backbone__in_conv__in_conv_0_Conv = 1;
const int BLOB___backbone__in_conv__in_conv_0_Conv_output_0 = 1;
const int LAYER___backbone__in_conv__in_conv_2_Relu = 2;
const int BLOB___backbone__in_conv__in_conv_2_Relu_output_0 = 2;
const int LAYER_splitncnn_0 = 3;
const int BLOB___backbone__in_conv__in_conv_2_Relu_output_0_splitncnn_0 = 3;
const int BLOB___backbone__in_conv__in_conv_2_Relu_output_0_splitncnn_1 = 4;
const int LAYER___backbone__block1__block1_0_conv1_conv1_0_Conv = 4;
const int BLOB___backbone__block1__block1_0_conv1_conv1_0_Conv_output_0 = 5;
const int LAYER___backbone__block1__block1_0_conv1_conv1_1_Conv = 5;
const int BLOB___backbone__block1__block1_0_conv1_conv1_1_Conv_output_0 = 6;
const int LAYER___backbone__block1__block1_0_max_pool_MaxPool = 6;
const int BLOB___backbone__block1__block1_0_max_pool_MaxPool_output_0 = 7;
const int LAYER___backbone__block1__block1_0_Add = 7;
const int BLOB___backbone__block1__block1_0_Add_output_0 = 8;
const int LAYER___backbone__block1__block1_0_act_Relu = 8;
const int BLOB___backbone__block1__block1_0_act_Relu_output_0 = 9;
const int LAYER_splitncnn_1 = 9;
const int BLOB___backbone__block1__block1_0_act_Relu_output_0_splitncnn_0 = 10;
const int BLOB___backbone__block1__block1_0_act_Relu_output_0_splitncnn_1 = 11;
const int LAYER___backbone__block1__block1_1_conv1_conv1_0_Conv = 10;
const int BLOB___backbone__block1__block1_1_conv1_conv1_0_Conv_output_0 = 12;
const int LAYER___backbone__block1__block1_1_conv1_conv1_1_Conv = 11;
const int BLOB___backbone__block1__block1_1_conv1_conv1_1_Conv_output_0 = 13;
const int LAYER___backbone__block1__block1_1_Add = 12;
const int BLOB___backbone__block1__block1_1_Add_output_0 = 14;
const int LAYER___backbone__block1__block1_1_act_Relu = 13;
const int BLOB___backbone__block1__block1_1_act_Relu_output_0 = 15;
const int LAYER_splitncnn_2 = 14;
const int BLOB___backbone__block1__block1_1_act_Relu_output_0_splitncnn_0 = 16;
const int BLOB___backbone__block1__block1_1_act_Relu_output_0_splitncnn_1 = 17;
const int LAYER___backbone__block1__block1_2_conv1_conv1_0_Conv = 15;
const int BLOB___backbone__block1__block1_2_conv1_conv1_0_Conv_output_0 = 18;
const int LAYER___backbone__block1__block1_2_conv1_conv1_1_Conv = 16;
const int BLOB___backbone__block1__block1_2_conv1_conv1_1_Conv_output_0 = 19;
const int LAYER___backbone__block1__block1_2_Add = 17;
const int BLOB___backbone__block1__block1_2_Add_output_0 = 20;
const int LAYER___backbone__block1__block1_2_act_Relu = 18;
const int BLOB___backbone__block1__block1_2_act_Relu_output_0 = 21;
const int LAYER_splitncnn_3 = 19;
const int BLOB___backbone__block1__block1_2_act_Relu_output_0_splitncnn_0 = 22;
const int BLOB___backbone__block1__block1_2_act_Relu_output_0_splitncnn_1 = 23;
const int LAYER___backbone__block2__block2_0_conv1_conv1_0_Conv = 20;
const int BLOB___backbone__block2__block2_0_conv1_conv1_0_Conv_output_0 = 24;
const int LAYER___backbone__block2__block2_0_conv1_conv1_1_Conv = 21;
const int BLOB___backbone__block2__block2_0_conv1_conv1_1_Conv_output_0 = 25;
const int LAYER___backbone__block2__block2_0_max_pool_MaxPool = 22;
const int BLOB___backbone__block2__block2_0_max_pool_MaxPool_output_0 = 26;
const int LAYER___backbone__block2__block2_0_skip_conv_skip_conv_0_Conv = 23;
const int BLOB___backbone__block2__block2_0_skip_conv_skip_conv_0_Conv_output_0 = 27;
const int LAYER___backbone__block2__block2_0_Add = 24;
const int BLOB___backbone__block2__block2_0_Add_output_0 = 28;
const int LAYER___backbone__block2__block2_0_act_Relu = 25;
const int BLOB___backbone__block2__block2_0_act_Relu_output_0 = 29;
const int LAYER_splitncnn_4 = 26;
const int BLOB___backbone__block2__block2_0_act_Relu_output_0_splitncnn_0 = 30;
const int BLOB___backbone__block2__block2_0_act_Relu_output_0_splitncnn_1 = 31;
const int LAYER___backbone__block2__block2_1_conv1_conv1_0_Conv = 27;
const int BLOB___backbone__block2__block2_1_conv1_conv1_0_Conv_output_0 = 32;
const int LAYER___backbone__block2__block2_1_conv1_conv1_1_Conv = 28;
const int BLOB___backbone__block2__block2_1_conv1_conv1_1_Conv_output_0 = 33;
const int LAYER___backbone__block2__block2_1_Add = 29;
const int BLOB___backbone__block2__block2_1_Add_output_0 = 34;
const int LAYER___backbone__block2__block2_1_act_Relu = 30;
const int BLOB___backbone__block2__block2_1_act_Relu_output_0 = 35;
const int LAYER_splitncnn_5 = 31;
const int BLOB___backbone__block2__block2_1_act_Relu_output_0_splitncnn_0 = 36;
const int BLOB___backbone__block2__block2_1_act_Relu_output_0_splitncnn_1 = 37;
const int LAYER___backbone__block2__block2_2_conv1_conv1_0_Conv = 32;
const int BLOB___backbone__block2__block2_2_conv1_conv1_0_Conv_output_0 = 38;
const int LAYER___backbone__block2__block2_2_conv1_conv1_1_Conv = 33;
const int BLOB___backbone__block2__block2_2_conv1_conv1_1_Conv_output_0 = 39;
const int LAYER___backbone__block2__block2_2_Add = 34;
const int BLOB___backbone__block2__block2_2_Add_output_0 = 40;
const int LAYER___backbone__block2__block2_2_act_Relu = 35;
const int BLOB___backbone__block2__block2_2_act_Relu_output_0 = 41;
const int LAYER_splitncnn_6 = 36;
const int BLOB___backbone__block2__block2_2_act_Relu_output_0_splitncnn_0 = 42;
const int BLOB___backbone__block2__block2_2_act_Relu_output_0_splitncnn_1 = 43;
const int LAYER___backbone__block3__block3_0_conv1_conv1_0_Conv = 37;
const int BLOB___backbone__block3__block3_0_conv1_conv1_0_Conv_output_0 = 44;
const int LAYER___backbone__block3__block3_0_conv1_conv1_1_Conv = 38;
const int BLOB___backbone__block3__block3_0_conv1_conv1_1_Conv_output_0 = 45;
const int LAYER___backbone__block3__block3_0_conv2_conv2_0_Relu = 39;
const int BLOB___backbone__block3__block3_0_conv2_conv2_0_Relu_output_0 = 46;
const int LAYER___backbone__block3__block3_0_conv2_conv2_1_Conv = 40;
const int BLOB___backbone__block3__block3_0_conv2_conv2_1_Conv_output_0 = 47;
const int LAYER___backbone__block3__block3_0_conv2_conv2_2_Conv = 41;
const int BLOB___backbone__block3__block3_0_conv2_conv2_2_Conv_output_0 = 48;
const int LAYER___backbone__block3__block3_0_max_pool_MaxPool = 42;
const int BLOB___backbone__block3__block3_0_max_pool_MaxPool_output_0 = 49;
const int LAYER___backbone__block3__block3_0_skip_conv_skip_conv_0_Conv = 43;
const int BLOB___backbone__block3__block3_0_skip_conv_skip_conv_0_Conv_output_0 = 50;
const int LAYER___backbone__block3__block3_0_Add = 44;
const int BLOB___backbone__block3__block3_0_Add_output_0 = 51;
const int LAYER___backbone__block3__block3_0_act_Relu = 45;
const int BLOB___backbone__block3__block3_0_act_Relu_output_0 = 52;
const int LAYER_splitncnn_7 = 46;
const int BLOB___backbone__block3__block3_0_act_Relu_output_0_splitncnn_0 = 53;
const int BLOB___backbone__block3__block3_0_act_Relu_output_0_splitncnn_1 = 54;
const int LAYER___backbone__block3__block3_1_conv1_conv1_0_Conv = 47;
const int BLOB___backbone__block3__block3_1_conv1_conv1_0_Conv_output_0 = 55;
const int LAYER___backbone__block3__block3_1_conv1_conv1_1_Conv = 48;
const int BLOB___backbone__block3__block3_1_conv1_conv1_1_Conv_output_0 = 56;
const int LAYER___backbone__block3__block3_1_conv2_conv2_0_Relu = 49;
const int BLOB___backbone__block3__block3_1_conv2_conv2_0_Relu_output_0 = 57;
const int LAYER___backbone__block3__block3_1_conv2_conv2_1_Conv = 50;
const int BLOB___backbone__block3__block3_1_conv2_conv2_1_Conv_output_0 = 58;
const int LAYER___backbone__block3__block3_1_conv2_conv2_2_Conv = 51;
const int BLOB___backbone__block3__block3_1_conv2_conv2_2_Conv_output_0 = 59;
const int LAYER___backbone__block3__block3_1_Add = 52;
const int BLOB___backbone__block3__block3_1_Add_output_0 = 60;
const int LAYER___backbone__block3__block3_1_act_Relu = 53;
const int BLOB___backbone__block3__block3_1_act_Relu_output_0 = 61;
const int LAYER_splitncnn_8 = 54;
const int BLOB___backbone__block3__block3_1_act_Relu_output_0_splitncnn_0 = 62;
const int BLOB___backbone__block3__block3_1_act_Relu_output_0_splitncnn_1 = 63;
const int LAYER___backbone__block3__block3_2_conv1_conv1_0_Conv = 55;
const int BLOB___backbone__block3__block3_2_conv1_conv1_0_Conv_output_0 = 64;
const int LAYER___backbone__block3__block3_2_conv1_conv1_1_Conv = 56;
const int BLOB___backbone__block3__block3_2_conv1_conv1_1_Conv_output_0 = 65;
const int LAYER___backbone__block3__block3_2_conv2_conv2_0_Relu = 57;
const int BLOB___backbone__block3__block3_2_conv2_conv2_0_Relu_output_0 = 66;
const int LAYER___backbone__block3__block3_2_conv2_conv2_1_Conv = 58;
const int BLOB___backbone__block3__block3_2_conv2_conv2_1_Conv_output_0 = 67;
const int LAYER___backbone__block3__block3_2_conv2_conv2_2_Conv = 59;
const int BLOB___backbone__block3__block3_2_conv2_conv2_2_Conv_output_0 = 68;
const int LAYER___backbone__block3__block3_2_Add = 60;
const int BLOB___backbone__block3__block3_2_Add_output_0 = 69;
const int LAYER___backbone__block3__block3_2_act_Relu = 61;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0 = 70;
const int LAYER_splitncnn_9 = 62;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0_splitncnn_0 = 71;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0_splitncnn_1 = 72;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0_splitncnn_2 = 73;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0_splitncnn_3 = 74;
const int BLOB___backbone__block3__block3_2_act_Relu_output_0_splitncnn_4 = 75;
const int LAYER___head_hm_conv_hm_conv_0_Conv = 63;
const int BLOB___head_hm_conv_hm_conv_0_Conv_output_0 = 76;
const int LAYER___head_hm_conv_hm_conv_1_Sigmoid = 64;
const int BLOB_hm = 77;
const int LAYER___head_offset_x_conv_Conv = 65;
const int BLOB___head_offset_x_conv_Conv_output_0 = 78;
const int LAYER___head_Sigmoid = 66;
const int BLOB_offset_x = 79;
const int LAYER___head_offset_y_conv_Conv = 67;
const int BLOB___head_offset_y_conv_Conv_output_0 = 80;
const int LAYER___head_Sigmoid_1 = 68;
const int BLOB_offset_y = 81;
const int LAYER___head_vis_conv_vis_conv_0_Conv = 69;
const int BLOB___head_vis_conv_vis_conv_0_Conv_output_0 = 82;
const int LAYER___head_vis_conv_vis_conv_1_Sigmoid = 70;
const int BLOB_vis = 83;
const int LAYER___head_avg_pool_AveragePool = 71;
const int BLOB___head_avg_pool_AveragePool_output_0 = 84;
const int LAYER_splitncnn_10 = 72;
const int BLOB___head_avg_pool_AveragePool_output_0_splitncnn_0 = 85;
const int BLOB___head_avg_pool_AveragePool_output_0_splitncnn_1 = 86;
const int BLOB___head_avg_pool_AveragePool_output_0_splitncnn_2 = 87;
const int BLOB___head_avg_pool_AveragePool_output_0_splitncnn_3 = 88;
const int LAYER___head_pose_yaw_conv_pose_yaw_conv_0_Conv = 73;
const int BLOB___head_pose_yaw_conv_pose_yaw_conv_0_Conv_output_0 = 89;
const int LAYER___head_pose_yaw_conv_pose_yaw_conv_1_Transpose = 74;
const int BLOB___head_pose_yaw_conv_pose_yaw_conv_1_Transpose_output_0 = 90;
const int LAYER___head_pose_yaw_conv_pose_yaw_conv_1_Softmax = 75;
const int BLOB___head_pose_yaw_conv_pose_yaw_conv_1_Softmax_output_0 = 91;
const int LAYER___head_pose_yaw_conv_pose_yaw_conv_1_Transpose_1 = 76;
const int BLOB_pose_y = 92;
const int LAYER___head_pose_pitch_conv_pose_pitch_conv_0_Conv = 77;
const int BLOB___head_pose_pitch_conv_pose_pitch_conv_0_Conv_output_0 = 93;
const int LAYER___head_pose_pitch_conv_pose_pitch_conv_1_Transpose = 78;
const int BLOB___head_pose_pitch_conv_pose_pitch_conv_1_Transpose_output_0 = 94;
const int LAYER___head_pose_pitch_conv_pose_pitch_conv_1_Softmax = 79;
const int BLOB___head_pose_pitch_conv_pose_pitch_conv_1_Softmax_output_0 = 95;
const int LAYER___head_pose_pitch_conv_pose_pitch_conv_1_Transpose_1 = 80;
const int BLOB_pose_p = 96;
const int LAYER___head_pose_roll_conv_pose_roll_conv_0_Conv = 81;
const int BLOB___head_pose_roll_conv_pose_roll_conv_0_Conv_output_0 = 97;
const int LAYER___head_pose_roll_conv_pose_roll_conv_1_Transpose = 82;
const int BLOB___head_pose_roll_conv_pose_roll_conv_1_Transpose_output_0 = 98;
const int LAYER___head_pose_roll_conv_pose_roll_conv_1_Softmax = 83;
const int BLOB___head_pose_roll_conv_pose_roll_conv_1_Softmax_output_0 = 99;
const int LAYER___head_pose_roll_conv_pose_roll_conv_1_Transpose_1 = 84;
const int BLOB_pose_r = 100;
const int LAYER___head_attr_conv_attr_conv_0_Conv = 85;
const int BLOB___head_attr_conv_attr_conv_0_Conv_output_0 = 101;
const int LAYER___head_attr_conv_attr_conv_1_Sigmoid = 86;
const int BLOB_attr = 102;
} // namespace face_landmark_5p_attr_blur_mask_epoch_190_loss_6_664871_param_id
#endif // NCNN_INCLUDE_GUARD_face_landmark_5p_attr_blur_mask_epoch_190_loss_6_664871_id_h
