7767517
72 88
Input                    in0                      0 1 in0
Convolution              convrelu_0               1 1 in0 1 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=864 9=1
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_48                1 1 3 4 0=32 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=800 7=32
Convolution              conv_4                   1 1 4 5 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024
Pooling                  maxpool2d_26             1 1 2 6 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
BinaryOp                 add_0                    2 1 5 6 7 0=0
ReLU                     relu_30                  1 1 7 8
Split                    splitncnn_1              1 2 8 9 10
ConvolutionDepthWise     convdw_49                1 1 10 11 0=32 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=800 7=32
Convolution              conv_5                   1 1 11 12 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024
BinaryOp                 add_1                    2 1 12 9 13 0=0
ReLU                     relu_31                  1 1 13 14
Split                    splitncnn_2              1 2 14 15 16
ConvolutionDepthWise     convdw_50                1 1 16 17 0=32 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=800 7=32
Convolution              conv_6                   1 1 17 18 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024
BinaryOp                 add_2                    2 1 18 15 19 0=0
ReLU                     relu_32                  1 1 19 20
Split                    splitncnn_3              1 2 20 21 22
ConvolutionDepthWise     convdw_51                1 1 22 23 0=32 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=800 7=32
Convolution              conv_7                   1 1 23 24 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Pooling                  maxpool2d_27             1 1 21 25 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_8                   1 1 25 26 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
BinaryOp                 add_3                    2 1 24 26 27 0=0
ReLU                     relu_33                  1 1 27 28
Split                    splitncnn_4              1 2 28 29 30
ConvolutionDepthWise     convdw_52                1 1 30 31 0=48 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=1200 7=48
Convolution              conv_9                   1 1 31 32 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_4                    2 1 32 29 33 0=0
ReLU                     relu_34                  1 1 33 34
Split                    splitncnn_5              1 2 34 35 36
ConvolutionDepthWise     convdw_53                1 1 36 37 0=48 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=1200 7=48
Convolution              conv_10                  1 1 37 38 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_5                    2 1 38 35 39 0=0
ReLU                     relu_35                  1 1 39 40
Split                    splitncnn_6              1 2 40 41 42
ConvolutionDepthWise     convdw_54                1 1 42 43 0=48 1=5 11=5 12=1 13=2 14=2 2=1 3=2 4=2 5=1 6=1200 7=48
Convolution              convrelu_1               1 1 43 44 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1152 9=1
ConvolutionDepthWise     convdw_55                1 1 44 45 0=24 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=600 7=24
Convolution              conv_12                  1 1 45 46 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_28             1 1 41 47 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_13                  1 1 47 48 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4608
BinaryOp                 add_6                    2 1 46 48 49 0=0
ReLU                     relu_37                  1 1 49 50
Split                    splitncnn_7              1 2 50 51 52
ConvolutionDepthWise     convdw_56                1 1 52 53 0=96 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=2400 7=96
Convolution              convrelu_2               1 1 53 54 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
ConvolutionDepthWise     convdw_57                1 1 54 55 0=24 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=600 7=24
Convolution              conv_15                  1 1 55 56 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_7                    2 1 56 51 57 0=0
ReLU                     relu_39                  1 1 57 58
Split                    splitncnn_8              1 2 58 59 60
ConvolutionDepthWise     convdw_58                1 1 60 61 0=96 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=2400 7=96
Convolution              convrelu_3               1 1 61 62 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
ConvolutionDepthWise     convdw_59                1 1 62 63 0=24 1=5 11=5 12=1 13=1 14=2 2=1 3=1 4=2 5=1 6=600 7=24
Convolution              conv_17                  1 1 63 64 0=96 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_8                    2 1 64 59 65 0=0
ReLU                     relu_41                  1 1 65 66
Split                    splitncnn_9              1 5 66 67 68 69 70 71
Convolution              convsigmoid_4            1 1 71 out0 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=480 9=4
Convolution              convsigmoid_5            1 1 70 out1 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=480 9=4
Convolution              convsigmoid_6            1 1 69 out2 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=480 9=4
Convolution              convsigmoid_7            1 1 68 out3 0=5 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=480 9=4
Pooling                  avgpool2d_2              1 1 67 76 0=1 1=7 11=7 12=7 13=0 2=7 3=0 5=1 6=1
Split                    splitncnn_10             1 4 76 77 78 79 80
Convolution              conv_22                  1 1 80 81 0=60 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5760
Softmax                  softmax_45               1 1 81 out4 0=0 1=1
Convolution              conv_23                  1 1 79 83 0=60 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5760
Softmax                  softmax_46               1 1 83 out5 0=0 1=1
Convolution              conv_24                  1 1 78 85 0=60 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5760
Softmax                  softmax_47               1 1 85 out6 0=0 1=1
Convolution              convsigmoid_8            1 1 77 out7 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=192 9=4
