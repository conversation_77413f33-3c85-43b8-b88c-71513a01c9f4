7767517
87 103
Input            input.1                  0 1 input.1
Convolution      /_backbone/_in_conv/_in_conv.0/Conv 1 1 input.1 /_backbone/_in_conv/_in_conv.0/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=864
ReLU             /_backbone/_in_conv/_in_conv.2/Relu 1 1 /_backbone/_in_conv/_in_conv.0/Conv_output_0 /_backbone/_in_conv/_in_conv.2/Relu_output_0
Split            splitncnn_0              1 2 /_backbone/_in_conv/_in_conv.2/Relu_output_0 /_backbone/_in_conv/_in_conv.2/Relu_output_0_splitncnn_0 /_backbone/_in_conv/_in_conv.2/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block1/_block1.0/conv1/conv1.0/Conv 1 1 /_backbone/_in_conv/_in_conv.2/Relu_output_0_splitncnn_1 /_backbone/_block1/_block1.0/conv1/conv1.0/Conv_output_0 0=32 1=5 11=5 2=1 12=1 3=2 13=2 4=2 14=2 15=2 16=2 5=1 6=800 7=32
Convolution      /_backbone/_block1/_block1.0/conv1/conv1.1/Conv 1 1 /_backbone/_block1/_block1.0/conv1/conv1.0/Conv_output_0 /_backbone/_block1/_block1.0/conv1/conv1.1/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Pooling          /_backbone/_block1/_block1.0/max_pool/MaxPool 1 1 /_backbone/_in_conv/_in_conv.2/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.0/max_pool/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
BinaryOp         /_backbone/_block1/_block1.0/Add 2 1 /_backbone/_block1/_block1.0/conv1/conv1.1/Conv_output_0 /_backbone/_block1/_block1.0/max_pool/MaxPool_output_0 /_backbone/_block1/_block1.0/Add_output_0 0=0
ReLU             /_backbone/_block1/_block1.0/act/Relu 1 1 /_backbone/_block1/_block1.0/Add_output_0 /_backbone/_block1/_block1.0/act/Relu_output_0
Split            splitncnn_1              1 2 /_backbone/_block1/_block1.0/act/Relu_output_0 /_backbone/_block1/_block1.0/act/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.0/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block1/_block1.1/conv1/conv1.0/Conv 1 1 /_backbone/_block1/_block1.0/act/Relu_output_0_splitncnn_1 /_backbone/_block1/_block1.1/conv1/conv1.0/Conv_output_0 0=32 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=800 7=32
Convolution      /_backbone/_block1/_block1.1/conv1/conv1.1/Conv 1 1 /_backbone/_block1/_block1.1/conv1/conv1.0/Conv_output_0 /_backbone/_block1/_block1.1/conv1/conv1.1/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
BinaryOp         /_backbone/_block1/_block1.1/Add 2 1 /_backbone/_block1/_block1.1/conv1/conv1.1/Conv_output_0 /_backbone/_block1/_block1.0/act/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.1/Add_output_0 0=0
ReLU             /_backbone/_block1/_block1.1/act/Relu 1 1 /_backbone/_block1/_block1.1/Add_output_0 /_backbone/_block1/_block1.1/act/Relu_output_0
Split            splitncnn_2              1 2 /_backbone/_block1/_block1.1/act/Relu_output_0 /_backbone/_block1/_block1.1/act/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.1/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block1/_block1.2/conv1/conv1.0/Conv 1 1 /_backbone/_block1/_block1.1/act/Relu_output_0_splitncnn_1 /_backbone/_block1/_block1.2/conv1/conv1.0/Conv_output_0 0=32 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=800 7=32
Convolution      /_backbone/_block1/_block1.2/conv1/conv1.1/Conv 1 1 /_backbone/_block1/_block1.2/conv1/conv1.0/Conv_output_0 /_backbone/_block1/_block1.2/conv1/conv1.1/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
BinaryOp         /_backbone/_block1/_block1.2/Add 2 1 /_backbone/_block1/_block1.2/conv1/conv1.1/Conv_output_0 /_backbone/_block1/_block1.1/act/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.2/Add_output_0 0=0
ReLU             /_backbone/_block1/_block1.2/act/Relu 1 1 /_backbone/_block1/_block1.2/Add_output_0 /_backbone/_block1/_block1.2/act/Relu_output_0
Split            splitncnn_3              1 2 /_backbone/_block1/_block1.2/act/Relu_output_0 /_backbone/_block1/_block1.2/act/Relu_output_0_splitncnn_0 /_backbone/_block1/_block1.2/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block2/_block2.0/conv1/conv1.0/Conv 1 1 /_backbone/_block1/_block1.2/act/Relu_output_0_splitncnn_1 /_backbone/_block2/_block2.0/conv1/conv1.0/Conv_output_0 0=32 1=5 11=5 2=1 12=1 3=2 13=2 4=2 14=2 15=2 16=2 5=1 6=800 7=32
Convolution      /_backbone/_block2/_block2.0/conv1/conv1.1/Conv 1 1 /_backbone/_block2/_block2.0/conv1/conv1.0/Conv_output_0 /_backbone/_block2/_block2.0/conv1/conv1.1/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
Pooling          /_backbone/_block2/_block2.0/max_pool/MaxPool 1 1 /_backbone/_block1/_block1.2/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.0/max_pool/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      /_backbone/_block2/_block2.0/skip_conv/skip_conv.0/Conv 1 1 /_backbone/_block2/_block2.0/max_pool/MaxPool_output_0 /_backbone/_block2/_block2.0/skip_conv/skip_conv.0/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
BinaryOp         /_backbone/_block2/_block2.0/Add 2 1 /_backbone/_block2/_block2.0/conv1/conv1.1/Conv_output_0 /_backbone/_block2/_block2.0/skip_conv/skip_conv.0/Conv_output_0 /_backbone/_block2/_block2.0/Add_output_0 0=0
ReLU             /_backbone/_block2/_block2.0/act/Relu 1 1 /_backbone/_block2/_block2.0/Add_output_0 /_backbone/_block2/_block2.0/act/Relu_output_0
Split            splitncnn_4              1 2 /_backbone/_block2/_block2.0/act/Relu_output_0 /_backbone/_block2/_block2.0/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.0/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block2/_block2.1/conv1/conv1.0/Conv 1 1 /_backbone/_block2/_block2.0/act/Relu_output_0_splitncnn_1 /_backbone/_block2/_block2.1/conv1/conv1.0/Conv_output_0 0=48 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=1200 7=48
Convolution      /_backbone/_block2/_block2.1/conv1/conv1.1/Conv 1 1 /_backbone/_block2/_block2.1/conv1/conv1.0/Conv_output_0 /_backbone/_block2/_block2.1/conv1/conv1.1/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         /_backbone/_block2/_block2.1/Add 2 1 /_backbone/_block2/_block2.1/conv1/conv1.1/Conv_output_0 /_backbone/_block2/_block2.0/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.1/Add_output_0 0=0
ReLU             /_backbone/_block2/_block2.1/act/Relu 1 1 /_backbone/_block2/_block2.1/Add_output_0 /_backbone/_block2/_block2.1/act/Relu_output_0
Split            splitncnn_5              1 2 /_backbone/_block2/_block2.1/act/Relu_output_0 /_backbone/_block2/_block2.1/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.1/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block2/_block2.2/conv1/conv1.0/Conv 1 1 /_backbone/_block2/_block2.1/act/Relu_output_0_splitncnn_1 /_backbone/_block2/_block2.2/conv1/conv1.0/Conv_output_0 0=48 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=1200 7=48
Convolution      /_backbone/_block2/_block2.2/conv1/conv1.1/Conv 1 1 /_backbone/_block2/_block2.2/conv1/conv1.0/Conv_output_0 /_backbone/_block2/_block2.2/conv1/conv1.1/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         /_backbone/_block2/_block2.2/Add 2 1 /_backbone/_block2/_block2.2/conv1/conv1.1/Conv_output_0 /_backbone/_block2/_block2.1/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.2/Add_output_0 0=0
ReLU             /_backbone/_block2/_block2.2/act/Relu 1 1 /_backbone/_block2/_block2.2/Add_output_0 /_backbone/_block2/_block2.2/act/Relu_output_0
Split            splitncnn_6              1 2 /_backbone/_block2/_block2.2/act/Relu_output_0 /_backbone/_block2/_block2.2/act/Relu_output_0_splitncnn_0 /_backbone/_block2/_block2.2/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block3/_block3.0/conv1/conv1.0/Conv 1 1 /_backbone/_block2/_block2.2/act/Relu_output_0_splitncnn_1 /_backbone/_block3/_block3.0/conv1/conv1.0/Conv_output_0 0=48 1=5 11=5 2=1 12=1 3=2 13=2 4=2 14=2 15=2 16=2 5=1 6=1200 7=48
Convolution      /_backbone/_block3/_block3.0/conv1/conv1.1/Conv 1 1 /_backbone/_block3/_block3.0/conv1/conv1.0/Conv_output_0 /_backbone/_block3/_block3.0/conv1/conv1.1/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
ReLU             /_backbone/_block3/_block3.0/conv2/conv2.0/Relu 1 1 /_backbone/_block3/_block3.0/conv1/conv1.1/Conv_output_0 /_backbone/_block3/_block3.0/conv2/conv2.0/Relu_output_0
ConvolutionDepthWise /_backbone/_block3/_block3.0/conv2/conv2.1/Conv 1 1 /_backbone/_block3/_block3.0/conv2/conv2.0/Relu_output_0 /_backbone/_block3/_block3.0/conv2/conv2.1/Conv_output_0 0=24 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=600 7=24
Convolution      /_backbone/_block3/_block3.0/conv2/conv2.2/Conv 1 1 /_backbone/_block3/_block3.0/conv2/conv2.1/Conv_output_0 /_backbone/_block3/_block3.0/conv2/conv2.2/Conv_output_0 0=96 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
Pooling          /_backbone/_block3/_block3.0/max_pool/MaxPool 1 1 /_backbone/_block2/_block2.2/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.0/max_pool/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      /_backbone/_block3/_block3.0/skip_conv/skip_conv.0/Conv 1 1 /_backbone/_block3/_block3.0/max_pool/MaxPool_output_0 /_backbone/_block3/_block3.0/skip_conv/skip_conv.0/Conv_output_0 0=96 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4608
BinaryOp         /_backbone/_block3/_block3.0/Add 2 1 /_backbone/_block3/_block3.0/conv2/conv2.2/Conv_output_0 /_backbone/_block3/_block3.0/skip_conv/skip_conv.0/Conv_output_0 /_backbone/_block3/_block3.0/Add_output_0 0=0
ReLU             /_backbone/_block3/_block3.0/act/Relu 1 1 /_backbone/_block3/_block3.0/Add_output_0 /_backbone/_block3/_block3.0/act/Relu_output_0
Split            splitncnn_7              1 2 /_backbone/_block3/_block3.0/act/Relu_output_0 /_backbone/_block3/_block3.0/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.0/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block3/_block3.1/conv1/conv1.0/Conv 1 1 /_backbone/_block3/_block3.0/act/Relu_output_0_splitncnn_1 /_backbone/_block3/_block3.1/conv1/conv1.0/Conv_output_0 0=96 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=2400 7=96
Convolution      /_backbone/_block3/_block3.1/conv1/conv1.1/Conv 1 1 /_backbone/_block3/_block3.1/conv1/conv1.0/Conv_output_0 /_backbone/_block3/_block3.1/conv1/conv1.1/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             /_backbone/_block3/_block3.1/conv2/conv2.0/Relu 1 1 /_backbone/_block3/_block3.1/conv1/conv1.1/Conv_output_0 /_backbone/_block3/_block3.1/conv2/conv2.0/Relu_output_0
ConvolutionDepthWise /_backbone/_block3/_block3.1/conv2/conv2.1/Conv 1 1 /_backbone/_block3/_block3.1/conv2/conv2.0/Relu_output_0 /_backbone/_block3/_block3.1/conv2/conv2.1/Conv_output_0 0=24 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=600 7=24
Convolution      /_backbone/_block3/_block3.1/conv2/conv2.2/Conv 1 1 /_backbone/_block3/_block3.1/conv2/conv2.1/Conv_output_0 /_backbone/_block3/_block3.1/conv2/conv2.2/Conv_output_0 0=96 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         /_backbone/_block3/_block3.1/Add 2 1 /_backbone/_block3/_block3.1/conv2/conv2.2/Conv_output_0 /_backbone/_block3/_block3.0/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.1/Add_output_0 0=0
ReLU             /_backbone/_block3/_block3.1/act/Relu 1 1 /_backbone/_block3/_block3.1/Add_output_0 /_backbone/_block3/_block3.1/act/Relu_output_0
Split            splitncnn_8              1 2 /_backbone/_block3/_block3.1/act/Relu_output_0 /_backbone/_block3/_block3.1/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.1/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /_backbone/_block3/_block3.2/conv1/conv1.0/Conv 1 1 /_backbone/_block3/_block3.1/act/Relu_output_0_splitncnn_1 /_backbone/_block3/_block3.2/conv1/conv1.0/Conv_output_0 0=96 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=2400 7=96
Convolution      /_backbone/_block3/_block3.2/conv1/conv1.1/Conv 1 1 /_backbone/_block3/_block3.2/conv1/conv1.0/Conv_output_0 /_backbone/_block3/_block3.2/conv1/conv1.1/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             /_backbone/_block3/_block3.2/conv2/conv2.0/Relu 1 1 /_backbone/_block3/_block3.2/conv1/conv1.1/Conv_output_0 /_backbone/_block3/_block3.2/conv2/conv2.0/Relu_output_0
ConvolutionDepthWise /_backbone/_block3/_block3.2/conv2/conv2.1/Conv 1 1 /_backbone/_block3/_block3.2/conv2/conv2.0/Relu_output_0 /_backbone/_block3/_block3.2/conv2/conv2.1/Conv_output_0 0=24 1=5 11=5 2=1 12=1 3=1 13=1 4=2 14=2 15=2 16=2 5=1 6=600 7=24
Convolution      /_backbone/_block3/_block3.2/conv2/conv2.2/Conv 1 1 /_backbone/_block3/_block3.2/conv2/conv2.1/Conv_output_0 /_backbone/_block3/_block3.2/conv2/conv2.2/Conv_output_0 0=96 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         /_backbone/_block3/_block3.2/Add 2 1 /_backbone/_block3/_block3.2/conv2/conv2.2/Conv_output_0 /_backbone/_block3/_block3.1/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.2/Add_output_0 0=0
ReLU             /_backbone/_block3/_block3.2/act/Relu 1 1 /_backbone/_block3/_block3.2/Add_output_0 /_backbone/_block3/_block3.2/act/Relu_output_0
Split            splitncnn_9              1 5 /_backbone/_block3/_block3.2/act/Relu_output_0 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_0 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_2 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_3 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_4
Convolution      /_head/hm_conv/hm_conv.0/Conv 1 1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_4 /_head/hm_conv/hm_conv.0/Conv_output_0 0=5 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=480
Sigmoid          /_head/hm_conv/hm_conv.1/Sigmoid 1 1 /_head/hm_conv/hm_conv.0/Conv_output_0 hm
Convolution      /_head/offset_x_conv/Conv 1 1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_3 /_head/offset_x_conv/Conv_output_0 0=5 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=480
Sigmoid          /_head/Sigmoid           1 1 /_head/offset_x_conv/Conv_output_0 offset_x
Convolution      /_head/offset_y_conv/Conv 1 1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_2 /_head/offset_y_conv/Conv_output_0 0=5 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=480
Sigmoid          /_head/Sigmoid_1         1 1 /_head/offset_y_conv/Conv_output_0 offset_y
Convolution      /_head/vis_conv/vis_conv.0/Conv 1 1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_1 /_head/vis_conv/vis_conv.0/Conv_output_0 0=5 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=480
Sigmoid          /_head/vis_conv/vis_conv.1/Sigmoid 1 1 /_head/vis_conv/vis_conv.0/Conv_output_0 vis
Pooling          /_head/avg_pool/AveragePool 1 1 /_backbone/_block3/_block3.2/act/Relu_output_0_splitncnn_0 /_head/avg_pool/AveragePool_output_0 0=1 1=7 11=7 2=7 12=7 3=0 13=0 14=0 15=0 5=1 6=0
Split            splitncnn_10             1 4 /_head/avg_pool/AveragePool_output_0 /_head/avg_pool/AveragePool_output_0_splitncnn_0 /_head/avg_pool/AveragePool_output_0_splitncnn_1 /_head/avg_pool/AveragePool_output_0_splitncnn_2 /_head/avg_pool/AveragePool_output_0_splitncnn_3
Convolution      /_head/pose_yaw_conv/pose_yaw_conv.0/Conv 1 1 /_head/avg_pool/AveragePool_output_0_splitncnn_3 /_head/pose_yaw_conv/pose_yaw_conv.0/Conv_output_0 0=60 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=5760
Permute          /_head/pose_yaw_conv/pose_yaw_conv.1/Transpose 1 1 /_head/pose_yaw_conv/pose_yaw_conv.0/Conv_output_0 /_head/pose_yaw_conv/pose_yaw_conv.1/Transpose_output_0 0=5
Softmax          /_head/pose_yaw_conv/pose_yaw_conv.1/Softmax 1 1 /_head/pose_yaw_conv/pose_yaw_conv.1/Transpose_output_0 /_head/pose_yaw_conv/pose_yaw_conv.1/Softmax_output_0 0=2 1=1
Permute          /_head/pose_yaw_conv/pose_yaw_conv.1/Transpose_1 1 1 /_head/pose_yaw_conv/pose_yaw_conv.1/Softmax_output_0 pose_y 0=5
Convolution      /_head/pose_pitch_conv/pose_pitch_conv.0/Conv 1 1 /_head/avg_pool/AveragePool_output_0_splitncnn_2 /_head/pose_pitch_conv/pose_pitch_conv.0/Conv_output_0 0=60 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=5760
Permute          /_head/pose_pitch_conv/pose_pitch_conv.1/Transpose 1 1 /_head/pose_pitch_conv/pose_pitch_conv.0/Conv_output_0 /_head/pose_pitch_conv/pose_pitch_conv.1/Transpose_output_0 0=5
Softmax          /_head/pose_pitch_conv/pose_pitch_conv.1/Softmax 1 1 /_head/pose_pitch_conv/pose_pitch_conv.1/Transpose_output_0 /_head/pose_pitch_conv/pose_pitch_conv.1/Softmax_output_0 0=2 1=1
Permute          /_head/pose_pitch_conv/pose_pitch_conv.1/Transpose_1 1 1 /_head/pose_pitch_conv/pose_pitch_conv.1/Softmax_output_0 pose_p 0=5
Convolution      /_head/pose_roll_conv/pose_roll_conv.0/Conv 1 1 /_head/avg_pool/AveragePool_output_0_splitncnn_1 /_head/pose_roll_conv/pose_roll_conv.0/Conv_output_0 0=60 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=5760
Permute          /_head/pose_roll_conv/pose_roll_conv.1/Transpose 1 1 /_head/pose_roll_conv/pose_roll_conv.0/Conv_output_0 /_head/pose_roll_conv/pose_roll_conv.1/Transpose_output_0 0=5
Softmax          /_head/pose_roll_conv/pose_roll_conv.1/Softmax 1 1 /_head/pose_roll_conv/pose_roll_conv.1/Transpose_output_0 /_head/pose_roll_conv/pose_roll_conv.1/Softmax_output_0 0=2 1=1
Permute          /_head/pose_roll_conv/pose_roll_conv.1/Transpose_1 1 1 /_head/pose_roll_conv/pose_roll_conv.1/Softmax_output_0 pose_r 0=5
Convolution      /_head/attr_conv/attr_conv.0/Conv 1 1 /_head/avg_pool/AveragePool_output_0_splitncnn_0 /_head/attr_conv/attr_conv.0/Conv_output_0 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=192
Sigmoid          /_head/attr_conv/attr_conv.1/Sigmoid 1 1 /_head/attr_conv/attr_conv.0/Conv_output_0 attr
