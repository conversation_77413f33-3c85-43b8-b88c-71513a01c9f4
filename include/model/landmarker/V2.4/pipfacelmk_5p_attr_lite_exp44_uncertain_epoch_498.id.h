#ifndef NCNN_INCLUDE_GUARD_pipfacelmk_5p_attr_lite_exp44_uncertain_epoch_498_id_h
#define NCNN_INCLUDE_GUARD_pipfacelmk_5p_attr_lite_exp44_uncertain_epoch_498_id_h
namespace pipfacelmk_5p_attr_lite_exp44_uncertain_epoch_498_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_convrelu_0 = 1;
const int BLOB_1 = 1;
const int LAYER_splitncnn_0 = 2;
const int BLOB_2 = 2;
const int BLOB_3 = 3;
const int LAYER_convdw_42 = 3;
const int BLOB_4 = 4;
const int LAYER_conv_4 = 4;
const int BLOB_5 = 5;
const int LAYER_maxpool2d_24 = 5;
const int BLOB_6 = 6;
const int LAYER_conv_5 = 6;
const int BLOB_7 = 7;
const int LAYER_add_0 = 7;
const int BLOB_8 = 8;
const int LAYER_relu_28 = 8;
const int BLOB_9 = 9;
const int LAYER_splitncnn_1 = 9;
const int BLOB_10 = 10;
const int BLOB_11 = 11;
const int LAYER_convdw_43 = 10;
const int BLOB_12 = 12;
const int LAYER_conv_6 = 11;
const int BLOB_13 = 13;
const int LAYER_add_1 = 12;
const int BLOB_14 = 14;
const int LAYER_relu_29 = 13;
const int BLOB_15 = 15;
const int LAYER_splitncnn_2 = 14;
const int BLOB_16 = 16;
const int BLOB_17 = 17;
const int LAYER_convdw_44 = 15;
const int BLOB_18 = 18;
const int LAYER_conv_7 = 16;
const int BLOB_19 = 19;
const int LAYER_add_2 = 17;
const int BLOB_20 = 20;
const int LAYER_relu_30 = 18;
const int BLOB_21 = 21;
const int LAYER_splitncnn_3 = 19;
const int BLOB_22 = 22;
const int BLOB_23 = 23;
const int LAYER_convdw_45 = 20;
const int BLOB_24 = 24;
const int LAYER_conv_8 = 21;
const int BLOB_25 = 25;
const int LAYER_maxpool2d_25 = 22;
const int BLOB_26 = 26;
const int LAYER_conv_9 = 23;
const int BLOB_27 = 27;
const int LAYER_add_3 = 24;
const int BLOB_28 = 28;
const int LAYER_relu_31 = 25;
const int BLOB_29 = 29;
const int LAYER_splitncnn_4 = 26;
const int BLOB_30 = 30;
const int BLOB_31 = 31;
const int LAYER_convdw_46 = 27;
const int BLOB_32 = 32;
const int LAYER_conv_10 = 28;
const int BLOB_33 = 33;
const int LAYER_add_4 = 29;
const int BLOB_34 = 34;
const int LAYER_relu_32 = 30;
const int BLOB_35 = 35;
const int LAYER_splitncnn_5 = 31;
const int BLOB_36 = 36;
const int BLOB_37 = 37;
const int LAYER_convdw_47 = 32;
const int BLOB_38 = 38;
const int LAYER_conv_11 = 33;
const int BLOB_39 = 39;
const int LAYER_add_5 = 34;
const int BLOB_40 = 40;
const int LAYER_relu_33 = 35;
const int BLOB_41 = 41;
const int LAYER_splitncnn_6 = 36;
const int BLOB_42 = 42;
const int BLOB_43 = 43;
const int LAYER_convdw_48 = 37;
const int BLOB_44 = 44;
const int LAYER_convrelu_1 = 38;
const int BLOB_45 = 45;
const int LAYER_convdw_49 = 39;
const int BLOB_46 = 46;
const int LAYER_conv_13 = 40;
const int BLOB_47 = 47;
const int LAYER_maxpool2d_26 = 41;
const int BLOB_48 = 48;
const int LAYER_conv_14 = 42;
const int BLOB_49 = 49;
const int LAYER_add_6 = 43;
const int BLOB_50 = 50;
const int LAYER_relu_35 = 44;
const int BLOB_51 = 51;
const int LAYER_splitncnn_7 = 45;
const int BLOB_52 = 52;
const int BLOB_53 = 53;
const int LAYER_convdw_50 = 46;
const int BLOB_54 = 54;
const int LAYER_convrelu_2 = 47;
const int BLOB_55 = 55;
const int LAYER_convdw_51 = 48;
const int BLOB_56 = 56;
const int LAYER_conv_16 = 49;
const int BLOB_57 = 57;
const int LAYER_add_7 = 50;
const int BLOB_58 = 58;
const int LAYER_relu_37 = 51;
const int BLOB_59 = 59;
const int LAYER_splitncnn_8 = 52;
const int BLOB_60 = 60;
const int BLOB_61 = 61;
const int LAYER_convdw_52 = 53;
const int BLOB_62 = 62;
const int LAYER_convrelu_3 = 54;
const int BLOB_63 = 63;
const int LAYER_convdw_53 = 55;
const int BLOB_64 = 64;
const int LAYER_conv_18 = 56;
const int BLOB_65 = 65;
const int LAYER_add_8 = 57;
const int BLOB_66 = 66;
const int LAYER_relu_39 = 58;
const int BLOB_67 = 67;
const int LAYER_splitncnn_9 = 59;
const int BLOB_68 = 68;
const int BLOB_69 = 69;
const int BLOB_70 = 70;
const int BLOB_71 = 71;
const int LAYER_convsigmoid_4 = 60;
const int BLOB_out0 = 72;
const int LAYER_convsigmoid_5 = 61;
const int BLOB_out1 = 73;
const int LAYER_convsigmoid_6 = 62;
const int BLOB_out2 = 74;
const int LAYER_avgpool2d_2 = 63;
const int BLOB_75 = 75;
const int LAYER_splitncnn_10 = 64;
const int BLOB_76 = 76;
const int BLOB_77 = 77;
const int LAYER_conv_22 = 65;
const int BLOB_out3 = 78;
const int LAYER_convsigmoid_7 = 66;
const int BLOB_out4 = 79;
} // namespace pipfacelmk_5p_attr_lite_exp44_uncertain_epoch_498_param_id
#endif // NCNN_INCLUDE_GUARD_pipfacelmk_5p_attr_lite_exp44_uncertain_epoch_498_id_h
