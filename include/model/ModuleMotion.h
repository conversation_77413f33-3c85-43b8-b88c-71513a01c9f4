/**  
 * All rights Reserved, Designed By MI
 * @projectName ModuleMotionor
 * @title     motion   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2022/1/12 下午5:31  
 * @version V0.0.0
 * @copyright 2022 <EMAIL>
 */
//

#ifndef MOTION_HPP
#define MOTION_HPP

#include <stdio.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
#include "BSTFaceUnlockConfig.h"

/*error code*/
typedef enum {
    MOTION_OPERATION_SUCCESS = 0,
    /******************init*****************************/
    MOTION_INIT_ERR = (01),
    MOTION_NOT_INIT_ERR = (02),

} ret_code_t;

typedef struct mi_motion_area {
    int x_offset;
    int y_offset;
    int x_width;
    int y_height;
    int stride;
    int threshold;
    float motion_ratio;
    int8_t m_flag; /* 0 No motion 无移动侦测
 *                     1 abs motion 帧差有结果
 *                     2 final motion  经过腐蚀计算后依旧有结果*/

} mi_motion_area_t;


/**
 * 移动侦测算法的输入结构体
 */
typedef struct
{
    int *thresholds;   /**< 移动侦测的灵敏度, 对正常摄像机范围是0-4，对全景摄像机范围是0-8 */
    int height_divide_count;  /*< 需要检测的roi区域坐标信息 */
    int width_divide_count;  /*< 需要检测的roi区域坐标信息 */
    int roi_detects_count;             /*< 需要检测的roi区域数量，范围为0-51，若为0：则不检测，1：检测roiRect 0
														 区域，2、检测roiRect 0,1区域，3、检测roiRect 0,1,2区域，依次类推 */

    float motion_rel_thres;   /**< 移动侦测的阈值，范围为0-1，0.1表示10%的变化 */
} move_param_t;

class ModuleMotion
{
public:
    ModuleMotion();

    ~ModuleMotion(){
        release();
    }


    int motion_cache(move_param_t m_param,uint8_t *frame, int w, int h,  Bbox& box);
    /**
         * @brief 移动侦测
         * @param frame
         * @param roi_results
         * @param m_tracking_info
         * @return
         */
    int motion_detect(uint8_t *frame, int w, int h, Bbox& box,
                      int &motion_result,int g_first_in);
    /**
         * @brief 设置阈值
         * @param thresholds
         * @return
         */
    int set_thresholds(std::vector<int> thresholds);
    int get_areas_count(int &num);
    /**
         * @brief 获取阈值
         * @param thresholds
         * @return
         */
    int get_thresholds(std::vector<int> &thresholds);
    /**
         * @brief 清除缓存
         * @return
         */
    int clear_cache();


    int release();
    /**
         * 获取模型算法库是否初始化成功
         * @return false 未初始化， true 初始化成功
         */
    bool is_initialized() const;

private:
    int region_motion_caculate(uint8_t *src_img,mi_motion_area_t *area_ptr);
    int region_threshold_binary(uint8_t *src_img,uint8_t *dst_img,uint8_t *buf_img,mi_motion_area_t *area_ptr);
    int input_height_;            /**< 帧尺寸信息,只需要配置width和height */
    int input_width_;            /**< 帧尺寸信息,只需要配置width和height */
    int input_image_size;
    int roi_detects_count_;             /*< 需要检测的roi区域数量，范围为0-51，若为0：则不检测，1：检测roiRect 0
														 区域，2、检测roiRect 0,1区域，3、检测roiRect 0,1,2区域，依次类推 */
    bool first_in_status;
    bool is_init;
    uint8_t *cache_frame;
    uint8_t * abs_frame;
    uint8_t * erode_frame;
    std::vector<mi_motion_area_t> roi_detects; /*< 需要检测的roi区域坐标信息 */


};


#endif //MOTION_HPP
