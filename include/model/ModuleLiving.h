/**  
 * All rights Reserved, Designed By MI
 * @projectName bstFaceUnlock
 * @title     ModuleLiving   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 上午1:41  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//

#ifndef BSTFACEUNLOCK_MODULELIVING_H
#define BSTFACEUNLOCK_MODULELIVING_H
#include "net.h"
#include "mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
enum LivingMode{ dark_mode = 1, normal_mode = 2 ,big_angel_mode = 3};

class ModuleLiving {
public:
    ModuleLiving();
    ~ModuleLiving();

    int init(LivingMode mode);
    int perfom(unsigned char* img, int img_w, int img_h, int img_c, BBox& box, float& liv_predicted_score, LmkInfo info,float &living_threshold);
    int release();

private:
    ncnn::Net m_net;
    LivingMode mode_ = normal_mode;

    int net_in_h = 224;
    int net_in_w = 224;
    int net_in_c = 3;

    const float scale[3] = {1 / 127.5f, 1 / 127.5f, 1 / 127.5f};
    const float mean[3]  = {127.5f, 127.5f, 127.5f};
//    const float scale[3] = {1/255.f, 1/255.f, 1/255.f};
//    const float mean[3]  = {0, 0, 0};
};
#endif //BSTFACEUNLOCK_MODULELIVING_H
