#ifndef __MODULE_LANDMARKER_H__
#define __MODULE_LANDMARKER_H__

#include <stdio.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"

struct ModuleLandmarkerParam {
    float iou_thd;
    float conf_thd;
    int   max_scale_num;
    float valid_thr;
    int   net_input_size;

    ModuleLandmarkerParam()
        : iou_thd(0.45f), conf_thd(0.15f), max_scale_num(4), valid_thr(conf_thd), net_input_size(128) {
    }
};

class ModuleLandmarker {
public:
    int  init();
    int  release();
    bool perfom(unsigned char* img, int img_w, int img_h, int c, const BBox& bbox, LmkInfo& info);

private:
    bool decode(std::vector<float*>& p_outs, std::vector<std::vector<int> >& out_shapes, LmkInfo& lmk_info,int indexes_size);

    ModuleLandmarkerParam m_param;
    ncnn::Net             landmark_net;
    const float           scale[3] = {1 / 127.5f, 1 / 127.5f ,1 / 127.5f};
    const float           mean[3]  = {127.5f,127.5f,127.5f};

    int   net_in_c_     = 3;
    int   net_in_w_     = 128;
    int   net_in_h_     = 128;
    int   num_lmk_      = 5;
    float offset_scale_ = 5.0f;
    int   stride_       = 16;
    int   format        = 0;
};

#endif // ifndef __MODULE_DETECT_H__