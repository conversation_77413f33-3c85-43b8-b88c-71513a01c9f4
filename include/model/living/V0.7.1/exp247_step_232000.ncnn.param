7767517
193 221
Input                    in0                      0 1 in0
Convolution              conv_11                  1 1 in0 1 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=864
PReLU                    prelu_50                 1 1 1 2 0=32
ConvolutionDepthWise     convdw_114               1 1 2 3 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=32
PReLU                    prelu_51                 1 1 3 4 0=32
Convolution              conv_12                  1 1 4 5 0=103 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3296
PReLU                    prelu_52                 1 1 5 6 0=103
ConvolutionDepthWise     convdw_115               1 1 6 7 0=103 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=927 7=103
PReLU                    prelu_53                 1 1 7 8 0=103
Convolution              conv_13                  1 1 8 9 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6592
Split                    splitncnn_0              1 2 9 10 11
Convolution              conv_14                  1 1 11 12 0=13 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=832
PReLU                    prelu_54                 1 1 12 13 0=13
ConvolutionDepthWise     convdw_116               1 1 13 14 0=13 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=117 7=13
PReLU                    prelu_55                 1 1 14 15 0=13
Convolution              conv_15                  1 1 15 16 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=832
BinaryOp                 add_0                    2 1 10 16 17 0=0
Split                    splitncnn_1              1 2 17 18 19
Convolution              conv_16                  1 1 19 20 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1664
PReLU                    prelu_56                 1 1 20 21 0=26
ConvolutionDepthWise     convdw_117               1 1 21 22 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_57                 1 1 22 23 0=26
Convolution              conv_17                  1 1 23 24 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1664
BinaryOp                 add_1                    2 1 18 24 25 0=0
Split                    splitncnn_2              1 2 25 26 27
Convolution              conv_18                  1 1 27 28 0=13 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=832
PReLU                    prelu_58                 1 1 28 29 0=13
ConvolutionDepthWise     convdw_118               1 1 29 30 0=13 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=117 7=13
PReLU                    prelu_59                 1 1 30 31 0=13
Convolution              conv_19                  1 1 31 32 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=832
BinaryOp                 add_2                    2 1 26 32 33 0=0
Split                    splitncnn_3              1 2 33 34 35
Convolution              conv_20                  1 1 35 36 0=52 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_60                 1 1 36 37 0=52
ConvolutionDepthWise     convdw_119               1 1 37 38 0=52 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=468 7=52
PReLU                    prelu_61                 1 1 38 39 0=52
Convolution              conv_21                  1 1 39 40 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
Split                    splitncnn_4              1 2 40 41 42
Pooling                  gap_8                    1 1 42 43 0=1 4=1
Convolution              convrelu_0               1 1 43 44 0=16 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024 9=1
Convolution              convsigmoid_3            1 1 44 45 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024 9=4
Reshape                  reshape_89               1 1 45 46 0=1 1=1 2=-1
BinaryOp                 mul_3                    2 1 41 46 47 0=2
BinaryOp                 add_4                    2 1 34 47 48 0=0
Convolution              conv_24                  1 1 48 49 0=231 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=14784
PReLU                    prelu_62                 1 1 49 50 0=231
ConvolutionDepthWise     convdw_120               1 1 50 51 0=231 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2079 7=231
PReLU                    prelu_63                 1 1 51 52 0=231
Convolution              conv_25                  1 1 52 53 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=29568
Split                    splitncnn_5              1 2 53 54 55
Convolution              conv_26                  1 1 55 56 0=154 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=19712
PReLU                    prelu_64                 1 1 56 57 0=154
ConvolutionDepthWise     convdw_121               1 1 57 58 0=154 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1386 7=154
PReLU                    prelu_65                 1 1 58 59 0=154
Convolution              conv_27                  1 1 59 60 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=19712
BinaryOp                 add_5                    2 1 54 60 61 0=0
Split                    splitncnn_6              1 2 61 62 63
Convolution              conv_28                  1 1 63 64 0=52 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6656
PReLU                    prelu_66                 1 1 64 65 0=52
ConvolutionDepthWise     convdw_122               1 1 65 66 0=52 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=468 7=52
PReLU                    prelu_67                 1 1 66 67 0=52
Convolution              conv_29                  1 1 67 68 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6656
BinaryOp                 add_6                    2 1 62 68 69 0=0
Split                    splitncnn_7              1 2 69 70 71
Convolution              conv_30                  1 1 71 72 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_68                 1 1 72 73 0=26
ConvolutionDepthWise     convdw_123               1 1 73 74 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_69                 1 1 74 75 0=26
Convolution              conv_31                  1 1 75 76 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
BinaryOp                 add_7                    2 1 70 76 77 0=0
Split                    splitncnn_8              1 2 77 78 79
Convolution              conv_32                  1 1 79 80 0=52 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6656
PReLU                    prelu_70                 1 1 80 81 0=52
ConvolutionDepthWise     convdw_124               1 1 81 82 0=52 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=468 7=52
PReLU                    prelu_71                 1 1 82 83 0=52
Convolution              conv_33                  1 1 83 84 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6656
BinaryOp                 add_8                    2 1 78 84 85 0=0
Split                    splitncnn_9              1 2 85 86 87
Convolution              conv_34                  1 1 87 88 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_72                 1 1 88 89 0=26
ConvolutionDepthWise     convdw_125               1 1 89 90 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_73                 1 1 90 91 0=26
Convolution              conv_35                  1 1 91 92 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
BinaryOp                 add_9                    2 1 86 92 93 0=0
Split                    splitncnn_10             1 2 93 94 95
Convolution              conv_36                  1 1 95 96 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_74                 1 1 96 97 0=26
ConvolutionDepthWise     convdw_126               1 1 97 98 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_75                 1 1 98 99 0=26
Convolution              conv_37                  1 1 99 100 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
Split                    splitncnn_11             1 2 100 101 102
Pooling                  gap_9                    1 1 102 103 0=1 4=1
Convolution              convrelu_1               1 1 103 104 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=1
Convolution              convsigmoid_4            1 1 104 105 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=4
Reshape                  reshape_90               1 1 105 106 0=1 1=1 2=-1
BinaryOp                 mul_10                   2 1 101 106 107 0=2
BinaryOp                 add_11                   2 1 94 107 108 0=0
Convolution              conv_40                  1 1 108 109 0=308 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=39424
PReLU                    prelu_76                 1 1 109 110 0=308
ConvolutionDepthWise     convdw_127               1 1 110 111 0=308 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2772 7=308
PReLU                    prelu_77                 1 1 111 112 0=308
Convolution              conv_41                  1 1 112 113 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=39424
Split                    splitncnn_12             1 2 113 114 115
Convolution              conv_42                  1 1 115 116 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_78                 1 1 116 117 0=26
ConvolutionDepthWise     convdw_128               1 1 117 118 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_79                 1 1 118 119 0=26
Convolution              conv_43                  1 1 119 120 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
BinaryOp                 add_12                   2 1 114 120 121 0=0
Split                    splitncnn_13             1 2 121 122 123
Convolution              conv_44                  1 1 123 124 0=26 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
PReLU                    prelu_80                 1 1 124 125 0=26
ConvolutionDepthWise     convdw_129               1 1 125 126 0=26 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=234 7=26
PReLU                    prelu_81                 1 1 126 127 0=26
Convolution              conv_45                  1 1 127 128 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3328
Split                    splitncnn_14             1 2 128 129 130
Pooling                  gap_10                   1 1 130 131 0=1 4=1
Convolution              convrelu_2               1 1 131 132 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=1
Convolution              convsigmoid_5            1 1 132 133 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096 9=4
Reshape                  reshape_91               1 1 133 134 0=1 1=1 2=-1
BinaryOp                 mul_13                   2 1 129 134 135 0=2
BinaryOp                 add_14                   2 1 122 135 136 0=0
Convolution              conv_48                  1 1 136 137 0=512 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
PReLU                    prelu_82                 1 1 137 138 0=512
ConvolutionDepthWise     convdw_130               1 1 138 139 0=256 1=14 11=14 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=100352 7=256
Reshape                  view_99                  1 1 139 140 0=256
InnerProduct             linear_49                1 1 140 141 0=256 1=1 2=65536
MemoryData               pnnx_40                  0 1 142 0=40 1=256
Normalize                normalize_0              1 1 141 143 0=1 1=1 2=1.000000e-12 3=1 4=1 9=1
Split                    splitncnn_15             1 7 143 144 145 146 147 148 149 150
MatMul                     mm_100                   2 1 150 142 151
Reshape                  reshape_92               1 1 151 152 0=20 1=2
Split                    splitncnn_16             1 2 152 153 154
BinaryOp                 mul_15                   1 1 153 155 0=2 1=1 2=1.000000e+01
Softmax                  softmax_1                1 1 155 156 0=1 1=1
BinaryOp                 mul_16                   2 1 156 154 157 0=2
Reduction                sum_107                  1 1 157 158 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_67                  0 1 159 0=40 1=256
MatMul                     mm_101                   2 1 149 159 160
Reshape                  reshape_93               1 1 160 161 0=20 1=2
Split                    splitncnn_17             1 2 161 162 163
BinaryOp                 mul_17                   1 1 162 164 0=2 1=1 2=1.000000e+01
Softmax                  softmax_2                1 1 164 165 0=1 1=1
BinaryOp                 mul_18                   2 1 165 163 166 0=2
Reduction                sum_108                  1 1 166 167 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_94                  0 1 168 0=40 1=256
MatMul                     mm_102                   2 1 148 168 169
Reshape                  reshape_94               1 1 169 170 0=20 1=2
Split                    splitncnn_18             1 2 170 171 172
BinaryOp                 mul_19                   1 1 171 173 0=2 1=1 2=1.000000e+01
Softmax                  softmax_3                1 1 173 174 0=1 1=1
BinaryOp                 mul_20                   2 1 174 172 175 0=2
Reduction                sum_109                  1 1 175 176 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_121                 0 1 177 0=40 1=256
MatMul                     mm_103                   2 1 147 177 178
Reshape                  reshape_95               1 1 178 179 0=20 1=2
Split                    splitncnn_19             1 2 179 180 181
BinaryOp                 mul_21                   1 1 180 182 0=2 1=1 2=1.000000e+01
Softmax                  softmax_4                1 1 182 183 0=1 1=1
BinaryOp                 mul_22                   2 1 183 181 184 0=2
Reduction                sum_110                  1 1 184 185 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_148                 0 1 186 0=40 1=256
MatMul                     mm_104                   2 1 146 186 187
Reshape                  reshape_96               1 1 187 188 0=20 1=2
Split                    splitncnn_20             1 2 188 189 190
BinaryOp                 mul_23                   1 1 189 191 0=2 1=1 2=1.000000e+01
Softmax                  softmax_5                1 1 191 192 0=1 1=1
BinaryOp                 mul_24                   2 1 192 190 193 0=2
Reduction                sum_111                  1 1 193 194 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_175                 0 1 195 0=40 1=256
MatMul                     mm_105                   2 1 145 195 196
Reshape                  reshape_97               1 1 196 197 0=20 1=2
Split                    splitncnn_21             1 2 197 198 199
BinaryOp                 mul_25                   1 1 198 200 0=2 1=1 2=1.000000e+01
Softmax                  softmax_6                1 1 200 201 0=1 1=1
BinaryOp                 mul_26                   2 1 201 199 202 0=2
Reduction                sum_112                  1 1 202 203 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_202                 0 1 204 0=40 1=256
MatMul                     mm_106                   2 1 144 204 205
Reshape                  reshape_98               1 1 205 206 0=20 1=2
Split                    splitncnn_22             1 2 206 207 208
BinaryOp                 mul_27                   1 1 207 209 0=2 1=1 2=1.000000e+01
Softmax                  softmax_7                1 1 209 210 0=1 1=1
BinaryOp                 mul_28                   2 1 210 208 211 0=2
Reduction                sum_113                  1 1 211 212 0=0 1=0 -23303=1,1 4=0 5=1
BinaryOp                 add_29                   2 1 158 167 213 0=0
BinaryOp                 add_30                   2 1 213 176 214 0=0
BinaryOp                 add_31                   2 1 214 185 215 0=0
BinaryOp                 add_32                   2 1 215 194 216 0=0
BinaryOp                 add_33                   2 1 216 203 217 0=0
BinaryOp                 add_34                   2 1 217 212 218 0=0
BinaryOp                 mul_35                   1 1 218 219 0=2 1=1 2=3.000000e+01
BinaryOp                 div_36                   1 1 219 out0 0=3 1=1 2=7.000000e+00
