onnx 转 ncnn 需要修改的地方
```shell
Reshape          /backbone/Reshape        1 1 /backbone/conv_6_dw/conv/Conv_output_0 /backbone/Reshape_output_0 0=-1
InnerProduct     /backbone/fc/Gemm        1 1 /backbone/Reshape_output_0 /backbone/fc/Gemm_output_0 0=256 1=1 2=65536
Normalize        /backbone/fc0/Div        1 1 /backbone/fc/Gemm_output_0 /backbone/fc0/Div_output_0 0=1 1=1 2=1.000000e-12 3=1 4=1 9=1
Split            splitncnn_15             1 7 /backbone/fc0/Div_output_0 /backbone/fc0/Div_output_0_splitncnn_0 /backbone/fc0/Div_output_0_splitncnn_1 /backbone/fc0/Div_output_0_splitncnn_2 /backbone/fc0/Div_output_0_splitncnn_3 /backbone/fc0/Div_output_0_splitncnn_4 /backbone/fc0/Div_output_0_splitncnn_5 /backbone/fc0/Div_output_0_splitncnn_6
MatMul             /backbone/fc0/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_6 /backbone/fc0/Constant_1_output_0 /backbone/fc0/Gemm_output_0
Reshape          /backbone/fc0/Reshape    1 1 /backbone/fc0/Gemm_output_0 /backbone/fc0/Reshape_output_0 0=20 1=2
Split            splitncnn_16             1 2 /backbone/fc0/Reshape_output_0 /backbone/fc0/Reshape_output_0_splitncnn_0 /backbone/fc0/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc0/Mul        1 1 /backbone/fc0/Reshape_output_0_splitncnn_1 /backbone/fc0/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc0/Softmax    1 1 /backbone/fc0/Mul_output_0 /backbone/fc0/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc0/Mul_1      2 1 /backbone/fc0/Softmax_output_0 /backbone/fc0/Reshape_output_0_splitncnn_0 /backbone/fc0/Mul_1_output_0 0=2
Reduction        /backbone/fc0/ReduceSum  1 1 /backbone/fc0/Mul_1_output_0 /backbone/fc0/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc1/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_5 /backbone/fc1/Constant_output_0 /backbone/fc1/Gemm_output_0
Reshape          /backbone/fc1/Reshape    1 1 /backbone/fc1/Gemm_output_0 /backbone/fc1/Reshape_output_0 0=20 1=2
Split            splitncnn_17             1 2 /backbone/fc1/Reshape_output_0 /backbone/fc1/Reshape_output_0_splitncnn_0 /backbone/fc1/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc1/Mul        1 1 /backbone/fc1/Reshape_output_0_splitncnn_1 /backbone/fc1/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc1/Softmax    1 1 /backbone/fc1/Mul_output_0 /backbone/fc1/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc1/Mul_1      2 1 /backbone/fc1/Softmax_output_0 /backbone/fc1/Reshape_output_0_splitncnn_0 /backbone/fc1/Mul_1_output_0 0=2
Reduction        /backbone/fc1/ReduceSum  1 1 /backbone/fc1/Mul_1_output_0 /backbone/fc1/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc2/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_4 /backbone/fc2/Constant_output_0 /backbone/fc2/Gemm_output_0
Reshape          /backbone/fc2/Reshape    1 1 /backbone/fc2/Gemm_output_0 /backbone/fc2/Reshape_output_0 0=20 1=2
Split            splitncnn_18             1 2 /backbone/fc2/Reshape_output_0 /backbone/fc2/Reshape_output_0_splitncnn_0 /backbone/fc2/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc2/Mul        1 1 /backbone/fc2/Reshape_output_0_splitncnn_1 /backbone/fc2/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc2/Softmax    1 1 /backbone/fc2/Mul_output_0 /backbone/fc2/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc2/Mul_1      2 1 /backbone/fc2/Softmax_output_0 /backbone/fc2/Reshape_output_0_splitncnn_0 /backbone/fc2/Mul_1_output_0 0=2
Reduction        /backbone/fc2/ReduceSum  1 1 /backbone/fc2/Mul_1_output_0 /backbone/fc2/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc3/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_3 /backbone/fc3/Constant_output_0 /backbone/fc3/Gemm_output_0
Reshape          /backbone/fc3/Reshape    1 1 /backbone/fc3/Gemm_output_0 /backbone/fc3/Reshape_output_0 0=20 1=2
Split            splitncnn_19             1 2 /backbone/fc3/Reshape_output_0 /backbone/fc3/Reshape_output_0_splitncnn_0 /backbone/fc3/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc3/Mul        1 1 /backbone/fc3/Reshape_output_0_splitncnn_1 /backbone/fc3/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc3/Softmax    1 1 /backbone/fc3/Mul_output_0 /backbone/fc3/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc3/Mul_1      2 1 /backbone/fc3/Softmax_output_0 /backbone/fc3/Reshape_output_0_splitncnn_0 /backbone/fc3/Mul_1_output_0 0=2
Reduction        /backbone/fc3/ReduceSum  1 1 /backbone/fc3/Mul_1_output_0 /backbone/fc3/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc4/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_2 /backbone/fc4/Constant_output_0 /backbone/fc4/Gemm_output_0
Reshape          /backbone/fc4/Reshape    1 1 /backbone/fc4/Gemm_output_0 /backbone/fc4/Reshape_output_0 0=20 1=2
Split            splitncnn_20             1 2 /backbone/fc4/Reshape_output_0 /backbone/fc4/Reshape_output_0_splitncnn_0 /backbone/fc4/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc4/Mul        1 1 /backbone/fc4/Reshape_output_0_splitncnn_1 /backbone/fc4/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc4/Softmax    1 1 /backbone/fc4/Mul_output_0 /backbone/fc4/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc4/Mul_1      2 1 /backbone/fc4/Softmax_output_0 /backbone/fc4/Reshape_output_0_splitncnn_0 /backbone/fc4/Mul_1_output_0 0=2
Reduction        /backbone/fc4/ReduceSum  1 1 /backbone/fc4/Mul_1_output_0 /backbone/fc4/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc5/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_1 /backbone/fc5/Constant_output_0 /backbone/fc5/Gemm_output_0
Reshape          /backbone/fc5/Reshape    1 1 /backbone/fc5/Gemm_output_0 /backbone/fc5/Reshape_output_0 0=20 1=2
Split            splitncnn_21             1 2 /backbone/fc5/Reshape_output_0 /backbone/fc5/Reshape_output_0_splitncnn_0 /backbone/fc5/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc5/Mul        1 1 /backbone/fc5/Reshape_output_0_splitncnn_1 /backbone/fc5/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc5/Softmax    1 1 /backbone/fc5/Mul_output_0 /backbone/fc5/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc5/Mul_1      2 1 /backbone/fc5/Softmax_output_0 /backbone/fc5/Reshape_output_0_splitncnn_0 /backbone/fc5/Mul_1_output_0 0=2
Reduction        /backbone/fc5/ReduceSum  1 1 /backbone/fc5/Mul_1_output_0 /backbone/fc5/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc6/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_0 /backbone/fc6/Constant_output_0 /backbone/fc6/Gemm_output_0
Reshape          /backbone/fc6/Reshape    1 1 /backbone/fc6/Gemm_output_0 /backbone/fc6/Reshape_output_0 0=20 1=2
Split            splitncnn_22             1 2 /backbone/fc6/Reshape_output_0 /backbone/fc6/Reshape_output_0_splitncnn_0 /backbone/fc6/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc6/Mul        1 1 /backbone/fc6/Reshape_output_0_splitncnn_1 /backbone/fc6/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc6/Softmax    1 1 /backbone/fc6/Mul_output_0 /backbone/fc6/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc6/Mul_1      2 1 /backbone/fc6/Softmax_output_0 /backbone/fc6/Reshape_output_0_splitncnn_0 /backbone/fc6/Mul_1_output_0 0=2
Reduction        /backbone/fc6/ReduceSum  1 1 /backbone/fc6/Mul_1_output_0 /backbone/fc6/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
BinaryOp         /backbone/Add            2 1 /backbone/fc0/ReduceSum_output_0 /backbone/fc1/ReduceSum_output_0 /backbone/Add_output_0 0=0
BinaryOp         /backbone/Add_1          2 1 /backbone/Add_output_0 /backbone/fc2/ReduceSum_output_0 /backbone/Add_1_output_0 0=0
BinaryOp         /backbone/Add_2          2 1 /backbone/Add_1_output_0 /backbone/fc3/ReduceSum_output_0 /backbone/Add_2_output_0 0=0
BinaryOp         /backbone/Add_3          2 1 /backbone/Add_2_output_0 /backbone/fc4/ReduceSum_output_0 /backbone/Add_3_output_0 0=0
BinaryOp         /backbone/Add_4          2 1 /backbone/Add_3_output_0 /backbone/fc5/ReduceSum_output_0 /backbone/Add_4_output_0 0=0
BinaryOp         /backbone/Add_5          2 1 /backbone/Add_4_output_0 /backbone/fc6/ReduceSum_output_0 /backbone/Add_5_output_0 0=0
BinaryOp         /backbone/Mul            1 1 /backbone/Add_5_output_0 /backbone/Mul_output_0 0=2 1=1 2=3.000000e+01
BinaryOp         /backbone/Div            1 1 /backbone/Mul_output_0 664 0=3 1=1 2=7.000000e+00

```
中间的核心两部分需要修改
1. Gemm 替换MatMul 并删除后面的参数
2. Normalize的输出参数
   
   1=1 2=1.000000e-12 3=1 9=1
   替换为
   0=1 1=1 2=1.000000e-12 3=1 4=1 9=1


/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.onnx /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.param /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.onnx /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.param /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11/exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.bin

export PATH="/media/shining/work/work/code/ncnn-20230223/build/tools":${PATH}
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.11
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.4.7
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.13
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.14
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.15
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.16
cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/living/V0.5.17


/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.onnx exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.param exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp261_step_248000_sim.onnx exp261_step_248000_sim.param exp261_step_248000_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.onnx exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.param exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.onnx exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.param exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.onnx exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.param exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.onnx exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.param exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.bin

/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.onnx exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.param exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.bin



ncnn2mem exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.param exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.bin exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.id.h exp334_step_64000_eer_val_items_O220.8696_val_normal0.9081_val_dark0.8350_ce_camera0.8665acc_0.869586_sim.mem.h
ncnn2mem exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.param exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.bin exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.id.h exp339_step_556000_eer_val_normal0.9659_val_dark0.6968acc_0.965896_sim.mem.h
ncnn2mem step_88000_eer_weak+kaggleMaskReal0.8892_weak0.9419_hard0.8229_eval_haibo0.9516_multiframe_3frame_v20.7638_ce_camera0.8999acc_0.889181_sim.param step_88000_eer_weak+kaggleMaskReal0.8892_weak0.9419_hard0.8229_eval_haibo0.9516_multiframe_3frame_v20.7638_ce_camera0.8999acc_0.889181_sim.bin step_88000_eer_weak+kaggleMaskReal0.8892_weak0.9419_hard0.8229_eval_haibo0.9516_multiframe_3frame_v20.7638_ce_camera0.8999acc_0.889181_sim.id.h step_88000_eer_weak+kaggleMaskReal0.8892_weak0.9419_hard0.8229_eval_haibo0.9516_multiframe_3frame_v20.7638_ce_camera0.8999acc_0.889181_sim.mem.h
ncnn2mem exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.param exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.bin exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.id.h exp382_step_216000_eer_val_normal0.9670_val_dark0.6825acc_0.967001_sim.mem.h
ncnn2mem exp261_step_248000_sim.param exp261_step_248000_sim.bin exp261_step_248000_sim.id.h exp261_step_248000_sim.mem.h
ncnn2mem exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.param exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.bin exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.id.h exp388_step_104000_eer_val_normal0.9432_val_dark0.7893acc_0.943230_sim.mem.h
ncnn2mem exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.param exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.bin exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.id.h exp388_step_144000_eer_val_normal0.9490_val_dark0.7648acc_0.949013_sim.mem.h
ncnn2mem exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.param exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.bin exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.id.h exp397_step_80000_eer_val_normal0.9274_val_dark0.7512_val_U6550.9425_val_U6550.9425_val_SN509A0.9625_val_W30.9772_val_SL219A0.9322acc_0.927404_sim.mem.h
ncnn2mem exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.param exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.bin exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.id.h exp397_step_104000_eer_val_normal0.8908_val_dark0.6800_val_U6550.9113_val_U6550.9113_val_SN509A0.9763_val_W30.9775_val_SL219A0.9091acc_0.890766_sim.mem.h
ncnn2mem exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.param exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.bin exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.id.h exp401_step_53366_eer_val_normal0.9424_val_dark0.6943_val_U6550.9406_val_SN509A0.9655_val_W30.9781_val_SL219A0.9959acc_0.942417_sim.mem.h
