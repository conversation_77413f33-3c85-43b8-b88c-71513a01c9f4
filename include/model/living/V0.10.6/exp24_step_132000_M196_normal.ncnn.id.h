#ifndef NCNN_INCLUDE_GUARD_exp24_step_132000_M196_normal_ncnn_id_h
#define NCNN_INCLUDE_GUARD_exp24_step_132000_M196_normal_ncnn_id_h
namespace exp24_step_132000_M196_normal_ncnn_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_conv_6 = 1;
const int BLOB_1 = 1;
const int LAYER_silu_23 = 2;
const int BLOB_2 = 2;
const int LAYER_conv_7 = 3;
const int BLOB_3 = 3;
const int LAYER_silu_24 = 4;
const int BLOB_4 = 4;
const int LAYER_conv_8 = 5;
const int BLOB_5 = 5;
const int LAYER_silu_25 = 6;
const int BLOB_6 = 6;
const int LAYER_splitncnn_0 = 7;
const int BLOB_7 = 7;
const int BLOB_8 = 8;
const int LAYER_slice_0 = 8;
const int BLOB_9 = 9;
const int LAYER_splitncnn_1 = 9;
const int BLOB_10 = 10;
const int BLOB_11 = 11;
const int LAYER_convdw_74 = 10;
const int BLOB_12 = 12;
const int LAYER_silu_26 = 11;
const int BLOB_13 = 13;
const int LAYER_convdw_75 = 12;
const int BLOB_14 = 14;
const int LAYER_silu_27 = 13;
const int BLOB_15 = 15;
const int LAYER_add_0 = 14;
const int BLOB_16 = 16;
const int LAYER_cat_0 = 15;
const int BLOB_17 = 17;
const int LAYER_conv_9 = 16;
const int BLOB_18 = 18;
const int LAYER_silu_28 = 17;
const int BLOB_19 = 19;
const int LAYER_convdw_76 = 18;
const int BLOB_20 = 20;
const int LAYER_silu_29 = 19;
const int BLOB_21 = 21;
const int LAYER_conv_10 = 20;
const int BLOB_22 = 22;
const int LAYER_silu_30 = 21;
const int BLOB_23 = 23;
const int LAYER_splitncnn_2 = 22;
const int BLOB_24 = 24;
const int BLOB_25 = 25;
const int LAYER_slice_1 = 23;
const int BLOB_26 = 26;
const int LAYER_splitncnn_3 = 24;
const int BLOB_27 = 27;
const int BLOB_28 = 28;
const int LAYER_convdw_77 = 25;
const int BLOB_29 = 29;
const int LAYER_silu_31 = 26;
const int BLOB_30 = 30;
const int LAYER_convdw_78 = 27;
const int BLOB_31 = 31;
const int LAYER_silu_32 = 28;
const int BLOB_32 = 32;
const int LAYER_add_1 = 29;
const int BLOB_33 = 33;
const int LAYER_cat_1 = 30;
const int BLOB_34 = 34;
const int LAYER_conv_11 = 31;
const int BLOB_35 = 35;
const int LAYER_silu_33 = 32;
const int BLOB_36 = 36;
const int LAYER_convdw_79 = 33;
const int BLOB_37 = 37;
const int LAYER_silu_34 = 34;
const int BLOB_38 = 38;
const int LAYER_conv_12 = 35;
const int BLOB_39 = 39;
const int LAYER_silu_35 = 36;
const int BLOB_40 = 40;
const int LAYER_splitncnn_4 = 37;
const int BLOB_41 = 41;
const int BLOB_42 = 42;
const int LAYER_slice_2 = 38;
const int BLOB_43 = 43;
const int LAYER_splitncnn_5 = 39;
const int BLOB_44 = 44;
const int BLOB_45 = 45;
const int LAYER_convdw_80 = 40;
const int BLOB_46 = 46;
const int LAYER_silu_36 = 41;
const int BLOB_47 = 47;
const int LAYER_splitncnn_6 = 42;
const int BLOB_48 = 48;
const int BLOB_49 = 49;
const int LAYER_convdw_81 = 43;
const int BLOB_50 = 50;
const int LAYER_silu_37 = 44;
const int BLOB_51 = 51;
const int LAYER_convdw_82 = 45;
const int BLOB_52 = 52;
const int LAYER_silu_38 = 46;
const int BLOB_53 = 53;
const int LAYER_add_2 = 47;
const int BLOB_54 = 54;
const int LAYER_splitncnn_7 = 48;
const int BLOB_55 = 55;
const int BLOB_56 = 56;
const int LAYER_convdw_83 = 49;
const int BLOB_57 = 57;
const int LAYER_silu_39 = 50;
const int BLOB_58 = 58;
const int LAYER_convdw_84 = 51;
const int BLOB_59 = 59;
const int LAYER_silu_40 = 52;
const int BLOB_60 = 60;
const int LAYER_add_3 = 53;
const int BLOB_61 = 61;
const int LAYER_convdw_85 = 54;
const int BLOB_62 = 62;
const int LAYER_silu_41 = 55;
const int BLOB_63 = 63;
const int LAYER_cat_2 = 56;
const int BLOB_64 = 64;
const int LAYER_convdw_86 = 57;
const int BLOB_65 = 65;
const int LAYER_silu_42 = 58;
const int BLOB_66 = 66;
const int LAYER_cat_3 = 59;
const int BLOB_67 = 67;
const int LAYER_conv_13 = 60;
const int BLOB_68 = 68;
const int LAYER_silu_43 = 61;
const int BLOB_69 = 69;
const int LAYER_convdw_87 = 62;
const int BLOB_70 = 70;
const int LAYER_silu_44 = 63;
const int BLOB_71 = 71;
const int LAYER_conv_14 = 64;
const int BLOB_72 = 72;
const int LAYER_silu_45 = 65;
const int BLOB_73 = 73;
const int LAYER_splitncnn_8 = 66;
const int BLOB_74 = 74;
const int BLOB_75 = 75;
const int LAYER_slice_3 = 67;
const int BLOB_76 = 76;
const int LAYER_splitncnn_9 = 68;
const int BLOB_77 = 77;
const int BLOB_78 = 78;
const int LAYER_convdw_88 = 69;
const int BLOB_79 = 79;
const int LAYER_silu_46 = 70;
const int BLOB_80 = 80;
const int LAYER_splitncnn_10 = 71;
const int BLOB_81 = 81;
const int BLOB_82 = 82;
const int LAYER_convdw_89 = 72;
const int BLOB_83 = 83;
const int LAYER_silu_47 = 73;
const int BLOB_84 = 84;
const int LAYER_convdw_90 = 74;
const int BLOB_85 = 85;
const int LAYER_silu_48 = 75;
const int BLOB_86 = 86;
const int LAYER_add_4 = 76;
const int BLOB_87 = 87;
const int LAYER_splitncnn_11 = 77;
const int BLOB_88 = 88;
const int BLOB_89 = 89;
const int LAYER_convdw_91 = 78;
const int BLOB_90 = 90;
const int LAYER_silu_49 = 79;
const int BLOB_91 = 91;
const int LAYER_convdw_92 = 80;
const int BLOB_92 = 92;
const int LAYER_silu_50 = 81;
const int BLOB_93 = 93;
const int LAYER_add_5 = 82;
const int BLOB_94 = 94;
const int LAYER_convdw_93 = 83;
const int BLOB_95 = 95;
const int LAYER_silu_51 = 84;
const int BLOB_96 = 96;
const int LAYER_cat_4 = 85;
const int BLOB_97 = 97;
const int LAYER_convdw_94 = 86;
const int BLOB_98 = 98;
const int LAYER_silu_52 = 87;
const int BLOB_99 = 99;
const int LAYER_cat_5 = 88;
const int BLOB_100 = 100;
const int LAYER_conv_15 = 89;
const int BLOB_101 = 101;
const int LAYER_silu_53 = 90;
const int BLOB_102 = 102;
const int LAYER_conv_16 = 91;
const int BLOB_103 = 103;
const int LAYER_silu_54 = 92;
const int BLOB_104 = 104;
const int LAYER_split_0 = 93;
const int BLOB_105 = 105;
const int BLOB_106 = 106;
const int LAYER_splitncnn_12 = 94;
const int BLOB_107 = 107;
const int BLOB_108 = 108;
const int LAYER_conv_17 = 95;
const int BLOB_109 = 109;
const int LAYER_view_61 = 96;
const int BLOB_110 = 110;
const int LAYER_split_1 = 97;
const int BLOB_111 = 111;
const int BLOB_112 = 112;
const int BLOB_113 = 113;
const int LAYER_splitncnn_13 = 98;
const int BLOB_114 = 114;
const int BLOB_115 = 115;
const int LAYER_transpose_72 = 99;
const int BLOB_116 = 116;
const int LAYER_matmul_64 = 100;
const int BLOB_117 = 117;
const int LAYER_mul_6 = 101;
const int BLOB_118 = 118;
const int LAYER_softmax_1 = 102;
const int BLOB_119 = 119;
const int LAYER_matmultransb_0 = 103;
const int BLOB_120 = 120;
const int LAYER_view_62 = 104;
const int BLOB_121 = 121;
const int LAYER_reshape_57 = 105;
const int BLOB_122 = 122;
const int LAYER_convdw_95 = 106;
const int BLOB_123 = 123;
const int LAYER_add_7 = 107;
const int BLOB_124 = 124;
const int LAYER_conv_18 = 108;
const int BLOB_125 = 125;
const int LAYER_add_8 = 109;
const int BLOB_126 = 126;
const int LAYER_splitncnn_14 = 110;
const int BLOB_127 = 127;
const int BLOB_128 = 128;
const int LAYER_conv_19 = 111;
const int BLOB_129 = 129;
const int LAYER_silu_55 = 112;
const int BLOB_130 = 130;
const int LAYER_conv_20 = 113;
const int BLOB_131 = 131;
const int LAYER_add_9 = 114;
const int BLOB_132 = 132;
const int LAYER_cat_6 = 115;
const int BLOB_133 = 133;
const int LAYER_conv_21 = 116;
const int BLOB_134 = 134;
const int LAYER_silu_56 = 117;
const int BLOB_135 = 135;
const int LAYER_gap_5 = 118;
const int BLOB_136 = 136;
const int LAYER_view_63 = 119;
const int BLOB_137 = 137;
const int LAYER_linear_22 = 120;
const int BLOB_138 = 138;
const int LAYER_pnnx_153 = 121;
const int BLOB_139 = 139;
const int LAYER_normalize_0 = 122;
const int BLOB_140 = 140;
const int LAYER_splitncnn_15 = 123;
const int BLOB_141 = 141;
const int BLOB_142 = 142;
const int BLOB_143 = 143;
const int LAYER_mm_66 = 124;
const int BLOB_144 = 144;
const int LAYER_reshape_58 = 125;
const int BLOB_145 = 145;
const int LAYER_splitncnn_16 = 126;
const int BLOB_146 = 146;
const int BLOB_147 = 147;
const int LAYER_mul_10 = 127;
const int BLOB_148 = 148;
const int LAYER_softmax_2 = 128;
const int BLOB_149 = 149;
const int LAYER_mul_11 = 129;
const int BLOB_150 = 150;
const int LAYER_sum_69 = 130;
const int BLOB_151 = 151;
const int LAYER_pnnx_180 = 131;
const int BLOB_152 = 152;
const int LAYER_mm_67 = 132;
const int BLOB_153 = 153;
const int LAYER_reshape_59 = 133;
const int BLOB_154 = 154;
const int LAYER_splitncnn_17 = 134;
const int BLOB_155 = 155;
const int BLOB_156 = 156;
const int LAYER_mul_12 = 135;
const int BLOB_157 = 157;
const int LAYER_softmax_3 = 136;
const int BLOB_158 = 158;
const int LAYER_mul_13 = 137;
const int BLOB_159 = 159;
const int LAYER_sum_70 = 138;
const int BLOB_160 = 160;
const int LAYER_pnnx_207 = 139;
const int BLOB_161 = 161;
const int LAYER_mm_68 = 140;
const int BLOB_162 = 162;
const int LAYER_reshape_60 = 141;
const int BLOB_163 = 163;
const int LAYER_splitncnn_18 = 142;
const int BLOB_164 = 164;
const int BLOB_165 = 165;
const int LAYER_mul_14 = 143;
const int BLOB_166 = 166;
const int LAYER_softmax_4 = 144;
const int BLOB_167 = 167;
const int LAYER_mul_15 = 145;
const int BLOB_168 = 168;
const int LAYER_sum_71 = 146;
const int BLOB_169 = 169;
const int LAYER_add_16 = 147;
const int BLOB_170 = 170;
const int LAYER_add_17 = 148;
const int BLOB_171 = 171;
const int LAYER_mul_18 = 149;
const int BLOB_172 = 172;
const int LAYER_div_19 = 150;
const int BLOB_out0 = 173;
} // namespace exp24_step_132000_M196_normal_ncnn_param_id
#endif // NCNN_INCLUDE_GUARD_exp24_step_132000_M196_normal_ncnn_id_h
