7767517
151 174
Input                    in0                      0 1 in0
Convolution              conv_6                   1 1 in0 1 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=864
Swish                    silu_23                  1 1 1 2
Convolution              conv_7                   1 1 2 3 0=32 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=9216
Swish                    silu_24                  1 1 3 4
Convolution              conv_8                   1 1 4 5 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1024
Swish                    silu_25                  1 1 5 6
Split                    splitncnn_0              1 2 6 7 8
Crop                     slice_0                  1 1 8 9 -23310=1,-233 -23311=1,0 -23309=1,16
Split                    splitncnn_1              1 2 9 10 11
ConvolutionDepthWise     convdw_74                1 1 11 12 0=8 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=144 7=8
Swish                    silu_26                  1 1 12 13
ConvolutionDepthWise     convdw_75                1 1 13 14 0=16 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=144 7=8
Swish                    silu_27                  1 1 14 15
BinaryOp                 add_0                    2 1 10 15 16 0=0
Concat                   cat_0                    2 1 7 16 17 0=0
Convolution              conv_9                   1 1 17 18 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3072
Swish                    silu_28                  1 1 18 19
ConvolutionDepthWise     convdw_76                1 1 19 20 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=576 7=64
Swish                    silu_29                  1 1 20 21
Convolution              conv_10                  1 1 21 22 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
Swish                    silu_30                  1 1 22 23
Split                    splitncnn_2              1 2 23 24 25
Crop                     slice_1                  1 1 25 26 -23310=1,-233 -23311=1,0 -23309=1,32
Split                    splitncnn_3              1 2 26 27 28
ConvolutionDepthWise     convdw_77                1 1 28 29 0=16 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=16
Swish                    silu_31                  1 1 29 30
ConvolutionDepthWise     convdw_78                1 1 30 31 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=16
Swish                    silu_32                  1 1 31 32
BinaryOp                 add_1                    2 1 27 32 33 0=0
Concat                   cat_1                    2 1 24 33 34 0=0
Convolution              conv_11                  1 1 34 35 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=12288
Swish                    silu_33                  1 1 35 36
ConvolutionDepthWise     convdw_79                1 1 36 37 0=128 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=1152 7=128
Swish                    silu_34                  1 1 37 38
Convolution              conv_12                  1 1 38 39 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
Swish                    silu_35                  1 1 39 40
Split                    splitncnn_4              1 2 40 41 42
Crop                     slice_2                  1 1 42 43 -23310=1,-233 -23311=1,0 -23309=1,64
Split                    splitncnn_5              1 2 43 44 45
ConvolutionDepthWise     convdw_80                1 1 45 46 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=64 7=32
Swish                    silu_36                  1 1 46 47
Split                    splitncnn_6              1 2 47 48 49
ConvolutionDepthWise     convdw_81                1 1 49 50 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=32
Swish                    silu_37                  1 1 50 51
ConvolutionDepthWise     convdw_82                1 1 51 52 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=32
Swish                    silu_38                  1 1 52 53
BinaryOp                 add_2                    2 1 48 53 54 0=0
Split                    splitncnn_7              1 2 54 55 56
ConvolutionDepthWise     convdw_83                1 1 56 57 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=32
Swish                    silu_39                  1 1 57 58
ConvolutionDepthWise     convdw_84                1 1 58 59 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=288 7=32
Swish                    silu_40                  1 1 59 60
BinaryOp                 add_3                    2 1 55 60 61 0=0
ConvolutionDepthWise     convdw_85                1 1 44 62 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=64 7=32
Swish                    silu_41                  1 1 62 63
Concat                   cat_2                    2 1 61 63 64 0=0
ConvolutionDepthWise     convdw_86                1 1 64 65 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=64 7=64
Swish                    silu_42                  1 1 65 66
Concat                   cat_3                    2 1 41 66 67 0=0
Convolution              conv_13                  1 1 67 68 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=24576
Swish                    silu_43                  1 1 68 69
ConvolutionDepthWise     convdw_87                1 1 69 70 0=256 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=2304 7=128
Swish                    silu_44                  1 1 70 71
Convolution              conv_14                  1 1 71 72 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_45                  1 1 72 73
Split                    splitncnn_8              1 2 73 74 75
Crop                     slice_3                  1 1 75 76 -23310=1,-233 -23311=1,0 -23309=1,128
Split                    splitncnn_9              1 2 76 77 78
ConvolutionDepthWise     convdw_88                1 1 78 79 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128 7=64
Swish                    silu_46                  1 1 79 80
Split                    splitncnn_10             1 2 80 81 82
ConvolutionDepthWise     convdw_89                1 1 82 83 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Swish                    silu_47                  1 1 83 84
ConvolutionDepthWise     convdw_90                1 1 84 85 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Swish                    silu_48                  1 1 85 86
BinaryOp                 add_4                    2 1 81 86 87 0=0
Split                    splitncnn_11             1 2 87 88 89
ConvolutionDepthWise     convdw_91                1 1 89 90 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Swish                    silu_49                  1 1 90 91
ConvolutionDepthWise     convdw_92                1 1 91 92 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Swish                    silu_50                  1 1 92 93
BinaryOp                 add_5                    2 1 88 93 94 0=0
ConvolutionDepthWise     convdw_93                1 1 77 95 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128 7=64
Swish                    silu_51                  1 1 95 96
Concat                   cat_4                    2 1 94 96 97 0=0
ConvolutionDepthWise     convdw_94                1 1 97 98 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=128 7=128
Swish                    silu_52                  1 1 98 99
Concat                   cat_5                    2 1 74 99 100 0=0
Convolution              conv_15                  1 1 100 101 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=98304
Swish                    silu_53                  1 1 101 102
Convolution              conv_16                  1 1 102 103 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_54                  1 1 103 104
Slice                    split_0                  1 2 104 105 106 -23300=2,128,128 1=0
Split                    splitncnn_12             1 2 106 107 108
Convolution              conv_17                  1 1 108 109 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Reshape                  view_61                  1 1 109 110 0=49 1=128 2=2
Slice                    split_1                  1 3 110 111 112 113 -23300=3,32,32,64 1=1
Split                    splitncnn_13             1 2 113 114 115
Permute                  transpose_72             1 1 111 116 0=1
MatMul                   matmul_64                2 1 116 112 117
BinaryOp                 mul_6                    1 1 117 118 0=2 1=1 2=1.767767e-01
Softmax                  softmax_1                1 1 118 119 0=2 1=1
MatMul                   matmultransb_0           2 1 115 119 120 0=1
Reshape                  view_62                  1 1 120 121 0=7 1=7 2=128
Reshape                  reshape_57               1 1 114 122 0=7 1=7 2=128
ConvolutionDepthWise     convdw_95                1 1 122 123 0=128 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=1152 7=128
BinaryOp                 add_7                    2 1 121 123 124 0=0
Convolution              conv_18                  1 1 124 125 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=16384
BinaryOp                 add_8                    2 1 107 125 126 0=0
Split                    splitncnn_14             1 2 126 127 128
Convolution              conv_19                  1 1 128 129 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
Swish                    silu_55                  1 1 129 130
Convolution              conv_20                  1 1 130 131 0=128 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=32768
BinaryOp                 add_9                    2 1 127 131 132 0=0
Concat                   cat_6                    2 1 105 132 133 0=0
Convolution              conv_21                  1 1 133 134 0=256 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=65536
Swish                    silu_56                  1 1 134 135
Pooling                  gap_5                    1 1 135 136 0=1 4=1
Reshape                  view_63                  1 1 136 137 0=256
InnerProduct             linear_22                1 1 137 138 0=256 1=1 2=65536
MemoryData               pnnx_153                 0 1 139 0=40 1=256
Normalize                normalize_0              1 1 138 140 0=1 1=1 2=1.000000e-12 3=1 4=1 9=1
Split                    splitncnn_15             1 3 140 141 142 143
MatMul                     mm_66                    2 1 143 139 144
Reshape                  reshape_58               1 1 144 145 0=20 1=2
Split                    splitncnn_16             1 2 145 146 147
BinaryOp                 mul_10                   1 1 146 148 0=2 1=1 2=1.000000e+01
Softmax                  softmax_2                1 1 148 149 0=1 1=1
BinaryOp                 mul_11                   2 1 149 147 150 0=2
Reduction                sum_69                   1 1 150 151 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_180                 0 1 152 0=40 1=256
MatMul                     mm_67                    2 1 142 152 153
Reshape                  reshape_59               1 1 153 154 0=20 1=2
Split                    splitncnn_17             1 2 154 155 156
BinaryOp                 mul_12                   1 1 155 157 0=2 1=1 2=1.000000e+01
Softmax                  softmax_3                1 1 157 158 0=1 1=1
BinaryOp                 mul_13                   2 1 158 156 159 0=2
Reduction                sum_70                   1 1 159 160 0=0 1=0 -23303=1,1 4=0 5=1
MemoryData               pnnx_207                 0 1 161 0=40 1=256
MatMul                     mm_68                    2 1 141 161 162
Reshape                  reshape_60               1 1 162 163 0=20 1=2
Split                    splitncnn_18             1 2 163 164 165
BinaryOp                 mul_14                   1 1 164 166 0=2 1=1 2=1.000000e+01
Softmax                  softmax_4                1 1 166 167 0=1 1=1
BinaryOp                 mul_15                   2 1 167 165 168 0=2
Reduction                sum_71                   1 1 168 169 0=0 1=0 -23303=1,1 4=0 5=1
BinaryOp                 add_16                   2 1 151 160 170 0=0
BinaryOp                 add_17                   2 1 170 169 171 0=0
BinaryOp                 mul_18                   1 1 171 172 0=2 1=1 2=3.000000e+01
BinaryOp                 div_19                   1 1 172 out0 0=3 1=1 2=3.000000e+00
