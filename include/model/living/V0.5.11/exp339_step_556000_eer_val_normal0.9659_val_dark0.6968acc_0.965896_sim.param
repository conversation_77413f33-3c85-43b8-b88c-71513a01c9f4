7767517
197 225
Input            input.1                  0 1 input.1
MemoryData       /backbone/fc0/Constant_1_output_0 0 1 /backbone/fc0/Constant_1_output_0 0=40 1=256
MemoryData       /backbone/fc0/Constant_output_0 0 1 /backbone/fc0/Constant_output_0 0=1
MemoryData       /backbone/fc1/Constant_output_0 0 1 /backbone/fc1/Constant_output_0 0=40 1=256
MemoryData       /backbone/fc2/Constant_output_0 0 1 /backbone/fc2/Constant_output_0 0=40 1=256
MemoryData       /backbone/fc3/Constant_output_0 0 1 /backbone/fc3/Constant_output_0 0=40 1=256
MemoryData       /backbone/fc4/Constant_output_0 0 1 /backbone/fc4/Constant_output_0 0=40 1=256
MemoryData       /backbone/fc5/Constant_output_0 0 1 /backbone/fc5/Constant_output_0 0=40 1=256
MemoryData       /backbone/fc6/Constant_output_0 0 1 /backbone/fc6/Constant_output_0 0=40 1=256
Convolution      /backbone/conv1/conv/Conv 1 1 input.1 /backbone/conv1/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=864
PReLU            /backbone/conv1/prelu/PRelu 1 1 /backbone/conv1/conv/Conv_output_0 /backbone/conv1/prelu/PRelu_output_0 0=32
ConvolutionDepthWise /backbone/conv2_dw/conv/Conv 1 1 /backbone/conv1/prelu/PRelu_output_0 /backbone/conv2_dw/conv/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=288 7=32
PReLU            /backbone/conv2_dw/prelu/PRelu 1 1 /backbone/conv2_dw/conv/Conv_output_0 /backbone/conv2_dw/prelu/PRelu_output_0 0=32
Convolution      /backbone/conv_23/conv/conv/Conv 1 1 /backbone/conv2_dw/prelu/PRelu_output_0 /backbone/conv_23/conv/conv/Conv_output_0 0=103 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3296
PReLU            /backbone/conv_23/conv/prelu/PRelu 1 1 /backbone/conv_23/conv/conv/Conv_output_0 /backbone/conv_23/conv/prelu/PRelu_output_0 0=103
ConvolutionDepthWise /backbone/conv_23/conv_dw/conv/Conv 1 1 /backbone/conv_23/conv/prelu/PRelu_output_0 /backbone/conv_23/conv_dw/conv/Conv_output_0 0=103 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=927 7=103
PReLU            /backbone/conv_23/conv_dw/prelu/PRelu 1 1 /backbone/conv_23/conv_dw/conv/Conv_output_0 /backbone/conv_23/conv_dw/prelu/PRelu_output_0 0=103
Convolution      /backbone/conv_23/project/conv/Conv 1 1 /backbone/conv_23/conv_dw/prelu/PRelu_output_0 /backbone/conv_23/project/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6592
Split            splitncnn_0              1 2 /backbone/conv_23/project/conv/Conv_output_0 /backbone/conv_23/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_23/project/conv/Conv_output_0_splitncnn_1
Convolution      /backbone/conv_3/model/model.0/conv/conv/Conv 1 1 /backbone/conv_23/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_3/model/model.0/conv/conv/Conv_output_0 0=13 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=832
PReLU            /backbone/conv_3/model/model.0/conv/prelu/PRelu 1 1 /backbone/conv_3/model/model.0/conv/conv/Conv_output_0 /backbone/conv_3/model/model.0/conv/prelu/PRelu_output_0 0=13
ConvolutionDepthWise /backbone/conv_3/model/model.0/conv_dw/conv/Conv 1 1 /backbone/conv_3/model/model.0/conv/prelu/PRelu_output_0 /backbone/conv_3/model/model.0/conv_dw/conv/Conv_output_0 0=13 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=117 7=13
PReLU            /backbone/conv_3/model/model.0/conv_dw/prelu/PRelu 1 1 /backbone/conv_3/model/model.0/conv_dw/conv/Conv_output_0 /backbone/conv_3/model/model.0/conv_dw/prelu/PRelu_output_0 0=13
Convolution      /backbone/conv_3/model/model.0/project/conv/Conv 1 1 /backbone/conv_3/model/model.0/conv_dw/prelu/PRelu_output_0 /backbone/conv_3/model/model.0/project/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=832
BinaryOp         /backbone/conv_3/model/model.0/Add 2 1 /backbone/conv_23/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_3/model/model.0/project/conv/Conv_output_0 /backbone/conv_3/model/model.0/Add_output_0 0=0
Split            splitncnn_1              1 2 /backbone/conv_3/model/model.0/Add_output_0 /backbone/conv_3/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.0/Add_output_0_splitncnn_1
Convolution      /backbone/conv_3/model/model.1/conv/conv/Conv 1 1 /backbone/conv_3/model/model.0/Add_output_0_splitncnn_1 /backbone/conv_3/model/model.1/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1664
PReLU            /backbone/conv_3/model/model.1/conv/prelu/PRelu 1 1 /backbone/conv_3/model/model.1/conv/conv/Conv_output_0 /backbone/conv_3/model/model.1/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_3/model/model.1/conv_dw/conv/Conv 1 1 /backbone/conv_3/model/model.1/conv/prelu/PRelu_output_0 /backbone/conv_3/model/model.1/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_3/model/model.1/conv_dw/prelu/PRelu 1 1 /backbone/conv_3/model/model.1/conv_dw/conv/Conv_output_0 /backbone/conv_3/model/model.1/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_3/model/model.1/project/conv/Conv 1 1 /backbone/conv_3/model/model.1/conv_dw/prelu/PRelu_output_0 /backbone/conv_3/model/model.1/project/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1664
BinaryOp         /backbone/conv_3/model/model.1/Add 2 1 /backbone/conv_3/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.1/project/conv/Conv_output_0 /backbone/conv_3/model/model.1/Add_output_0 0=0
Split            splitncnn_2              1 2 /backbone/conv_3/model/model.1/Add_output_0 /backbone/conv_3/model/model.1/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.1/Add_output_0_splitncnn_1
Convolution      /backbone/conv_3/model/model.2/conv/conv/Conv 1 1 /backbone/conv_3/model/model.1/Add_output_0_splitncnn_1 /backbone/conv_3/model/model.2/conv/conv/Conv_output_0 0=13 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=832
PReLU            /backbone/conv_3/model/model.2/conv/prelu/PRelu 1 1 /backbone/conv_3/model/model.2/conv/conv/Conv_output_0 /backbone/conv_3/model/model.2/conv/prelu/PRelu_output_0 0=13
ConvolutionDepthWise /backbone/conv_3/model/model.2/conv_dw/conv/Conv 1 1 /backbone/conv_3/model/model.2/conv/prelu/PRelu_output_0 /backbone/conv_3/model/model.2/conv_dw/conv/Conv_output_0 0=13 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=117 7=13
PReLU            /backbone/conv_3/model/model.2/conv_dw/prelu/PRelu 1 1 /backbone/conv_3/model/model.2/conv_dw/conv/Conv_output_0 /backbone/conv_3/model/model.2/conv_dw/prelu/PRelu_output_0 0=13
Convolution      /backbone/conv_3/model/model.2/project/conv/Conv 1 1 /backbone/conv_3/model/model.2/conv_dw/prelu/PRelu_output_0 /backbone/conv_3/model/model.2/project/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=832
BinaryOp         /backbone/conv_3/model/model.2/Add 2 1 /backbone/conv_3/model/model.1/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.2/project/conv/Conv_output_0 /backbone/conv_3/model/model.2/Add_output_0 0=0
Split            splitncnn_3              1 2 /backbone/conv_3/model/model.2/Add_output_0 /backbone/conv_3/model/model.2/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.2/Add_output_0_splitncnn_1
Convolution      /backbone/conv_3/model/model.3/conv/conv/Conv 1 1 /backbone/conv_3/model/model.2/Add_output_0_splitncnn_1 /backbone/conv_3/model/model.3/conv/conv/Conv_output_0 0=52 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_3/model/model.3/conv/prelu/PRelu 1 1 /backbone/conv_3/model/model.3/conv/conv/Conv_output_0 /backbone/conv_3/model/model.3/conv/prelu/PRelu_output_0 0=52
ConvolutionDepthWise /backbone/conv_3/model/model.3/conv_dw/conv/Conv 1 1 /backbone/conv_3/model/model.3/conv/prelu/PRelu_output_0 /backbone/conv_3/model/model.3/conv_dw/conv/Conv_output_0 0=52 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=468 7=52
PReLU            /backbone/conv_3/model/model.3/conv_dw/prelu/PRelu 1 1 /backbone/conv_3/model/model.3/conv_dw/conv/Conv_output_0 /backbone/conv_3/model/model.3/conv_dw/prelu/PRelu_output_0 0=52
Convolution      /backbone/conv_3/model/model.3/project/conv/Conv 1 1 /backbone/conv_3/model/model.3/conv_dw/prelu/PRelu_output_0 /backbone/conv_3/model/model.3/project/conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
Split            splitncnn_4              1 2 /backbone/conv_3/model/model.3/project/conv/Conv_output_0 /backbone/conv_3/model/model.3/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_3/model/model.3/project/conv/Conv_output_0_splitncnn_1
Pooling          /backbone/conv_3/model/model.3/se_module/avg_pool/GlobalAveragePool 1 1 /backbone/conv_3/model/model.3/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_3/model/model.3/se_module/avg_pool/GlobalAveragePool_output_0 0=1 4=1
Convolution      /backbone/conv_3/model/model.3/se_module/fc1/Conv 1 1 /backbone/conv_3/model/model.3/se_module/avg_pool/GlobalAveragePool_output_0 /backbone/conv_3/model/model.3/se_module/fc1/Conv_output_0 0=16 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
ReLU             /backbone/conv_3/model/model.3/se_module/relu/Relu 1 1 /backbone/conv_3/model/model.3/se_module/fc1/Conv_output_0 /backbone/conv_3/model/model.3/se_module/relu/Relu_output_0
Convolution      /backbone/conv_3/model/model.3/se_module/fc2/Conv 1 1 /backbone/conv_3/model/model.3/se_module/relu/Relu_output_0 /backbone/conv_3/model/model.3/se_module/fc2/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1024
Sigmoid          /backbone/conv_3/model/model.3/se_module/sigmoid/Sigmoid 1 1 /backbone/conv_3/model/model.3/se_module/fc2/Conv_output_0 /backbone/conv_3/model/model.3/se_module/sigmoid/Sigmoid_output_0
BinaryOp         /backbone/conv_3/model/model.3/se_module/Mul 2 1 /backbone/conv_3/model/model.3/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_3/model/model.3/se_module/sigmoid/Sigmoid_output_0 /backbone/conv_3/model/model.3/se_module/Mul_output_0 0=2
BinaryOp         /backbone/conv_3/model/model.3/Add 2 1 /backbone/conv_3/model/model.2/Add_output_0_splitncnn_0 /backbone/conv_3/model/model.3/se_module/Mul_output_0 /backbone/conv_3/model/model.3/Add_output_0 0=0
Convolution      /backbone/conv_34/conv/conv/Conv 1 1 /backbone/conv_3/model/model.3/Add_output_0 /backbone/conv_34/conv/conv/Conv_output_0 0=231 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=14784
PReLU            /backbone/conv_34/conv/prelu/PRelu 1 1 /backbone/conv_34/conv/conv/Conv_output_0 /backbone/conv_34/conv/prelu/PRelu_output_0 0=231
ConvolutionDepthWise /backbone/conv_34/conv_dw/conv/Conv 1 1 /backbone/conv_34/conv/prelu/PRelu_output_0 /backbone/conv_34/conv_dw/conv/Conv_output_0 0=231 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=2079 7=231
PReLU            /backbone/conv_34/conv_dw/prelu/PRelu 1 1 /backbone/conv_34/conv_dw/conv/Conv_output_0 /backbone/conv_34/conv_dw/prelu/PRelu_output_0 0=231
Convolution      /backbone/conv_34/project/conv/Conv 1 1 /backbone/conv_34/conv_dw/prelu/PRelu_output_0 /backbone/conv_34/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=29568
Split            splitncnn_5              1 2 /backbone/conv_34/project/conv/Conv_output_0 /backbone/conv_34/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_34/project/conv/Conv_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.0/conv/conv/Conv 1 1 /backbone/conv_34/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_4/model/model.0/conv/conv/Conv_output_0 0=154 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=19712
PReLU            /backbone/conv_4/model/model.0/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.0/conv/conv/Conv_output_0 /backbone/conv_4/model/model.0/conv/prelu/PRelu_output_0 0=154
ConvolutionDepthWise /backbone/conv_4/model/model.0/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.0/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.0/conv_dw/conv/Conv_output_0 0=154 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=1386 7=154
PReLU            /backbone/conv_4/model/model.0/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.0/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.0/conv_dw/prelu/PRelu_output_0 0=154
Convolution      /backbone/conv_4/model/model.0/project/conv/Conv 1 1 /backbone/conv_4/model/model.0/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.0/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=19712
BinaryOp         /backbone/conv_4/model/model.0/Add 2 1 /backbone/conv_34/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_4/model/model.0/project/conv/Conv_output_0 /backbone/conv_4/model/model.0/Add_output_0 0=0
Split            splitncnn_6              1 2 /backbone/conv_4/model/model.0/Add_output_0 /backbone/conv_4/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.0/Add_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.1/conv/conv/Conv 1 1 /backbone/conv_4/model/model.0/Add_output_0_splitncnn_1 /backbone/conv_4/model/model.1/conv/conv/Conv_output_0 0=52 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6656
PReLU            /backbone/conv_4/model/model.1/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.1/conv/conv/Conv_output_0 /backbone/conv_4/model/model.1/conv/prelu/PRelu_output_0 0=52
ConvolutionDepthWise /backbone/conv_4/model/model.1/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.1/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.1/conv_dw/conv/Conv_output_0 0=52 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=468 7=52
PReLU            /backbone/conv_4/model/model.1/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.1/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.1/conv_dw/prelu/PRelu_output_0 0=52
Convolution      /backbone/conv_4/model/model.1/project/conv/Conv 1 1 /backbone/conv_4/model/model.1/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.1/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6656
BinaryOp         /backbone/conv_4/model/model.1/Add 2 1 /backbone/conv_4/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.1/project/conv/Conv_output_0 /backbone/conv_4/model/model.1/Add_output_0 0=0
Split            splitncnn_7              1 2 /backbone/conv_4/model/model.1/Add_output_0 /backbone/conv_4/model/model.1/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.1/Add_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.2/conv/conv/Conv 1 1 /backbone/conv_4/model/model.1/Add_output_0_splitncnn_1 /backbone/conv_4/model/model.2/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_4/model/model.2/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.2/conv/conv/Conv_output_0 /backbone/conv_4/model/model.2/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_4/model/model.2/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.2/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.2/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_4/model/model.2/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.2/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.2/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_4/model/model.2/project/conv/Conv 1 1 /backbone/conv_4/model/model.2/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.2/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
BinaryOp         /backbone/conv_4/model/model.2/Add 2 1 /backbone/conv_4/model/model.1/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.2/project/conv/Conv_output_0 /backbone/conv_4/model/model.2/Add_output_0 0=0
Split            splitncnn_8              1 2 /backbone/conv_4/model/model.2/Add_output_0 /backbone/conv_4/model/model.2/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.2/Add_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.3/conv/conv/Conv 1 1 /backbone/conv_4/model/model.2/Add_output_0_splitncnn_1 /backbone/conv_4/model/model.3/conv/conv/Conv_output_0 0=52 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6656
PReLU            /backbone/conv_4/model/model.3/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.3/conv/conv/Conv_output_0 /backbone/conv_4/model/model.3/conv/prelu/PRelu_output_0 0=52
ConvolutionDepthWise /backbone/conv_4/model/model.3/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.3/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.3/conv_dw/conv/Conv_output_0 0=52 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=468 7=52
PReLU            /backbone/conv_4/model/model.3/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.3/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.3/conv_dw/prelu/PRelu_output_0 0=52
Convolution      /backbone/conv_4/model/model.3/project/conv/Conv 1 1 /backbone/conv_4/model/model.3/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.3/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6656
BinaryOp         /backbone/conv_4/model/model.3/Add 2 1 /backbone/conv_4/model/model.2/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.3/project/conv/Conv_output_0 /backbone/conv_4/model/model.3/Add_output_0 0=0
Split            splitncnn_9              1 2 /backbone/conv_4/model/model.3/Add_output_0 /backbone/conv_4/model/model.3/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.3/Add_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.4/conv/conv/Conv 1 1 /backbone/conv_4/model/model.3/Add_output_0_splitncnn_1 /backbone/conv_4/model/model.4/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_4/model/model.4/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.4/conv/conv/Conv_output_0 /backbone/conv_4/model/model.4/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_4/model/model.4/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.4/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.4/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_4/model/model.4/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.4/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.4/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_4/model/model.4/project/conv/Conv 1 1 /backbone/conv_4/model/model.4/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.4/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
BinaryOp         /backbone/conv_4/model/model.4/Add 2 1 /backbone/conv_4/model/model.3/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.4/project/conv/Conv_output_0 /backbone/conv_4/model/model.4/Add_output_0 0=0
Split            splitncnn_10             1 2 /backbone/conv_4/model/model.4/Add_output_0 /backbone/conv_4/model/model.4/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.4/Add_output_0_splitncnn_1
Convolution      /backbone/conv_4/model/model.5/conv/conv/Conv 1 1 /backbone/conv_4/model/model.4/Add_output_0_splitncnn_1 /backbone/conv_4/model/model.5/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_4/model/model.5/conv/prelu/PRelu 1 1 /backbone/conv_4/model/model.5/conv/conv/Conv_output_0 /backbone/conv_4/model/model.5/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_4/model/model.5/conv_dw/conv/Conv 1 1 /backbone/conv_4/model/model.5/conv/prelu/PRelu_output_0 /backbone/conv_4/model/model.5/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_4/model/model.5/conv_dw/prelu/PRelu 1 1 /backbone/conv_4/model/model.5/conv_dw/conv/Conv_output_0 /backbone/conv_4/model/model.5/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_4/model/model.5/project/conv/Conv 1 1 /backbone/conv_4/model/model.5/conv_dw/prelu/PRelu_output_0 /backbone/conv_4/model/model.5/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
Split            splitncnn_11             1 2 /backbone/conv_4/model/model.5/project/conv/Conv_output_0 /backbone/conv_4/model/model.5/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_4/model/model.5/project/conv/Conv_output_0_splitncnn_1
Pooling          /backbone/conv_4/model/model.5/se_module/avg_pool/GlobalAveragePool 1 1 /backbone/conv_4/model/model.5/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_4/model/model.5/se_module/avg_pool/GlobalAveragePool_output_0 0=1 4=1
Convolution      /backbone/conv_4/model/model.5/se_module/fc1/Conv 1 1 /backbone/conv_4/model/model.5/se_module/avg_pool/GlobalAveragePool_output_0 /backbone/conv_4/model/model.5/se_module/fc1/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
ReLU             /backbone/conv_4/model/model.5/se_module/relu/Relu 1 1 /backbone/conv_4/model/model.5/se_module/fc1/Conv_output_0 /backbone/conv_4/model/model.5/se_module/relu/Relu_output_0
Convolution      /backbone/conv_4/model/model.5/se_module/fc2/Conv 1 1 /backbone/conv_4/model/model.5/se_module/relu/Relu_output_0 /backbone/conv_4/model/model.5/se_module/fc2/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Sigmoid          /backbone/conv_4/model/model.5/se_module/sigmoid/Sigmoid 1 1 /backbone/conv_4/model/model.5/se_module/fc2/Conv_output_0 /backbone/conv_4/model/model.5/se_module/sigmoid/Sigmoid_output_0
BinaryOp         /backbone/conv_4/model/model.5/se_module/Mul 2 1 /backbone/conv_4/model/model.5/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_4/model/model.5/se_module/sigmoid/Sigmoid_output_0 /backbone/conv_4/model/model.5/se_module/Mul_output_0 0=2
BinaryOp         /backbone/conv_4/model/model.5/Add 2 1 /backbone/conv_4/model/model.4/Add_output_0_splitncnn_0 /backbone/conv_4/model/model.5/se_module/Mul_output_0 /backbone/conv_4/model/model.5/Add_output_0 0=0
Convolution      /backbone/conv_45/conv/conv/Conv 1 1 /backbone/conv_4/model/model.5/Add_output_0 /backbone/conv_45/conv/conv/Conv_output_0 0=308 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=39424
PReLU            /backbone/conv_45/conv/prelu/PRelu 1 1 /backbone/conv_45/conv/conv/Conv_output_0 /backbone/conv_45/conv/prelu/PRelu_output_0 0=308
ConvolutionDepthWise /backbone/conv_45/conv_dw/conv/Conv 1 1 /backbone/conv_45/conv/prelu/PRelu_output_0 /backbone/conv_45/conv_dw/conv/Conv_output_0 0=308 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=2772 7=308
PReLU            /backbone/conv_45/conv_dw/prelu/PRelu 1 1 /backbone/conv_45/conv_dw/conv/Conv_output_0 /backbone/conv_45/conv_dw/prelu/PRelu_output_0 0=308
Convolution      /backbone/conv_45/project/conv/Conv 1 1 /backbone/conv_45/conv_dw/prelu/PRelu_output_0 /backbone/conv_45/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=39424
Split            splitncnn_12             1 2 /backbone/conv_45/project/conv/Conv_output_0 /backbone/conv_45/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_45/project/conv/Conv_output_0_splitncnn_1
Convolution      /backbone/conv_5/model/model.0/conv/conv/Conv 1 1 /backbone/conv_45/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_5/model/model.0/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_5/model/model.0/conv/prelu/PRelu 1 1 /backbone/conv_5/model/model.0/conv/conv/Conv_output_0 /backbone/conv_5/model/model.0/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_5/model/model.0/conv_dw/conv/Conv 1 1 /backbone/conv_5/model/model.0/conv/prelu/PRelu_output_0 /backbone/conv_5/model/model.0/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_5/model/model.0/conv_dw/prelu/PRelu 1 1 /backbone/conv_5/model/model.0/conv_dw/conv/Conv_output_0 /backbone/conv_5/model/model.0/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_5/model/model.0/project/conv/Conv 1 1 /backbone/conv_5/model/model.0/conv_dw/prelu/PRelu_output_0 /backbone/conv_5/model/model.0/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
BinaryOp         /backbone/conv_5/model/model.0/Add 2 1 /backbone/conv_45/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_5/model/model.0/project/conv/Conv_output_0 /backbone/conv_5/model/model.0/Add_output_0 0=0
Split            splitncnn_13             1 2 /backbone/conv_5/model/model.0/Add_output_0 /backbone/conv_5/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_5/model/model.0/Add_output_0_splitncnn_1
Convolution      /backbone/conv_5/model/model.1/conv/conv/Conv 1 1 /backbone/conv_5/model/model.0/Add_output_0_splitncnn_1 /backbone/conv_5/model/model.1/conv/conv/Conv_output_0 0=26 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
PReLU            /backbone/conv_5/model/model.1/conv/prelu/PRelu 1 1 /backbone/conv_5/model/model.1/conv/conv/Conv_output_0 /backbone/conv_5/model/model.1/conv/prelu/PRelu_output_0 0=26
ConvolutionDepthWise /backbone/conv_5/model/model.1/conv_dw/conv/Conv 1 1 /backbone/conv_5/model/model.1/conv/prelu/PRelu_output_0 /backbone/conv_5/model/model.1/conv_dw/conv/Conv_output_0 0=26 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=234 7=26
PReLU            /backbone/conv_5/model/model.1/conv_dw/prelu/PRelu 1 1 /backbone/conv_5/model/model.1/conv_dw/conv/Conv_output_0 /backbone/conv_5/model/model.1/conv_dw/prelu/PRelu_output_0 0=26
Convolution      /backbone/conv_5/model/model.1/project/conv/Conv 1 1 /backbone/conv_5/model/model.1/conv_dw/prelu/PRelu_output_0 /backbone/conv_5/model/model.1/project/conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3328
Split            splitncnn_14             1 2 /backbone/conv_5/model/model.1/project/conv/Conv_output_0 /backbone/conv_5/model/model.1/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_5/model/model.1/project/conv/Conv_output_0_splitncnn_1
Pooling          /backbone/conv_5/model/model.1/se_module/avg_pool/GlobalAveragePool 1 1 /backbone/conv_5/model/model.1/project/conv/Conv_output_0_splitncnn_1 /backbone/conv_5/model/model.1/se_module/avg_pool/GlobalAveragePool_output_0 0=1 4=1
Convolution      /backbone/conv_5/model/model.1/se_module/fc1/Conv 1 1 /backbone/conv_5/model/model.1/se_module/avg_pool/GlobalAveragePool_output_0 /backbone/conv_5/model/model.1/se_module/fc1/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
ReLU             /backbone/conv_5/model/model.1/se_module/relu/Relu 1 1 /backbone/conv_5/model/model.1/se_module/fc1/Conv_output_0 /backbone/conv_5/model/model.1/se_module/relu/Relu_output_0
Convolution      /backbone/conv_5/model/model.1/se_module/fc2/Conv 1 1 /backbone/conv_5/model/model.1/se_module/relu/Relu_output_0 /backbone/conv_5/model/model.1/se_module/fc2/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Sigmoid          /backbone/conv_5/model/model.1/se_module/sigmoid/Sigmoid 1 1 /backbone/conv_5/model/model.1/se_module/fc2/Conv_output_0 /backbone/conv_5/model/model.1/se_module/sigmoid/Sigmoid_output_0
BinaryOp         /backbone/conv_5/model/model.1/se_module/Mul 2 1 /backbone/conv_5/model/model.1/project/conv/Conv_output_0_splitncnn_0 /backbone/conv_5/model/model.1/se_module/sigmoid/Sigmoid_output_0 /backbone/conv_5/model/model.1/se_module/Mul_output_0 0=2
BinaryOp         /backbone/conv_5/model/model.1/Add 2 1 /backbone/conv_5/model/model.0/Add_output_0_splitncnn_0 /backbone/conv_5/model/model.1/se_module/Mul_output_0 /backbone/conv_5/model/model.1/Add_output_0 0=0
Convolution      /backbone/conv_6_sep/conv/Conv 1 1 /backbone/conv_5/model/model.1/Add_output_0 /backbone/conv_6_sep/conv/Conv_output_0 0=512 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=65536
PReLU            /backbone/conv_6_sep/prelu/PRelu 1 1 /backbone/conv_6_sep/conv/Conv_output_0 /backbone/conv_6_sep/prelu/PRelu_output_0 0=512
ConvolutionDepthWise /backbone/conv_6_dw/conv/Conv 1 1 /backbone/conv_6_sep/prelu/PRelu_output_0 /backbone/conv_6_dw/conv/Conv_output_0 0=256 1=14 11=14 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=100352 7=256
Reshape          /backbone/Reshape        1 1 /backbone/conv_6_dw/conv/Conv_output_0 /backbone/Reshape_output_0 0=-1
InnerProduct     /backbone/fc/Gemm        1 1 /backbone/Reshape_output_0 /backbone/fc/Gemm_output_0 0=256 1=1 2=65536
Normalize        /backbone/fc0/Div        1 1 /backbone/fc/Gemm_output_0 /backbone/fc0/Div_output_0 0=1 1=1 2=1.000000e-12 3=1 4=1 9=1
Split            splitncnn_15             1 7 /backbone/fc0/Div_output_0 /backbone/fc0/Div_output_0_splitncnn_0 /backbone/fc0/Div_output_0_splitncnn_1 /backbone/fc0/Div_output_0_splitncnn_2 /backbone/fc0/Div_output_0_splitncnn_3 /backbone/fc0/Div_output_0_splitncnn_4 /backbone/fc0/Div_output_0_splitncnn_5 /backbone/fc0/Div_output_0_splitncnn_6
MatMul             /backbone/fc0/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_6 /backbone/fc0/Constant_1_output_0 /backbone/fc0/Gemm_output_0
Reshape          /backbone/fc0/Reshape    1 1 /backbone/fc0/Gemm_output_0 /backbone/fc0/Reshape_output_0 0=20 1=2
Split            splitncnn_16             1 2 /backbone/fc0/Reshape_output_0 /backbone/fc0/Reshape_output_0_splitncnn_0 /backbone/fc0/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc0/Mul        1 1 /backbone/fc0/Reshape_output_0_splitncnn_1 /backbone/fc0/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc0/Softmax    1 1 /backbone/fc0/Mul_output_0 /backbone/fc0/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc0/Mul_1      2 1 /backbone/fc0/Softmax_output_0 /backbone/fc0/Reshape_output_0_splitncnn_0 /backbone/fc0/Mul_1_output_0 0=2
Reduction        /backbone/fc0/ReduceSum  1 1 /backbone/fc0/Mul_1_output_0 /backbone/fc0/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc1/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_5 /backbone/fc1/Constant_output_0 /backbone/fc1/Gemm_output_0
Reshape          /backbone/fc1/Reshape    1 1 /backbone/fc1/Gemm_output_0 /backbone/fc1/Reshape_output_0 0=20 1=2
Split            splitncnn_17             1 2 /backbone/fc1/Reshape_output_0 /backbone/fc1/Reshape_output_0_splitncnn_0 /backbone/fc1/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc1/Mul        1 1 /backbone/fc1/Reshape_output_0_splitncnn_1 /backbone/fc1/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc1/Softmax    1 1 /backbone/fc1/Mul_output_0 /backbone/fc1/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc1/Mul_1      2 1 /backbone/fc1/Softmax_output_0 /backbone/fc1/Reshape_output_0_splitncnn_0 /backbone/fc1/Mul_1_output_0 0=2
Reduction        /backbone/fc1/ReduceSum  1 1 /backbone/fc1/Mul_1_output_0 /backbone/fc1/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc2/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_4 /backbone/fc2/Constant_output_0 /backbone/fc2/Gemm_output_0
Reshape          /backbone/fc2/Reshape    1 1 /backbone/fc2/Gemm_output_0 /backbone/fc2/Reshape_output_0 0=20 1=2
Split            splitncnn_18             1 2 /backbone/fc2/Reshape_output_0 /backbone/fc2/Reshape_output_0_splitncnn_0 /backbone/fc2/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc2/Mul        1 1 /backbone/fc2/Reshape_output_0_splitncnn_1 /backbone/fc2/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc2/Softmax    1 1 /backbone/fc2/Mul_output_0 /backbone/fc2/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc2/Mul_1      2 1 /backbone/fc2/Softmax_output_0 /backbone/fc2/Reshape_output_0_splitncnn_0 /backbone/fc2/Mul_1_output_0 0=2
Reduction        /backbone/fc2/ReduceSum  1 1 /backbone/fc2/Mul_1_output_0 /backbone/fc2/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc3/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_3 /backbone/fc3/Constant_output_0 /backbone/fc3/Gemm_output_0
Reshape          /backbone/fc3/Reshape    1 1 /backbone/fc3/Gemm_output_0 /backbone/fc3/Reshape_output_0 0=20 1=2
Split            splitncnn_19             1 2 /backbone/fc3/Reshape_output_0 /backbone/fc3/Reshape_output_0_splitncnn_0 /backbone/fc3/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc3/Mul        1 1 /backbone/fc3/Reshape_output_0_splitncnn_1 /backbone/fc3/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc3/Softmax    1 1 /backbone/fc3/Mul_output_0 /backbone/fc3/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc3/Mul_1      2 1 /backbone/fc3/Softmax_output_0 /backbone/fc3/Reshape_output_0_splitncnn_0 /backbone/fc3/Mul_1_output_0 0=2
Reduction        /backbone/fc3/ReduceSum  1 1 /backbone/fc3/Mul_1_output_0 /backbone/fc3/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc4/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_2 /backbone/fc4/Constant_output_0 /backbone/fc4/Gemm_output_0
Reshape          /backbone/fc4/Reshape    1 1 /backbone/fc4/Gemm_output_0 /backbone/fc4/Reshape_output_0 0=20 1=2
Split            splitncnn_20             1 2 /backbone/fc4/Reshape_output_0 /backbone/fc4/Reshape_output_0_splitncnn_0 /backbone/fc4/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc4/Mul        1 1 /backbone/fc4/Reshape_output_0_splitncnn_1 /backbone/fc4/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc4/Softmax    1 1 /backbone/fc4/Mul_output_0 /backbone/fc4/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc4/Mul_1      2 1 /backbone/fc4/Softmax_output_0 /backbone/fc4/Reshape_output_0_splitncnn_0 /backbone/fc4/Mul_1_output_0 0=2
Reduction        /backbone/fc4/ReduceSum  1 1 /backbone/fc4/Mul_1_output_0 /backbone/fc4/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc5/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_1 /backbone/fc5/Constant_output_0 /backbone/fc5/Gemm_output_0
Reshape          /backbone/fc5/Reshape    1 1 /backbone/fc5/Gemm_output_0 /backbone/fc5/Reshape_output_0 0=20 1=2
Split            splitncnn_21             1 2 /backbone/fc5/Reshape_output_0 /backbone/fc5/Reshape_output_0_splitncnn_0 /backbone/fc5/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc5/Mul        1 1 /backbone/fc5/Reshape_output_0_splitncnn_1 /backbone/fc5/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc5/Softmax    1 1 /backbone/fc5/Mul_output_0 /backbone/fc5/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc5/Mul_1      2 1 /backbone/fc5/Softmax_output_0 /backbone/fc5/Reshape_output_0_splitncnn_0 /backbone/fc5/Mul_1_output_0 0=2
Reduction        /backbone/fc5/ReduceSum  1 1 /backbone/fc5/Mul_1_output_0 /backbone/fc5/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
MatMul             /backbone/fc6/Gemm       2 1 /backbone/fc0/Div_output_0_splitncnn_0 /backbone/fc6/Constant_output_0 /backbone/fc6/Gemm_output_0
Reshape          /backbone/fc6/Reshape    1 1 /backbone/fc6/Gemm_output_0 /backbone/fc6/Reshape_output_0 0=20 1=2
Split            splitncnn_22             1 2 /backbone/fc6/Reshape_output_0 /backbone/fc6/Reshape_output_0_splitncnn_0 /backbone/fc6/Reshape_output_0_splitncnn_1
BinaryOp         /backbone/fc6/Mul        1 1 /backbone/fc6/Reshape_output_0_splitncnn_1 /backbone/fc6/Mul_output_0 0=2 1=1 2=1.000000e+01
Softmax          /backbone/fc6/Softmax    1 1 /backbone/fc6/Mul_output_0 /backbone/fc6/Softmax_output_0 0=1 1=1
BinaryOp         /backbone/fc6/Mul_1      2 1 /backbone/fc6/Softmax_output_0 /backbone/fc6/Reshape_output_0_splitncnn_0 /backbone/fc6/Mul_1_output_0 0=2
Reduction        /backbone/fc6/ReduceSum  1 1 /backbone/fc6/Mul_1_output_0 /backbone/fc6/ReduceSum_output_0 0=0 1=0 -23303=1,1 4=0 5=1
BinaryOp         /backbone/Add            2 1 /backbone/fc0/ReduceSum_output_0 /backbone/fc1/ReduceSum_output_0 /backbone/Add_output_0 0=0
BinaryOp         /backbone/Add_1          2 1 /backbone/Add_output_0 /backbone/fc2/ReduceSum_output_0 /backbone/Add_1_output_0 0=0
BinaryOp         /backbone/Add_2          2 1 /backbone/Add_1_output_0 /backbone/fc3/ReduceSum_output_0 /backbone/Add_2_output_0 0=0
BinaryOp         /backbone/Add_3          2 1 /backbone/Add_2_output_0 /backbone/fc4/ReduceSum_output_0 /backbone/Add_3_output_0 0=0
BinaryOp         /backbone/Add_4          2 1 /backbone/Add_3_output_0 /backbone/fc5/ReduceSum_output_0 /backbone/Add_4_output_0 0=0
BinaryOp         /backbone/Add_5          2 1 /backbone/Add_4_output_0 /backbone/fc6/ReduceSum_output_0 /backbone/Add_5_output_0 0=0
BinaryOp         /backbone/Mul            1 1 /backbone/Add_5_output_0 /backbone/Mul_output_0 0=2 1=1 2=3.000000e+01
BinaryOp         /backbone/Div            1 1 /backbone/Mul_output_0 664 0=3 1=1 2=7.000000e+00
