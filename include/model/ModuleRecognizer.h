/**  
 * All rights Reserved, Designed By MI
 * @projectName bstFaceUnlock
 * @title     ModuleRecognizer   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 下午3:30  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//

#ifndef BSTFACEUNLOCK_MODULERECOGNIZER_H
#define BSTFACEUNLOCK_MODULERECOGNIZER_H
#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
class BSTRecognizerParam {
public:
    bool normalized = true; //whether need normalize feature
};
class ModuleRecognizer {
public:
    ModuleRecognizer() {
    }
    virtual ~ModuleRecognizer();

    int init();
    int release();

    bool perm(unsigned char* img, int w, int h, int c, std::vector<float>& feats);

    float getScore(const std::vector<float>& f1, const std::vector<float>& f2);

private:
    void        normalize(std::vector<float>& feats);
    ncnn::Net   m_net;
    const float           scale[3] = {1 / 127.5f, 1 / 127.5f ,1 / 127.5f};
    const float           mean[3]  = {127.5f,127.5f,127.5f};

    int                net_in_c_ = 3;
    int                net_in_w_ = 112;
    int                net_in_h_ = 112;
    BSTRecognizerParam param_;
    float              sum = 0;
};

#endif //BSTFACEUNLOCK_MODULERECOGNIZER_H
