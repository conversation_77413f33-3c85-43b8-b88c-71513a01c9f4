#ifndef __MODULE_SCREEN_PAPER_DETECT_H__
#define __MODULE_SCREEN_PAPER_DETECT_H__

#include <stdio.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
#include "BSTFaceUnlockConfig.h"


struct ModuleScreenPaperDetectParam {
    float iou_thd;
    float conf_enroll_thd;
    float conf_auth_thd;
    int   max_scale_num;

    int   net_input_w;
    int   net_input_h;

    ModuleScreenPaperDetectParam()
        : iou_thd(0.65f), conf_enroll_thd(0.5f),conf_auth_thd(0.5f) , max_scale_num(4), net_input_w(160), net_input_h(192)  {
    }
};

class ModuleScreenPaperDetect {
public:
    int  init();
    bool perfom(const unsigned char* const img, int w, int h, int c, std::vector<Object>& objects,Bbox face, bool isEnroll, float min_face);

    int release();

private:

    void preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* const dst, int out_w, int out_h, std::vector<float>& pad_info);

    ModuleScreenPaperDetectParam m_param;
    ncnn::Net         m_net;

    float             scale[3]     = {1 / 255.f, 1 / 255.f, 1 / 255.f};
};

#endif // ifndef __MODULE_SCREEN_PAPER_DETECT_H__