7767517
188 220
Input                    in0                      0 1 in0
Convolution              conv_2                   1 1 in0 1 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_110               1 1 3 4 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_3                   1 1 4 5 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_0                    2 1 5 2 6 0=0
ReLU                     relu_62                  1 1 6 7
Split                    splitncnn_1              1 2 7 8 9
ConvolutionDepthWise     convdw_111               1 1 9 10 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_4                   1 1 10 11 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_1                    2 1 11 8 12 0=0
ReLU                     relu_63                  1 1 12 13
Split                    splitncnn_2              1 2 13 14 15
ConvolutionDepthWise     convdw_112               1 1 15 16 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Convolution              conv_5                   1 1 16 17 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
Pooling                  maxpool2d_55             1 1 14 18 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_6                   1 1 18 19 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
BinaryOp                 add_2                    2 1 17 19 20 0=0
ReLU                     relu_64                  1 1 20 21
Split                    splitncnn_3              1 2 21 22 23
ConvolutionDepthWise     convdw_113               1 1 23 24 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_7                   1 1 24 25 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_3                    2 1 25 22 26 0=0
ReLU                     relu_65                  1 1 26 27
Split                    splitncnn_4              1 2 27 28 29
ConvolutionDepthWise     convdw_114               1 1 29 30 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_8                   1 1 30 31 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_4                    2 1 31 28 32 0=0
ReLU                     relu_66                  1 1 32 33
Split                    splitncnn_5              1 2 33 34 35
ConvolutionDepthWise     convdw_115               1 1 35 36 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              conv_9                   1 1 36 37 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
Pooling                  maxpool2d_56             1 1 34 38 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_10                  1 1 38 39 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_5                    2 1 37 39 40 0=0
ReLU                     relu_67                  1 1 40 41
Split                    splitncnn_6              1 2 41 42 43
ConvolutionDepthWise     convdw_116               1 1 43 44 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_11                  1 1 44 45 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_6                    2 1 45 42 46 0=0
ReLU                     relu_68                  1 1 46 47
Split                    splitncnn_7              1 3 47 48 49 50
ConvolutionDepthWise     convdw_117               1 1 50 51 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              convrelu_0               1 1 51 52 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1280 9=1
Convolution              conv_13                  1 1 52 53 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_14                  1 1 53 54 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_57             1 1 49 55 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_15                  1 1 55 56 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2880
BinaryOp                 add_7                    2 1 54 56 57 0=0
ReLU                     relu_70                  1 1 57 58
Split                    splitncnn_8              1 2 58 59 60
ConvolutionDepthWise     convdw_118               1 1 60 61 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_1               1 1 61 62 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_17                  1 1 62 63 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_18                  1 1 63 64 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_8                    2 1 64 59 65 0=0
ReLU                     relu_72                  1 1 65 66
Split                    splitncnn_9              1 2 66 67 68
ConvolutionDepthWise     convdw_119               1 1 68 69 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_2               1 1 69 70 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_20                  1 1 70 71 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_21                  1 1 71 72 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_9                    2 1 72 67 73 0=0
ReLU                     relu_74                  1 1 73 74
Split                    splitncnn_10             1 3 74 75 76 77
ConvolutionDepthWise     convdw_120               1 1 77 78 0=72 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 7=72
Convolution              convrelu_3               1 1 78 79 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_23                  1 1 79 80 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_24                  1 1 80 81 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_58             1 1 76 82 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_25                  1 1 82 83 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5184
BinaryOp                 add_10                   2 1 81 83 84 0=0
ReLU                     relu_76                  1 1 84 85
Split                    splitncnn_11             1 2 85 86 87
ConvolutionDepthWise     convdw_121               1 1 87 88 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_4               1 1 88 89 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_27                  1 1 89 90 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_28                  1 1 90 91 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_11                   2 1 91 86 92 0=0
ReLU                     relu_78                  1 1 92 93
Split                    splitncnn_12             1 2 93 94 95
ConvolutionDepthWise     convdw_122               1 1 95 96 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_5               1 1 96 97 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_30                  1 1 97 98 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_31                  1 1 98 99 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_12                   2 1 99 94 100 0=0
ReLU                     relu_80                  1 1 100 101
Convolution              conv_32                  1 1 101 102 0=36 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2592
Swish                    silu_81                  1 1 102 103
Split                    splitncnn_13             1 2 103 104 105
Pooling                  maxpool2d_59             1 1 105 106 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_14             1 2 106 107 108
Pooling                  maxpool2d_60             1 1 108 109 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_15             1 2 109 110 111
Pooling                  maxpool2d_61             1 1 111 112 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Concat                   cat_0                    4 1 104 107 110 112 113 0=0
Convolution              conv_33                  1 1 113 114 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3456
Swish                    silu_82                  1 1 114 115
Split                    splitncnn_16             1 2 115 116 117
Convolution              conv_34                  1 1 117 118 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Interp                   upsample_101             1 1 118 119 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_1                    2 1 119 75 120 0=0
ConvolutionDepthWise     convdw_123               1 1 120 121 0=96 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=864 7=96
Swish                    silu_83                  1 1 121 122
Convolution              conv_35                  1 1 122 123 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Swish                    silu_84                  1 1 123 124
Split                    splitncnn_17             1 2 124 125 126
Convolution              conv_36                  1 1 126 127 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Interp                   upsample_102             1 1 127 128 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_2                    2 1 128 48 129 0=0
ConvolutionDepthWise     convdw_124               1 1 129 130 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Swish                    silu_85                  1 1 130 131
Convolution              conv_37                  1 1 131 132 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Swish                    silu_86                  1 1 132 133
Split                    splitncnn_18             1 2 133 134 135
ConvolutionDepthWise     convdw_125               1 1 135 136 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Swish                    silu_87                  1 1 136 137
Convolution              conv_38                  1 1 137 138 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_88                  1 1 138 139
BinaryOp                 add_13                   2 1 139 125 140 0=0
ConvolutionDepthWise     convdw_126               1 1 140 141 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Swish                    silu_89                  1 1 141 142
Convolution              conv_39                  1 1 142 143 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_90                  1 1 143 144
Split                    splitncnn_19             1 2 144 145 146
ConvolutionDepthWise     convdw_127               1 1 146 147 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Swish                    silu_91                  1 1 147 148
Convolution              conv_40                  1 1 148 149 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_92                  1 1 149 150
BinaryOp                 add_14                   2 1 150 116 151 0=0
ConvolutionDepthWise     convdw_128               1 1 151 152 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Swish                    silu_93                  1 1 152 153
Convolution              conv_41                  1 1 153 154 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_94                  1 1 154 155
MemoryData               pnnx_42                  0 1 156 0=630
Convolution              conv_42                  1 1 134 157 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_95                  1 1 157 158
Convolution              conv_43                  1 1 158 159 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_96                  1 1 159 160
Split                    splitncnn_20             1 2 160 161 162
Convolution              conv_44                  1 1 162 163 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Convolution              conv_45                  1 1 161 164 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48
Concat                   cat_3                    2 1 163 164 165 0=0
Split                    splitncnn_21             1 2 165 out1 167
Convolution              conv_46                  1 1 145 168 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_97                  1 1 168 169
Convolution              conv_47                  1 1 169 170 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_98                  1 1 170 171
Split                    splitncnn_22             1 2 171 172 173
Convolution              conv_48                  1 1 173 174 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Convolution              conv_49                  1 1 172 175 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48
Concat                   cat_4                    2 1 174 175 176 0=0
Split                    splitncnn_23             1 2 176 out2 178
Convolution              conv_50                  1 1 155 179 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_99                  1 1 179 180
Convolution              conv_51                  1 1 180 181 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_100                 1 1 181 182
Split                    splitncnn_24             1 2 182 183 184
Convolution              conv_52                  1 1 184 185 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Convolution              conv_53                  1 1 183 186 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48
Concat                   cat_5                    2 1 185 186 187 0=0
Split                    splitncnn_25             1 2 187 out3 189
Reshape                  view_104                 1 1 167 190 0=480 1=66
Reshape                  view_105                 1 1 178 191 0=120 1=66
Reshape                  view_106                 1 1 189 192 0=30 1=66
Concat                   cat_6                    3 1 190 191 192 193 0=1
Slice                    split_0                  1 2 193 194 195 -23300=2,64,2 1=0
Reshape                  view_107                 1 1 194 196 0=630 1=16 2=4
Permute                  transpose_109            1 1 196 197 0=2
Softmax                  softmax_1                1 1 197 198 0=0 1=1
Convolution              conv_54                  1 1 198 199 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=0 6=16
Reshape                  view_108                 1 1 199 200 0=630 1=4
MemoryData               pnnx_fold_anchor_points.1 0 1 201 0=630 1=2
MemoryData               pnnx_fold_anchor_points.1_1 0 1 202 0=630 1=2
Slice                    chunk_0                  1 2 200 203 204 -23300=2,-233,-233 1=0
BinaryOp                 sub_15                   2 1 201 203 205 0=1
Split                    splitncnn_26             1 2 205 206 207
BinaryOp                 add_16                   2 1 202 204 208 0=0
Split                    splitncnn_27             1 2 208 209 210
BinaryOp                 add_17                   2 1 206 209 211 0=0
BinaryOp                 div_18                   1 1 211 212 0=3 1=1 2=2.000000e+00
BinaryOp                 sub_19                   2 1 210 207 213 0=1
Concat                   cat_7                    2 1 212 213 214 0=0
Reshape                  reshape_103              1 1 156 215 0=630 1=1
BinaryOp                 mul_20                   2 1 214 215 216 0=2
Sigmoid                  sigmoid_0                1 1 195 217
Concat                   cat_8                    2 1 216 217 218 0=0
Permute                  tranpose_2               1 1 218 output 0=1