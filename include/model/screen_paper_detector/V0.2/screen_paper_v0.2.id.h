#ifndef NCNN_INCLUDE_GUARD_screen_paper_v0_2_id_h
#define NCNN_INCLUDE_GUARD_screen_paper_v0_2_id_h
namespace screen_paper_v0_2_param_id {
const int LAYER_images = 0;
const int BLOB_images = 0;
const int LAYER_Conv_0 = 1;
const int BLOB_421 = 1;
const int LAYER_Relu_1 = 2;
const int BLOB_422 = 2;
const int LAYER_splitncnn_0 = 3;
const int BLOB_422_splitncnn_0 = 3;
const int BLOB_422_splitncnn_1 = 4;
const int LAYER_Conv_2 = 4;
const int BLOB_688 = 5;
const int LAYER_Conv_3 = 5;
const int BLOB_691 = 6;
const int LAYER_Add_4 = 6;
const int BLOB_427 = 7;
const int LAYER_Relu_5 = 7;
const int BLOB_428 = 8;
const int LAYER_splitncnn_1 = 8;
const int BLOB_428_splitncnn_0 = 9;
const int BLOB_428_splitncnn_1 = 10;
const int LAYER_Conv_6 = 9;
const int BLOB_694 = 11;
const int LAYER_Conv_7 = 10;
const int BLOB_697 = 12;
const int LAYER_Add_8 = 11;
const int BLOB_433 = 13;
const int LAYER_Relu_9 = 12;
const int BLOB_434 = 14;
const int LAYER_splitncnn_2 = 13;
const int BLOB_434_splitncnn_0 = 15;
const int BLOB_434_splitncnn_1 = 16;
const int LAYER_Conv_10 = 14;
const int BLOB_700 = 17;
const int LAYER_Conv_11 = 15;
const int BLOB_703 = 18;
const int LAYER_MaxPool_12 = 16;
const int BLOB_439 = 19;
const int LAYER_Conv_13 = 17;
const int BLOB_706 = 20;
const int LAYER_Add_14 = 18;
const int BLOB_442 = 21;
const int LAYER_Relu_15 = 19;
const int BLOB_443 = 22;
const int LAYER_splitncnn_3 = 20;
const int BLOB_443_splitncnn_0 = 23;
const int BLOB_443_splitncnn_1 = 24;
const int LAYER_Conv_16 = 21;
const int BLOB_709 = 25;
const int LAYER_Conv_17 = 22;
const int BLOB_712 = 26;
const int LAYER_Add_18 = 23;
const int BLOB_448 = 27;
const int LAYER_Relu_19 = 24;
const int BLOB_449 = 28;
const int LAYER_splitncnn_4 = 25;
const int BLOB_449_splitncnn_0 = 29;
const int BLOB_449_splitncnn_1 = 30;
const int LAYER_Conv_20 = 26;
const int BLOB_715 = 31;
const int LAYER_Conv_21 = 27;
const int BLOB_718 = 32;
const int LAYER_Add_22 = 28;
const int BLOB_454 = 33;
const int LAYER_Relu_23 = 29;
const int BLOB_455 = 34;
const int LAYER_splitncnn_5 = 30;
const int BLOB_455_splitncnn_0 = 35;
const int BLOB_455_splitncnn_1 = 36;
const int LAYER_Conv_24 = 31;
const int BLOB_721 = 37;
const int LAYER_Conv_25 = 32;
const int BLOB_724 = 38;
const int LAYER_MaxPool_26 = 33;
const int BLOB_460 = 39;
const int LAYER_Conv_27 = 34;
const int BLOB_727 = 40;
const int LAYER_Add_28 = 35;
const int BLOB_463 = 41;
const int LAYER_Relu_29 = 36;
const int BLOB_464 = 42;
const int LAYER_splitncnn_6 = 37;
const int BLOB_464_splitncnn_0 = 43;
const int BLOB_464_splitncnn_1 = 44;
const int LAYER_Conv_30 = 38;
const int BLOB_730 = 45;
const int LAYER_Conv_31 = 39;
const int BLOB_733 = 46;
const int LAYER_Add_32 = 40;
const int BLOB_469 = 47;
const int LAYER_Relu_33 = 41;
const int BLOB_470 = 48;
const int LAYER_splitncnn_7 = 42;
const int BLOB_470_splitncnn_0 = 49;
const int BLOB_470_splitncnn_1 = 50;
const int BLOB_470_splitncnn_2 = 51;
const int LAYER_Conv_34 = 43;
const int BLOB_736 = 52;
const int LAYER_Conv_35 = 44;
const int BLOB_739 = 53;
const int LAYER_Relu_36 = 45;
const int BLOB_475 = 54;
const int LAYER_Conv_37 = 46;
const int BLOB_742 = 55;
const int LAYER_Conv_38 = 47;
const int BLOB_745 = 56;
const int LAYER_MaxPool_39 = 48;
const int BLOB_480 = 57;
const int LAYER_Conv_40 = 49;
const int BLOB_748 = 58;
const int LAYER_Add_41 = 50;
const int BLOB_483 = 59;
const int LAYER_Relu_42 = 51;
const int BLOB_484 = 60;
const int LAYER_splitncnn_8 = 52;
const int BLOB_484_splitncnn_0 = 61;
const int BLOB_484_splitncnn_1 = 62;
const int LAYER_Conv_43 = 53;
const int BLOB_751 = 63;
const int LAYER_Conv_44 = 54;
const int BLOB_754 = 64;
const int LAYER_Relu_45 = 55;
const int BLOB_489 = 65;
const int LAYER_Conv_46 = 56;
const int BLOB_757 = 66;
const int LAYER_Conv_47 = 57;
const int BLOB_760 = 67;
const int LAYER_Add_48 = 58;
const int BLOB_494 = 68;
const int LAYER_Relu_49 = 59;
const int BLOB_495 = 69;
const int LAYER_splitncnn_9 = 60;
const int BLOB_495_splitncnn_0 = 70;
const int BLOB_495_splitncnn_1 = 71;
const int LAYER_Conv_50 = 61;
const int BLOB_763 = 72;
const int LAYER_Conv_51 = 62;
const int BLOB_766 = 73;
const int LAYER_Relu_52 = 63;
const int BLOB_500 = 74;
const int LAYER_Conv_53 = 64;
const int BLOB_769 = 75;
const int LAYER_Conv_54 = 65;
const int BLOB_772 = 76;
const int LAYER_Add_55 = 66;
const int BLOB_505 = 77;
const int LAYER_Relu_56 = 67;
const int BLOB_506 = 78;
const int LAYER_splitncnn_10 = 68;
const int BLOB_506_splitncnn_0 = 79;
const int BLOB_506_splitncnn_1 = 80;
const int BLOB_506_splitncnn_2 = 81;
const int LAYER_Conv_57 = 69;
const int BLOB_775 = 82;
const int LAYER_Conv_58 = 70;
const int BLOB_778 = 83;
const int LAYER_Relu_59 = 71;
const int BLOB_511 = 84;
const int LAYER_Conv_60 = 72;
const int BLOB_781 = 85;
const int LAYER_Conv_61 = 73;
const int BLOB_784 = 86;
const int LAYER_MaxPool_62 = 74;
const int BLOB_516 = 87;
const int LAYER_Conv_63 = 75;
const int BLOB_787 = 88;
const int LAYER_Add_64 = 76;
const int BLOB_519 = 89;
const int LAYER_Relu_65 = 77;
const int BLOB_520 = 90;
const int LAYER_splitncnn_11 = 78;
const int BLOB_520_splitncnn_0 = 91;
const int BLOB_520_splitncnn_1 = 92;
const int LAYER_Conv_66 = 79;
const int BLOB_790 = 93;
const int LAYER_Conv_67 = 80;
const int BLOB_793 = 94;
const int LAYER_Relu_68 = 81;
const int BLOB_525 = 95;
const int LAYER_Conv_69 = 82;
const int BLOB_796 = 96;
const int LAYER_Conv_70 = 83;
const int BLOB_799 = 97;
const int LAYER_Add_71 = 84;
const int BLOB_530 = 98;
const int LAYER_Relu_72 = 85;
const int BLOB_531 = 99;
const int LAYER_splitncnn_12 = 86;
const int BLOB_531_splitncnn_0 = 100;
const int BLOB_531_splitncnn_1 = 101;
const int LAYER_Conv_73 = 87;
const int BLOB_802 = 102;
const int LAYER_Conv_74 = 88;
const int BLOB_805 = 103;
const int LAYER_Relu_75 = 89;
const int BLOB_536 = 104;
const int LAYER_Conv_76 = 90;
const int BLOB_808 = 105;
const int LAYER_Conv_77 = 91;
const int BLOB_811 = 106;
const int LAYER_Add_78 = 92;
const int BLOB_541 = 107;
const int LAYER_Relu_79 = 93;
const int BLOB_542 = 108;
const int LAYER_Conv_80 = 94;
const int BLOB_543 = 109;
const int LAYER_Mul_82 = 95;
const int BLOB_545 = 110;
const int LAYER_splitncnn_13 = 96;
const int BLOB_545_splitncnn_0 = 111;
const int BLOB_545_splitncnn_1 = 112;
const int LAYER_Resize_84 = 97;
const int BLOB_550 = 113;
const int LAYER_Concat_85 = 98;
const int BLOB_551 = 114;
const int LAYER_Conv_86 = 99;
const int BLOB_814 = 115;
const int LAYER_Relu_87 = 100;
const int BLOB_554 = 116;
const int LAYER_Conv_88 = 101;
const int BLOB_817 = 117;
const int LAYER_Relu_89 = 102;
const int BLOB_557 = 118;
const int LAYER_Conv_90 = 103;
const int BLOB_558 = 119;
const int LAYER_Mul_92 = 104;
const int BLOB_560 = 120;
const int LAYER_splitncnn_14 = 105;
const int BLOB_560_splitncnn_0 = 121;
const int BLOB_560_splitncnn_1 = 122;
const int LAYER_Resize_94 = 106;
const int BLOB_565 = 123;
const int LAYER_Concat_95 = 107;
const int BLOB_566 = 124;
const int LAYER_Conv_96 = 108;
const int BLOB_820 = 125;
const int LAYER_Relu_97 = 109;
const int BLOB_569 = 126;
const int LAYER_Conv_98 = 110;
const int BLOB_823 = 127;
const int LAYER_Relu_99 = 111;
const int BLOB_572 = 128;
const int LAYER_splitncnn_15 = 112;
const int BLOB_572_splitncnn_0 = 129;
const int BLOB_572_splitncnn_1 = 130;
const int LAYER_Conv_100 = 113;
const int BLOB_826 = 131;
const int LAYER_Relu_101 = 114;
const int BLOB_575 = 132;
const int LAYER_Conv_102 = 115;
const int BLOB_829 = 133;
const int LAYER_Relu_103 = 116;
const int BLOB_578 = 134;
const int LAYER_Add_104 = 117;
const int BLOB_579 = 135;
const int LAYER_Conv_105 = 118;
const int BLOB_832 = 136;
const int LAYER_Relu_106 = 119;
const int BLOB_582 = 137;
const int LAYER_Conv_107 = 120;
const int BLOB_835 = 138;
const int LAYER_Relu_108 = 121;
const int BLOB_585 = 139;
const int LAYER_splitncnn_16 = 122;
const int BLOB_585_splitncnn_0 = 140;
const int BLOB_585_splitncnn_1 = 141;
const int LAYER_Conv_109 = 123;
const int BLOB_838 = 142;
const int LAYER_Relu_110 = 124;
const int BLOB_588 = 143;
const int LAYER_Conv_111 = 125;
const int BLOB_841 = 144;
const int LAYER_Relu_112 = 126;
const int BLOB_591 = 145;
const int LAYER_Add_113 = 127;
const int BLOB_592 = 146;
const int LAYER_Conv_114 = 128;
const int BLOB_844 = 147;
const int LAYER_Relu_115 = 129;
const int BLOB_595 = 148;
const int LAYER_Conv_116 = 130;
const int BLOB_847 = 149;
const int LAYER_Relu_117 = 131;
const int BLOB_598 = 150;
const int LAYER_Conv_118 = 132;
const int BLOB_599 = 151;
const int LAYER_Mul_120 = 133;
const int BLOB_601 = 152;
const int LAYER_splitncnn_17 = 134;
const int BLOB_601_splitncnn_0 = 153;
const int BLOB_601_splitncnn_1 = 154;
const int LAYER_Conv_121 = 135;
const int BLOB_602 = 155;
const int LAYER_Mul_123 = 136;
const int BLOB_604 = 156;
const int LAYER_splitncnn_18 = 137;
const int BLOB_604_splitncnn_0 = 157;
const int BLOB_604_splitncnn_1 = 158;
const int LAYER_Conv_124 = 138;
const int BLOB_605 = 159;
const int LAYER_Mul_126 = 139;
const int BLOB_607 = 160;
const int LAYER_splitncnn_19 = 140;
const int BLOB_607_splitncnn_0 = 161;
const int BLOB_607_splitncnn_1 = 162;
const int LAYER_Conv_127 = 141;
const int BLOB_608 = 163;
const int LAYER_Mul_129 = 142;
const int BLOB_610 = 164;
const int LAYER_Conv_130 = 143;
const int BLOB_611 = 165;
const int LAYER_Mul_132 = 144;
const int BLOB_613 = 166;
const int LAYER_Conv_133 = 145;
const int BLOB_614 = 167;
const int LAYER_Mul_135 = 146;
const int BLOB_616 = 168;
const int LAYER_Conv_136 = 147;
const int BLOB_617 = 169;
const int LAYER_Mul_138 = 148;
const int BLOB_619 = 170;
const int LAYER_splitncnn_20 = 149;
const int BLOB_619_splitncnn_0 = 171;
const int BLOB_619_splitncnn_1 = 172;
const int LAYER_Conv_139 = 150;
const int BLOB_620 = 173;
const int LAYER_Mul_141 = 151;
const int BLOB_622 = 174;
const int LAYER_Conv_142 = 152;
const int BLOB_623 = 175;
const int LAYER_Mul_144 = 153;
const int BLOB_625 = 176;
const int LAYER_Conv_145 = 154;
const int BLOB_626 = 177;
const int LAYER_Mul_147 = 155;
const int BLOB_628 = 178;
const int LAYER_Conv_148 = 156;
const int BLOB_629 = 179;
const int LAYER_Mul_150 = 157;
const int BLOB_631 = 180;
const int LAYER_splitncnn_21 = 158;
const int BLOB_631_splitncnn_0 = 181;
const int BLOB_631_splitncnn_1 = 182;
const int LAYER_Conv_151 = 159;
const int BLOB_632 = 183;
const int LAYER_Mul_153 = 160;
const int BLOB_634 = 184;
const int LAYER_Conv_154 = 161;
const int BLOB_635 = 185;
const int LAYER_Mul_156 = 162;
const int BLOB_637 = 186;
const int LAYER_Conv_157 = 163;
const int BLOB_638 = 187;
const int LAYER_Mul_159 = 164;
const int BLOB_640 = 188;
const int LAYER_Conv_160 = 165;
const int BLOB_641 = 189;
const int LAYER_Mul_162 = 166;
const int BLOB_643 = 190;
const int LAYER_splitncnn_22 = 167;
const int BLOB_643_splitncnn_0 = 191;
const int BLOB_643_splitncnn_1 = 192;
const int LAYER_Conv_163 = 168;
const int BLOB_644 = 193;
const int LAYER_Conv_164 = 169;
const int BLOB_645 = 194;
const int LAYER_Conv_165 = 170;
const int BLOB_646 = 195;
const int LAYER_Sigmoid_166 = 171;
const int BLOB_647 = 196;
const int LAYER_Sigmoid_167 = 172;
const int BLOB_648 = 197;
const int LAYER_Concat_168 = 173;
const int BLOB_649 = 198;
const int LAYER_Conv_169 = 174;
const int BLOB_650 = 199;
const int LAYER_Conv_170 = 175;
const int BLOB_651 = 200;
const int LAYER_Conv_171 = 176;
const int BLOB_652 = 201;
const int LAYER_Sigmoid_172 = 177;
const int BLOB_653 = 202;
const int LAYER_Sigmoid_173 = 178;
const int BLOB_654 = 203;
const int LAYER_Concat_174 = 179;
const int BLOB_655 = 204;
const int LAYER_Conv_175 = 180;
const int BLOB_656 = 205;
const int LAYER_Conv_176 = 181;
const int BLOB_657 = 206;
const int LAYER_Conv_177 = 182;
const int BLOB_658 = 207;
const int LAYER_Sigmoid_178 = 183;
const int BLOB_659 = 208;
const int LAYER_Sigmoid_179 = 184;
const int BLOB_660 = 209;
const int LAYER_Concat_180 = 185;
const int BLOB_661 = 210;
const int LAYER_Reshape_188 = 186;
const int BLOB_669 = 211;
const int LAYER_Reshape_196 = 187;
const int BLOB_677 = 212;
const int LAYER_Reshape_204 = 188;
const int BLOB_685 = 213;
const int LAYER_Concat_205 = 189;
const int BLOB_686 = 214;
const int LAYER_Transpose_206 = 190;
const int BLOB_output = 215;
} // namespace screen_paper_v0_2_param_id
#endif // NCNN_INCLUDE_GUARD_screen_paper_v0_2_id_h
