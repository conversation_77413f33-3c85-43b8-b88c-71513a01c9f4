7767517
191 216
Input            images                   0 1 images
Convolution      Conv_0                   1 1 images 421 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=648
ReLU             Relu_1                   1 1 421 422
Split            splitncnn_0              1 2 422 422_splitncnn_0 422_splitncnn_1
ConvolutionDepthWise Conv_2                   1 1 422_splitncnn_1 688 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=216 7=24
Convolution      Conv_3                   1 1 688 691 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
BinaryOp         Add_4                    2 1 691 422_splitncnn_0 427 0=0
ReLU             Relu_5                   1 1 427 428
Split            splitncnn_1              1 2 428 428_splitncnn_0 428_splitncnn_1
ConvolutionDepthWise Conv_6                   1 1 428_splitncnn_1 694 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=216 7=24
Convolution      Conv_7                   1 1 694 697 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
BinaryOp         Add_8                    2 1 697 428_splitncnn_0 433 0=0
ReLU             Relu_9                   1 1 433 434
Split            splitncnn_2              1 2 434 434_splitncnn_0 434_splitncnn_1
ConvolutionDepthWise Conv_10                  1 1 434_splitncnn_1 700 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=216 7=24
Convolution      Conv_11                  1 1 700 703 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=960
Pooling          MaxPool_12               1 1 434_splitncnn_0 439 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      Conv_13                  1 1 439 706 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=960
BinaryOp         Add_14                   2 1 703 706 442 0=0
ReLU             Relu_15                  1 1 442 443
Split            splitncnn_3              1 2 443 443_splitncnn_0 443_splitncnn_1
ConvolutionDepthWise Conv_16                  1 1 443_splitncnn_1 709 0=40 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=360 7=40
Convolution      Conv_17                  1 1 709 712 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1600
BinaryOp         Add_18                   2 1 712 443_splitncnn_0 448 0=0
ReLU             Relu_19                  1 1 448 449
Split            splitncnn_4              1 2 449 449_splitncnn_0 449_splitncnn_1
ConvolutionDepthWise Conv_20                  1 1 449_splitncnn_1 715 0=40 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=360 7=40
Convolution      Conv_21                  1 1 715 718 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1600
BinaryOp         Add_22                   2 1 718 449_splitncnn_0 454 0=0
ReLU             Relu_23                  1 1 454 455
Split            splitncnn_5              1 2 455 455_splitncnn_0 455_splitncnn_1
ConvolutionDepthWise Conv_24                  1 1 455_splitncnn_1 721 0=40 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=360 7=40
Convolution      Conv_25                  1 1 721 724 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1600
Pooling          MaxPool_26               1 1 455_splitncnn_0 460 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      Conv_27                  1 1 460 727 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1600
BinaryOp         Add_28                   2 1 724 727 463 0=0
ReLU             Relu_29                  1 1 463 464
Split            splitncnn_6              1 2 464 464_splitncnn_0 464_splitncnn_1
ConvolutionDepthWise Conv_30                  1 1 464_splitncnn_1 730 0=40 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=360 7=40
Convolution      Conv_31                  1 1 730 733 0=40 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1600
BinaryOp         Add_32                   2 1 733 464_splitncnn_0 469 0=0
ReLU             Relu_33                  1 1 469 470
Split            splitncnn_7              1 3 470 470_splitncnn_0 470_splitncnn_1 470_splitncnn_2
ConvolutionDepthWise Conv_34                  1 1 470_splitncnn_2 736 0=40 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=360 7=40
Convolution      Conv_35                  1 1 736 739 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1280
ReLU             Relu_36                  1 1 739 475
Convolution      Conv_37                  1 1 475 742 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_38                  1 1 742 745 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
Pooling          MaxPool_39               1 1 470_splitncnn_1 480 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      Conv_40                  1 1 480 748 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2880
BinaryOp         Add_41                   2 1 745 748 483 0=0
ReLU             Relu_42                  1 1 483 484
Split            splitncnn_8              1 2 484 484_splitncnn_0 484_splitncnn_1
ConvolutionDepthWise Conv_43                  1 1 484_splitncnn_1 751 0=72 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=648 7=72
Convolution      Conv_44                  1 1 751 754 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_45                  1 1 754 489
Convolution      Conv_46                  1 1 489 757 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_47                  1 1 757 760 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         Add_48                   2 1 760 484_splitncnn_0 494 0=0
ReLU             Relu_49                  1 1 494 495
Split            splitncnn_9              1 2 495 495_splitncnn_0 495_splitncnn_1
ConvolutionDepthWise Conv_50                  1 1 495_splitncnn_1 763 0=72 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=648 7=72
Convolution      Conv_51                  1 1 763 766 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_52                  1 1 766 500
Convolution      Conv_53                  1 1 500 769 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_54                  1 1 769 772 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         Add_55                   2 1 772 495_splitncnn_0 505 0=0
ReLU             Relu_56                  1 1 505 506
Split            splitncnn_10             1 3 506 506_splitncnn_0 506_splitncnn_1 506_splitncnn_2
ConvolutionDepthWise Conv_57                  1 1 506_splitncnn_2 775 0=72 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=648 7=72
Convolution      Conv_58                  1 1 775 778 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_59                  1 1 778 511
Convolution      Conv_60                  1 1 511 781 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_61                  1 1 781 784 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
Pooling          MaxPool_62               1 1 506_splitncnn_1 516 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      Conv_63                  1 1 516 787 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=5184
BinaryOp         Add_64                   2 1 784 787 519 0=0
ReLU             Relu_65                  1 1 519 520
Split            splitncnn_11             1 2 520 520_splitncnn_0 520_splitncnn_1
ConvolutionDepthWise Conv_66                  1 1 520_splitncnn_1 790 0=72 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=648 7=72
Convolution      Conv_67                  1 1 790 793 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_68                  1 1 793 525
Convolution      Conv_69                  1 1 525 796 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_70                  1 1 796 799 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         Add_71                   2 1 799 520_splitncnn_0 530 0=0
ReLU             Relu_72                  1 1 530 531
Split            splitncnn_12             1 2 531 531_splitncnn_0 531_splitncnn_1
ConvolutionDepthWise Conv_73                  1 1 531_splitncnn_1 802 0=72 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=648 7=72
Convolution      Conv_74                  1 1 802 805 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_75                  1 1 805 536
Convolution      Conv_76                  1 1 536 808 0=32 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=9216
Convolution      Conv_77                  1 1 808 811 0=72 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         Add_78                   2 1 811 531_splitncnn_0 541 0=0
ReLU             Relu_79                  1 1 541 542
Convolution      Conv_80                  1 1 542 543 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1728
Swish            Mul_82                   1 1 543 545
Split            splitncnn_13             1 2 545 545_splitncnn_0 545_splitncnn_1
Interp           Resize_84                1 1 545_splitncnn_1 550 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           Concat_85                2 1 550 506_splitncnn_0 551 0=0
ConvolutionDepthWise Conv_86                  1 1 551 814 0=96 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=864 7=96
ReLU             Relu_87                  1 1 814 554
Convolution      Conv_88                  1 1 554 817 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             Relu_89                  1 1 817 557
Convolution      Conv_90                  1 1 557 558 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
Swish            Mul_92                   1 1 558 560
Split            splitncnn_14             1 2 560 560_splitncnn_0 560_splitncnn_1
Interp           Resize_94                1 1 560_splitncnn_1 565 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           Concat_95                2 1 565 470_splitncnn_0 566 0=0
ConvolutionDepthWise Conv_96                  1 1 566 820 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=576 7=64
ReLU             Relu_97                  1 1 820 569
Convolution      Conv_98                  1 1 569 823 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
ReLU             Relu_99                  1 1 823 572
Split            splitncnn_15             1 2 572 572_splitncnn_0 572_splitncnn_1
ConvolutionDepthWise Conv_100                 1 1 572_splitncnn_1 826 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=216 7=24
ReLU             Relu_101                 1 1 826 575
Convolution      Conv_102                 1 1 575 829 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
ReLU             Relu_103                 1 1 829 578
BinaryOp         Add_104                  2 1 578 560_splitncnn_0 579 0=0
ConvolutionDepthWise Conv_105                 1 1 579 832 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=216 7=24
ReLU             Relu_106                 1 1 832 582
Convolution      Conv_107                 1 1 582 835 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
ReLU             Relu_108                 1 1 835 585
Split            splitncnn_16             1 2 585 585_splitncnn_0 585_splitncnn_1
ConvolutionDepthWise Conv_109                 1 1 585_splitncnn_1 838 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=216 7=24
ReLU             Relu_110                 1 1 838 588
Convolution      Conv_111                 1 1 588 841 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
ReLU             Relu_112                 1 1 841 591
BinaryOp         Add_113                  2 1 591 545_splitncnn_0 592 0=0
ConvolutionDepthWise Conv_114                 1 1 592 844 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=216 7=24
ReLU             Relu_115                 1 1 844 595
Convolution      Conv_116                 1 1 595 847 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
ReLU             Relu_117                 1 1 847 598
Convolution      Conv_118                 1 1 572_splitncnn_0 599 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
Swish            Mul_120                  1 1 599 601
Split            splitncnn_17             1 2 601 601_splitncnn_0 601_splitncnn_1
Convolution      Conv_121                 1 1 585_splitncnn_0 602 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
Swish            Mul_123                  1 1 602 604
Split            splitncnn_18             1 2 604 604_splitncnn_0 604_splitncnn_1
Convolution      Conv_124                 1 1 598 605 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
Swish            Mul_126                  1 1 605 607
Split            splitncnn_19             1 2 607 607_splitncnn_0 607_splitncnn_1
Convolution      Conv_127                 1 1 601_splitncnn_1 608 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_129                  1 1 608 610
Convolution      Conv_130                 1 1 610 611 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_132                  1 1 611 613
Convolution      Conv_133                 1 1 601_splitncnn_0 614 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_135                  1 1 614 616
Convolution      Conv_136                 1 1 616 617 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_138                  1 1 617 619
Split            splitncnn_20             1 2 619 619_splitncnn_0 619_splitncnn_1
Convolution      Conv_139                 1 1 604_splitncnn_1 620 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_141                  1 1 620 622
Convolution      Conv_142                 1 1 622 623 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_144                  1 1 623 625
Convolution      Conv_145                 1 1 604_splitncnn_0 626 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_147                  1 1 626 628
Convolution      Conv_148                 1 1 628 629 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_150                  1 1 629 631
Split            splitncnn_21             1 2 631 631_splitncnn_0 631_splitncnn_1
Convolution      Conv_151                 1 1 607_splitncnn_1 632 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_153                  1 1 632 634
Convolution      Conv_154                 1 1 634 635 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_156                  1 1 635 637
Convolution      Conv_157                 1 1 607_splitncnn_0 638 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_159                  1 1 638 640
Convolution      Conv_160                 1 1 640 641 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Swish            Mul_162                  1 1 641 643
Split            splitncnn_22             1 2 643 643_splitncnn_0 643_splitncnn_1
Convolution      Conv_163                 1 1 613 644 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=48
Convolution      Conv_164                 1 1 619_splitncnn_1 645 0=4 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=96
Convolution      Conv_165                 1 1 619_splitncnn_0 646 0=1 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=24
Sigmoid          Sigmoid_166              1 1 646 647
Sigmoid          Sigmoid_167              1 1 644 648
Concat           Concat_168               3 1 645 647 648 649 0=0
Convolution      Conv_169                 1 1 625 650 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=48
Convolution      Conv_170                 1 1 631_splitncnn_1 651 0=4 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=96
Convolution      Conv_171                 1 1 631_splitncnn_0 652 0=1 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=24
Sigmoid          Sigmoid_172              1 1 652 653
Sigmoid          Sigmoid_173              1 1 650 654
Concat           Concat_174               3 1 651 653 654 655 0=0
Convolution      Conv_175                 1 1 637 656 0=2 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=48
Convolution      Conv_176                 1 1 643_splitncnn_1 657 0=4 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=96
Convolution      Conv_177                 1 1 643_splitncnn_0 658 0=1 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=24
Sigmoid          Sigmoid_178              1 1 658 659
Sigmoid          Sigmoid_179              1 1 656 660
Concat           Concat_180               3 1 657 659 660 661 0=0
Reshape          Reshape_188              1 1 649 669 0=-1 1=7
Reshape          Reshape_196              1 1 655 677 0=-1 1=7
Reshape          Reshape_204              1 1 661 685 0=-1 1=7
Concat           Concat_205               3 1 669 677 685 686 0=1
Permute          Transpose_206            1 1 686 output 0=1
