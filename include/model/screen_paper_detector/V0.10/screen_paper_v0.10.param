7767517
166 191
Input                    in0                      0 1 in0
Convolution              convrelu_0               1 1 in0 1 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 9=1
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_127               1 1 3 4 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_36                  1 1 4 5 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_0                    2 1 5 2 6 0=0
ReLU                     relu_102                 1 1 6 7
Split                    splitncnn_1              1 2 7 8 9
ConvolutionDepthWise     convdw_128               1 1 9 10 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_37                  1 1 10 11 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_1                    2 1 11 8 12 0=0
ReLU                     relu_103                 1 1 12 13
Split                    splitncnn_2              1 2 13 14 15
ConvolutionDepthWise     convdw_129               1 1 15 16 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Convolution              conv_38                  1 1 16 17 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
Pooling                  maxpool2d_97             1 1 14 18 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_39                  1 1 18 19 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=960
BinaryOp                 add_2                    2 1 17 19 20 0=0
ReLU                     relu_104                 1 1 20 21
Split                    splitncnn_3              1 2 21 22 23
ConvolutionDepthWise     convdw_130               1 1 23 24 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_40                  1 1 24 25 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_3                    2 1 25 22 26 0=0
ReLU                     relu_105                 1 1 26 27
Split                    splitncnn_4              1 2 27 28 29
ConvolutionDepthWise     convdw_131               1 1 29 30 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_41                  1 1 30 31 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_4                    2 1 31 28 32 0=0
ReLU                     relu_106                 1 1 32 33
Split                    splitncnn_5              1 2 33 34 35
ConvolutionDepthWise     convdw_132               1 1 35 36 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              conv_42                  1 1 36 37 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
Pooling                  maxpool2d_98             1 1 34 38 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_43                  1 1 38 39 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_5                    2 1 37 39 40 0=0
ReLU                     relu_107                 1 1 40 41
Split                    splitncnn_6              1 2 41 42 43
ConvolutionDepthWise     convdw_133               1 1 43 44 0=40 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=360 7=40
Convolution              conv_44                  1 1 44 45 0=40 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1600
BinaryOp                 add_6                    2 1 45 42 46 0=0
ReLU                     relu_108                 1 1 46 47
Split                    splitncnn_7              1 3 47 48 49 50
ConvolutionDepthWise     convdw_134               1 1 50 51 0=40 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=360 7=40
Convolution              convrelu_1               1 1 51 52 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1280 9=1
Convolution              conv_46                  1 1 52 53 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_47                  1 1 53 54 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_99             1 1 49 55 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_48                  1 1 55 56 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2880
BinaryOp                 add_7                    2 1 54 56 57 0=0
ReLU                     relu_110                 1 1 57 58
Split                    splitncnn_8              1 2 58 59 60
ConvolutionDepthWise     convdw_135               1 1 60 61 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_2               1 1 61 62 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_50                  1 1 62 63 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_51                  1 1 63 64 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_8                    2 1 64 59 65 0=0
ReLU                     relu_112                 1 1 65 66
Split                    splitncnn_9              1 2 66 67 68
ConvolutionDepthWise     convdw_136               1 1 68 69 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_3               1 1 69 70 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_53                  1 1 70 71 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_54                  1 1 71 72 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_9                    2 1 72 67 73 0=0
ReLU                     relu_114                 1 1 73 74
Split                    splitncnn_10             1 3 74 75 76 77
ConvolutionDepthWise     convdw_137               1 1 77 78 0=72 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 7=72
Convolution              convrelu_4               1 1 78 79 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_56                  1 1 79 80 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_57                  1 1 80 81 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
Pooling                  maxpool2d_100            1 1 76 82 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_58                  1 1 82 83 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5184
BinaryOp                 add_10                   2 1 81 83 84 0=0
ReLU                     relu_116                 1 1 84 85
Split                    splitncnn_11             1 2 85 86 87
ConvolutionDepthWise     convdw_138               1 1 87 88 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_5               1 1 88 89 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_60                  1 1 89 90 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_61                  1 1 90 91 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_11                   2 1 91 86 92 0=0
ReLU                     relu_118                 1 1 92 93
Split                    splitncnn_12             1 2 93 94 95
ConvolutionDepthWise     convdw_139               1 1 95 96 0=72 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=648 7=72
Convolution              convrelu_6               1 1 96 97 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_63                  1 1 97 98 0=32 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=9216
Convolution              conv_64                  1 1 98 99 0=72 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_12                   2 1 99 94 100 0=0
ReLU                     relu_120                 1 1 100 101
Convolution              conv_65                  1 1 101 102 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1728
Swish                    silu_18                  1 1 102 103
Split                    splitncnn_13             1 2 103 104 105
Interp                   upsample_121             1 1 105 106 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_0                    2 1 106 75 107 0=0
ConvolutionDepthWise     convdwrelu_0             1 1 107 108 0=96 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=864 7=96 9=1
Convolution              convrelu_7               1 1 108 109 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Convolution              conv_67                  1 1 109 110 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_19                  1 1 110 111
Split                    splitncnn_14             1 2 111 112 113
Interp                   upsample_122             1 1 113 114 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_1                    2 1 114 48 115 0=0
ConvolutionDepthWise     convdwrelu_1             1 1 115 116 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64 9=1
Convolution              convrelu_8               1 1 116 117 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536 9=1
Split                    splitncnn_15             1 2 117 118 119
ConvolutionDepthWise     convdwrelu_2             1 1 119 120 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24 9=1
Convolution              convrelu_9               1 1 120 121 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576 9=1
BinaryOp                 add_13                   2 1 121 112 122 0=0
ConvolutionDepthWise     convdwrelu_3             1 1 122 123 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24 9=1
Convolution              convrelu_10              1 1 123 124 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576 9=1
Split                    splitncnn_16             1 2 124 125 126
ConvolutionDepthWise     convdwrelu_4             1 1 126 127 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24 9=1
Convolution              convrelu_11              1 1 127 128 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576 9=1
BinaryOp                 add_14                   2 1 128 104 129 0=0
ConvolutionDepthWise     convdwrelu_5             1 1 129 130 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24 9=1
Convolution              convrelu_12              1 1 130 131 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576 9=1
Convolution              conv_73                  1 1 118 132 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_20                  1 1 132 133
Split                    splitncnn_17             1 2 133 134 135
Convolution              conv_74                  1 1 125 136 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_21                  1 1 136 137
Split                    splitncnn_18             1 2 137 138 139
Convolution              conv_75                  1 1 131 140 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
Swish                    silu_22                  1 1 140 141
Split                    splitncnn_19             1 2 141 142 143
Convolution              conv_76                  1 1 135 144 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_23                  1 1 144 145
Convolution              conv_77                  1 1 145 146 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_24                  1 1 146 147
Convolution              conv_78                  1 1 134 148 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_25                  1 1 148 149
Convolution              conv_79                  1 1 149 150 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_26                  1 1 150 151
Split                    splitncnn_20             1 2 151 152 153
Convolution              conv_80                  1 1 139 154 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_27                  1 1 154 155
Convolution              conv_81                  1 1 155 156 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_28                  1 1 156 157
Convolution              conv_82                  1 1 138 158 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_29                  1 1 158 159
Convolution              conv_83                  1 1 159 160 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_30                  1 1 160 161
Split                    splitncnn_21             1 2 161 162 163
Convolution              conv_84                  1 1 143 164 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_31                  1 1 164 165
Convolution              conv_85                  1 1 165 166 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_32                  1 1 166 167
Convolution              conv_86                  1 1 142 168 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_33                  1 1 168 169
Convolution              conv_87                  1 1 169 170 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Swish                    silu_34                  1 1 170 171
Split                    splitncnn_22             1 2 171 172 173
Convolution              conv_89                  1 1 153 174 0=4 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=96
Convolution              convsigmoid_13           1 1 152 175 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=24 9=4
Convolution              convsigmoid_14           1 1 147 176 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48 9=4
Concat                   cat_2                    3 1 174 175 176 177 0=0
Convolution              conv_92                  1 1 163 178 0=4 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=96
Convolution              convsigmoid_15           1 1 162 179 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=24 9=4
Convolution              convsigmoid_16           1 1 157 180 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48 9=4
Concat                   cat_3                    3 1 178 179 180 181 0=0
Convolution              conv_95                  1 1 173 182 0=4 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=96
Convolution              convsigmoid_17           1 1 172 183 0=1 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=24 9=4
Convolution              convsigmoid_18           1 1 167 184 0=2 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=48 9=4
Concat                   cat_4                    3 1 182 183 184 185 0=0
Reshape                  flatten_124              1 1 177 186 0=-1 1=7
Reshape                  flatten_125              1 1 181 187 0=-1 1=7
Reshape                  flatten_126              1 1 185 188 0=-1 1=7
Concat                   cat_5                    3 1 186 187 188 189 0=1
Permute                  tranpose_2               1 1 189 output 0=1