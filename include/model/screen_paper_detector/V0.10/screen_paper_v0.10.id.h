#ifndef NCNN_INCLUDE_GUARD_screen_paper_v0_10_id_h
#define NCNN_INCLUDE_GUARD_screen_paper_v0_10_id_h
namespace screen_paper_v0_10_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_convrelu_0 = 1;
const int BLOB_1 = 1;
const int LAYER_splitncnn_0 = 2;
const int BLOB_2 = 2;
const int BLOB_3 = 3;
const int LAYER_convdw_127 = 3;
const int BLOB_4 = 4;
const int LAYER_conv_36 = 4;
const int BLOB_5 = 5;
const int LAYER_add_0 = 5;
const int BLOB_6 = 6;
const int LAYER_relu_102 = 6;
const int BLOB_7 = 7;
const int LAYER_splitncnn_1 = 7;
const int BLOB_8 = 8;
const int BLOB_9 = 9;
const int LAYER_convdw_128 = 8;
const int BLOB_10 = 10;
const int LAYER_conv_37 = 9;
const int BLOB_11 = 11;
const int LAYER_add_1 = 10;
const int BLOB_12 = 12;
const int LAYER_relu_103 = 11;
const int BLOB_13 = 13;
const int LAYER_splitncnn_2 = 12;
const int BLOB_14 = 14;
const int BLOB_15 = 15;
const int LAYER_convdw_129 = 13;
const int BLOB_16 = 16;
const int LAYER_conv_38 = 14;
const int BLOB_17 = 17;
const int LAYER_maxpool2d_97 = 15;
const int BLOB_18 = 18;
const int LAYER_conv_39 = 16;
const int BLOB_19 = 19;
const int LAYER_add_2 = 17;
const int BLOB_20 = 20;
const int LAYER_relu_104 = 18;
const int BLOB_21 = 21;
const int LAYER_splitncnn_3 = 19;
const int BLOB_22 = 22;
const int BLOB_23 = 23;
const int LAYER_convdw_130 = 20;
const int BLOB_24 = 24;
const int LAYER_conv_40 = 21;
const int BLOB_25 = 25;
const int LAYER_add_3 = 22;
const int BLOB_26 = 26;
const int LAYER_relu_105 = 23;
const int BLOB_27 = 27;
const int LAYER_splitncnn_4 = 24;
const int BLOB_28 = 28;
const int BLOB_29 = 29;
const int LAYER_convdw_131 = 25;
const int BLOB_30 = 30;
const int LAYER_conv_41 = 26;
const int BLOB_31 = 31;
const int LAYER_add_4 = 27;
const int BLOB_32 = 32;
const int LAYER_relu_106 = 28;
const int BLOB_33 = 33;
const int LAYER_splitncnn_5 = 29;
const int BLOB_34 = 34;
const int BLOB_35 = 35;
const int LAYER_convdw_132 = 30;
const int BLOB_36 = 36;
const int LAYER_conv_42 = 31;
const int BLOB_37 = 37;
const int LAYER_maxpool2d_98 = 32;
const int BLOB_38 = 38;
const int LAYER_conv_43 = 33;
const int BLOB_39 = 39;
const int LAYER_add_5 = 34;
const int BLOB_40 = 40;
const int LAYER_relu_107 = 35;
const int BLOB_41 = 41;
const int LAYER_splitncnn_6 = 36;
const int BLOB_42 = 42;
const int BLOB_43 = 43;
const int LAYER_convdw_133 = 37;
const int BLOB_44 = 44;
const int LAYER_conv_44 = 38;
const int BLOB_45 = 45;
const int LAYER_add_6 = 39;
const int BLOB_46 = 46;
const int LAYER_relu_108 = 40;
const int BLOB_47 = 47;
const int LAYER_splitncnn_7 = 41;
const int BLOB_48 = 48;
const int BLOB_49 = 49;
const int BLOB_50 = 50;
const int LAYER_convdw_134 = 42;
const int BLOB_51 = 51;
const int LAYER_convrelu_1 = 43;
const int BLOB_52 = 52;
const int LAYER_conv_46 = 44;
const int BLOB_53 = 53;
const int LAYER_conv_47 = 45;
const int BLOB_54 = 54;
const int LAYER_maxpool2d_99 = 46;
const int BLOB_55 = 55;
const int LAYER_conv_48 = 47;
const int BLOB_56 = 56;
const int LAYER_add_7 = 48;
const int BLOB_57 = 57;
const int LAYER_relu_110 = 49;
const int BLOB_58 = 58;
const int LAYER_splitncnn_8 = 50;
const int BLOB_59 = 59;
const int BLOB_60 = 60;
const int LAYER_convdw_135 = 51;
const int BLOB_61 = 61;
const int LAYER_convrelu_2 = 52;
const int BLOB_62 = 62;
const int LAYER_conv_50 = 53;
const int BLOB_63 = 63;
const int LAYER_conv_51 = 54;
const int BLOB_64 = 64;
const int LAYER_add_8 = 55;
const int BLOB_65 = 65;
const int LAYER_relu_112 = 56;
const int BLOB_66 = 66;
const int LAYER_splitncnn_9 = 57;
const int BLOB_67 = 67;
const int BLOB_68 = 68;
const int LAYER_convdw_136 = 58;
const int BLOB_69 = 69;
const int LAYER_convrelu_3 = 59;
const int BLOB_70 = 70;
const int LAYER_conv_53 = 60;
const int BLOB_71 = 71;
const int LAYER_conv_54 = 61;
const int BLOB_72 = 72;
const int LAYER_add_9 = 62;
const int BLOB_73 = 73;
const int LAYER_relu_114 = 63;
const int BLOB_74 = 74;
const int LAYER_splitncnn_10 = 64;
const int BLOB_75 = 75;
const int BLOB_76 = 76;
const int BLOB_77 = 77;
const int LAYER_convdw_137 = 65;
const int BLOB_78 = 78;
const int LAYER_convrelu_4 = 66;
const int BLOB_79 = 79;
const int LAYER_conv_56 = 67;
const int BLOB_80 = 80;
const int LAYER_conv_57 = 68;
const int BLOB_81 = 81;
const int LAYER_maxpool2d_100 = 69;
const int BLOB_82 = 82;
const int LAYER_conv_58 = 70;
const int BLOB_83 = 83;
const int LAYER_add_10 = 71;
const int BLOB_84 = 84;
const int LAYER_relu_116 = 72;
const int BLOB_85 = 85;
const int LAYER_splitncnn_11 = 73;
const int BLOB_86 = 86;
const int BLOB_87 = 87;
const int LAYER_convdw_138 = 74;
const int BLOB_88 = 88;
const int LAYER_convrelu_5 = 75;
const int BLOB_89 = 89;
const int LAYER_conv_60 = 76;
const int BLOB_90 = 90;
const int LAYER_conv_61 = 77;
const int BLOB_91 = 91;
const int LAYER_add_11 = 78;
const int BLOB_92 = 92;
const int LAYER_relu_118 = 79;
const int BLOB_93 = 93;
const int LAYER_splitncnn_12 = 80;
const int BLOB_94 = 94;
const int BLOB_95 = 95;
const int LAYER_convdw_139 = 81;
const int BLOB_96 = 96;
const int LAYER_convrelu_6 = 82;
const int BLOB_97 = 97;
const int LAYER_conv_63 = 83;
const int BLOB_98 = 98;
const int LAYER_conv_64 = 84;
const int BLOB_99 = 99;
const int LAYER_add_12 = 85;
const int BLOB_100 = 100;
const int LAYER_relu_120 = 86;
const int BLOB_101 = 101;
const int LAYER_conv_65 = 87;
const int BLOB_102 = 102;
const int LAYER_silu_18 = 88;
const int BLOB_103 = 103;
const int LAYER_splitncnn_13 = 89;
const int BLOB_104 = 104;
const int BLOB_105 = 105;
const int LAYER_upsample_121 = 90;
const int BLOB_106 = 106;
const int LAYER_cat_0 = 91;
const int BLOB_107 = 107;
const int LAYER_convdwrelu_0 = 92;
const int BLOB_108 = 108;
const int LAYER_convrelu_7 = 93;
const int BLOB_109 = 109;
const int LAYER_conv_67 = 94;
const int BLOB_110 = 110;
const int LAYER_silu_19 = 95;
const int BLOB_111 = 111;
const int LAYER_splitncnn_14 = 96;
const int BLOB_112 = 112;
const int BLOB_113 = 113;
const int LAYER_upsample_122 = 97;
const int BLOB_114 = 114;
const int LAYER_cat_1 = 98;
const int BLOB_115 = 115;
const int LAYER_convdwrelu_1 = 99;
const int BLOB_116 = 116;
const int LAYER_convrelu_8 = 100;
const int BLOB_117 = 117;
const int LAYER_splitncnn_15 = 101;
const int BLOB_118 = 118;
const int BLOB_119 = 119;
const int LAYER_convdwrelu_2 = 102;
const int BLOB_120 = 120;
const int LAYER_convrelu_9 = 103;
const int BLOB_121 = 121;
const int LAYER_add_13 = 104;
const int BLOB_122 = 122;
const int LAYER_convdwrelu_3 = 105;
const int BLOB_123 = 123;
const int LAYER_convrelu_10 = 106;
const int BLOB_124 = 124;
const int LAYER_splitncnn_16 = 107;
const int BLOB_125 = 125;
const int BLOB_126 = 126;
const int LAYER_convdwrelu_4 = 108;
const int BLOB_127 = 127;
const int LAYER_convrelu_11 = 109;
const int BLOB_128 = 128;
const int LAYER_add_14 = 110;
const int BLOB_129 = 129;
const int LAYER_convdwrelu_5 = 111;
const int BLOB_130 = 130;
const int LAYER_convrelu_12 = 112;
const int BLOB_131 = 131;
const int LAYER_conv_73 = 113;
const int BLOB_132 = 132;
const int LAYER_silu_20 = 114;
const int BLOB_133 = 133;
const int LAYER_splitncnn_17 = 115;
const int BLOB_134 = 134;
const int BLOB_135 = 135;
const int LAYER_conv_74 = 116;
const int BLOB_136 = 136;
const int LAYER_silu_21 = 117;
const int BLOB_137 = 137;
const int LAYER_splitncnn_18 = 118;
const int BLOB_138 = 138;
const int BLOB_139 = 139;
const int LAYER_conv_75 = 119;
const int BLOB_140 = 140;
const int LAYER_silu_22 = 120;
const int BLOB_141 = 141;
const int LAYER_splitncnn_19 = 121;
const int BLOB_142 = 142;
const int BLOB_143 = 143;
const int LAYER_conv_76 = 122;
const int BLOB_144 = 144;
const int LAYER_silu_23 = 123;
const int BLOB_145 = 145;
const int LAYER_conv_77 = 124;
const int BLOB_146 = 146;
const int LAYER_silu_24 = 125;
const int BLOB_147 = 147;
const int LAYER_conv_78 = 126;
const int BLOB_148 = 148;
const int LAYER_silu_25 = 127;
const int BLOB_149 = 149;
const int LAYER_conv_79 = 128;
const int BLOB_150 = 150;
const int LAYER_silu_26 = 129;
const int BLOB_151 = 151;
const int LAYER_splitncnn_20 = 130;
const int BLOB_152 = 152;
const int BLOB_153 = 153;
const int LAYER_conv_80 = 131;
const int BLOB_154 = 154;
const int LAYER_silu_27 = 132;
const int BLOB_155 = 155;
const int LAYER_conv_81 = 133;
const int BLOB_156 = 156;
const int LAYER_silu_28 = 134;
const int BLOB_157 = 157;
const int LAYER_conv_82 = 135;
const int BLOB_158 = 158;
const int LAYER_silu_29 = 136;
const int BLOB_159 = 159;
const int LAYER_conv_83 = 137;
const int BLOB_160 = 160;
const int LAYER_silu_30 = 138;
const int BLOB_161 = 161;
const int LAYER_splitncnn_21 = 139;
const int BLOB_162 = 162;
const int BLOB_163 = 163;
const int LAYER_conv_84 = 140;
const int BLOB_164 = 164;
const int LAYER_silu_31 = 141;
const int BLOB_165 = 165;
const int LAYER_conv_85 = 142;
const int BLOB_166 = 166;
const int LAYER_silu_32 = 143;
const int BLOB_167 = 167;
const int LAYER_conv_86 = 144;
const int BLOB_168 = 168;
const int LAYER_silu_33 = 145;
const int BLOB_169 = 169;
const int LAYER_conv_87 = 146;
const int BLOB_170 = 170;
const int LAYER_silu_34 = 147;
const int BLOB_171 = 171;
const int LAYER_splitncnn_22 = 148;
const int BLOB_172 = 172;
const int BLOB_173 = 173;
const int LAYER_conv_89 = 149;
const int BLOB_174 = 174;
const int LAYER_convsigmoid_13 = 150;
const int BLOB_175 = 175;
const int LAYER_convsigmoid_14 = 151;
const int BLOB_176 = 176;
const int LAYER_cat_2 = 152;
const int BLOB_177 = 177;
const int LAYER_conv_92 = 153;
const int BLOB_178 = 178;
const int LAYER_convsigmoid_15 = 154;
const int BLOB_179 = 179;
const int LAYER_convsigmoid_16 = 155;
const int BLOB_180 = 180;
const int LAYER_cat_3 = 156;
const int BLOB_181 = 181;
const int LAYER_conv_95 = 157;
const int BLOB_182 = 182;
const int LAYER_convsigmoid_17 = 158;
const int BLOB_183 = 183;
const int LAYER_convsigmoid_18 = 159;
const int BLOB_184 = 184;
const int LAYER_cat_4 = 160;
const int BLOB_185 = 185;
const int LAYER_flatten_124 = 161;
const int BLOB_186 = 186;
const int LAYER_flatten_125 = 162;
const int BLOB_187 = 187;
const int LAYER_flatten_126 = 163;
const int BLOB_188 = 188;
const int LAYER_cat_5 = 164;
const int BLOB_189 = 189;
const int LAYER_tranpose_2 = 165;
const int BLOB_output = 190;
} // namespace screen_paper_v0_10_param_id
#endif // NCNN_INCLUDE_GUARD_screen_paper_v0_10_id_h
