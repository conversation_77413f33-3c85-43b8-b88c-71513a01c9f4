7767517
69 81
Input                    in0                      0 1 in0
Convolution              convrelu_0               1 1 in0 1 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=648 9=1
Split                    splitncnn_0              1 2 1 2 3
ConvolutionDepthWise     convdw_48                1 1 3 4 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=216 7=24
Convolution              conv_3                   1 1 4 5 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=576
BinaryOp                 add_0                    2 1 5 2 6 0=0
ReLU                     relu_34                  1 1 6 7
Split                    splitncnn_1              1 2 7 8 9
ConvolutionDepthWise     convdw_49                1 1 9 10 0=24 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=216 7=24
Convolution              conv_4                   1 1 10 11 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1152
Pooling                  maxpool2d_27             1 1 8 12 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_5                   1 1 12 13 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1152
BinaryOp                 add_1                    2 1 11 13 14 0=0
ReLU                     relu_35                  1 1 14 15
Split                    splitncnn_2              1 2 15 16 17
ConvolutionDepthWise     convdw_50                1 1 17 18 0=48 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=432 7=48
Convolution              conv_6                   1 1 18 19 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304
BinaryOp                 add_2                    2 1 19 16 20 0=0
ReLU                     relu_36                  1 1 20 21
Split                    splitncnn_3              1 2 21 22 23
ConvolutionDepthWise     convdw_51                1 1 23 24 0=48 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=432 7=48
Convolution              convrelu_1               1 1 24 25 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1152 9=1
Convolution              conv_8                   1 1 25 26 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Convolution              conv_9                   1 1 26 27 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Pooling                  maxpool2d_28             1 1 22 28 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_10                  1 1 28 29 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=3072
BinaryOp                 add_3                    2 1 27 29 30 0=0
ReLU                     relu_38                  1 1 30 31
Split                    splitncnn_4              1 2 31 32 33
ConvolutionDepthWise     convdw_52                1 1 33 34 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Convolution              convrelu_2               1 1 34 35 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536 9=1
Convolution              conv_12                  1 1 35 36 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Convolution              conv_13                  1 1 36 37 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
BinaryOp                 add_4                    2 1 37 32 38 0=0
ReLU                     relu_40                  1 1 38 39
Split                    splitncnn_5              1 3 39 40 41 42
ConvolutionDepthWise     convdw_53                1 1 42 43 0=64 1=3 11=3 12=1 13=2 14=1 2=1 3=2 4=1 5=1 6=576 7=64
Convolution              convrelu_3               1 1 43 44 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536 9=1
Convolution              conv_15                  1 1 44 45 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Convolution              conv_16                  1 1 45 46 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
Pooling                  maxpool2d_29             1 1 41 47 0=0 1=2 11=2 12=2 13=0 2=2 3=0 5=1
Convolution              conv_17                  1 1 47 48 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=4096
BinaryOp                 add_5                    2 1 46 48 49 0=0
ReLU                     relu_42                  1 1 49 50
Split                    splitncnn_6              1 2 50 51 52
ConvolutionDepthWise     convdw_54                1 1 52 53 0=64 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=576 7=64
Convolution              convrelu_4               1 1 53 54 0=24 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536 9=1
Convolution              conv_19                  1 1 54 55 0=24 1=3 11=3 12=1 13=1 14=1 2=1 3=1 4=1 5=1 6=5184
Convolution              conv_20                  1 1 55 56 0=64 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1536
BinaryOp                 add_6                    2 1 56 51 57 0=0
ReLU                     relu_44                  1 1 57 58
Convolution              conv_21                  1 1 58 59 0=32 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2048
Swish                    silu_0                   1 1 59 60
Split                    splitncnn_7              1 2 60 61 62
Pooling                  maxpool2d_30             1 1 62 63 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_8              1 2 63 64 65
Pooling                  maxpool2d_31             1 1 65 66 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Split                    splitncnn_9              1 2 66 67 68
Pooling                  maxpool2d_32             1 1 68 69 0=0 1=5 11=5 12=1 13=2 2=1 3=2 5=1
Concat                   cat_0                    4 1 61 64 67 69 70 0=0
Convolution              conv_22                  1 1 70 71 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=6144
Swish                    silu_1                   1 1 71 72
Split                    splitncnn_10             1 2 72 73 74
Convolution              convrelu_5               1 1 74 75 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=2304 9=1
Interp                   upsample_47              1 1 75 76 0=1 1=2.000000e+00 2=2.000000e+00 6=0
Concat                   cat_1                    2 1 76 40 77 0=0
Convolution              convrelu_6               1 1 77 78 0=48 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=5376 9=1
Convolution              conv_25                  1 1 78 out0 0=21 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1008
Convolution              conv_26                  1 1 73 out1 0=21 1=1 11=1 12=1 13=1 14=0 2=1 3=1 4=0 5=1 6=1008
