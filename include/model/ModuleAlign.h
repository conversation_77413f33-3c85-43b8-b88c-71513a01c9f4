//#pragma once
#ifndef _FACEALIGN_H
#define _FACEALIGN_H

#include <stdio.h>
#include <float.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
#include "BSTFaceUnlockConfig.h"

#define NORM_W  112
#define NORM_H  112
#define NORM_C  3
typedef struct ImageStruct
{
	int cols;
	int rows;
	unsigned char* data;
	int step;
}ImageStruct;

typedef struct ImageSize
{
	int width;
	int height;
}ImageSize;

void uv_warp_affine_gray(const ImageStruct &src_image,
	ImageStruct &dst_image,
	const ImageStruct &M,
	ImageSize dsize);

void uv_warp_affine_color(const ImageStruct &src_image,
	ImageStruct &dst_image,
	const ImageStruct &M,
	ImageSize dsize);

void transMatrixCalc(ImageStruct &transform, float* points_src);


//points_src 按照 x，y，x，y次序摆放


void landmarkAlign(float *points_src, float *points_dst);

bool faceAlign(unsigned char* img, int w, int h, int c, unsigned char* dst_img,int target_width,int target_height, Bbox cropBox);

#endif