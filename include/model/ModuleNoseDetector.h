/**  
 * All rights Reserved, Designed By MI
 * @projectName bstFaceUnlock
 * @title     ModuleNoseDetector   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 下午3:33  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//

#ifndef BSTFACEUNLOCK_ModuleNoseDETECTOR_H
#define BSTFACEUNLOCK_ModuleNoseDETECTOR_H
#include <stdio.h>

#include "ncnn/net.h"
#include "ncnn/mat.h"

#include "CommonDef.h"
#include "CommonUtils.h"
#include "BSTFaceUnlockConfig.h"

struct ModuleNoseParam {
    float iou_thd;
    float conf_thd;
    int   max_scale_num;
    float valid_thr;
    int   net_input_w;
    int   net_input_h;

    ModuleNoseParam()
        : iou_thd(0.45f), conf_thd(0.65f), max_scale_num(4), valid_thr(conf_thd), net_input_w(96), net_input_h(128) {
    }
};

class ModuleNose {
public:
    int  init();
    bool perfom(const unsigned char* const img, int img_w, int img_h, int c, BBox& cropBox, float score_TH,float face_brightness, LmkInfo& info);
    int  release();

private:
    void decode(float* data_ptr,  int fea_h, int fea_w,int feat_c, const int* const anchor, std::vector<Object>& prebox, float threshold, int stride);

    void decode(const ncnn::Mat& data, const int* const anchors, std::vector<Object>& prebox, float threshold, int stride);
    bool preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* const dst, int out_w, int out_h,float face_brightness, std::vector<float>& pad_info);

    ModuleNoseParam m_param;
    ncnn::Net         m_net;
    float             scale[3]     = {1 / 255.f, 1 / 255.f, 1 / 255.f};
    int               m_anchor0[6] = {5, 6, 12, 15, 27, 35};
    int               m_anchor1[6] = {13, 17, 44, 57, 84, 108};

    //    int               m_anchor2[6]   = {72, 97, 123, 164, 209, 297};
};

#endif //BSTFACEUNLOCK_ModuleNoseDETECTOR_H
