#ifndef NCNN_INCLUDE_GUARD_4_0_64x64_MobileNetHalf_Eyes_20221018_1014_id_h
#define NCNN_INCLUDE_GUARD_4_0_64x64_MobileNetHalf_Eyes_20221018_1014_id_h
namespace MobileNetHalf_Eyes_20221018_1014_param_id {
const int LAYER_input = 0;
const int BLOB_input = 0;
const int LAYER__header_conv1_header_conv1_0_Conv = 1;
const int BLOB__header_conv1_header_conv1_0_Conv_output_0 = 1;
const int LAYER__header_conv1_header_conv1_2_Relu = 2;
const int BLOB__header_conv1_header_conv1_2_Relu_output_0 = 2;
const int LAYER__block3_1_conv_pw_conv_Conv = 3;
const int BLOB__block3_1_conv_pw_conv_Conv_output_0 = 3;
const int LAYER__block3_1_conv_pw_relu_Relu = 4;
const int BLOB__block3_1_conv_pw_relu_Relu_output_0 = 4;
const int LAYER__block3_1_conv_dw_conv_Conv = 5;
const int BLOB__block3_1_conv_dw_conv_Conv_output_0 = 5;
const int LAYER__block3_1_conv_dw_relu_Relu = 6;
const int BLOB__block3_1_conv_dw_relu_Relu_output_0 = 6;
const int LAYER__block3_1_conv_dw_linear_conv_Conv = 7;
const int BLOB__block3_1_conv_dw_linear_conv_Conv_output_0 = 7;
const int LAYER_splitncnn_0 = 8;
const int BLOB__block3_1_conv_dw_linear_conv_Conv_output_0_splitncnn_0 = 8;
const int BLOB__block3_1_conv_dw_linear_conv_Conv_output_0_splitncnn_1 = 9;
const int LAYER__block3_2_conv_pw_conv_Conv = 9;
const int BLOB__block3_2_conv_pw_conv_Conv_output_0 = 10;
const int LAYER__block3_2_conv_pw_relu_Relu = 10;
const int BLOB__block3_2_conv_pw_relu_Relu_output_0 = 11;
const int LAYER__block3_2_conv_dw_conv_Conv = 11;
const int BLOB__block3_2_conv_dw_conv_Conv_output_0 = 12;
const int LAYER__block3_2_conv_dw_relu_Relu = 12;
const int BLOB__block3_2_conv_dw_relu_Relu_output_0 = 13;
const int LAYER__block3_2_conv_dw_linear_conv_Conv = 13;
const int BLOB__block3_2_conv_dw_linear_conv_Conv_output_0 = 14;
const int LAYER__block3_2_Add = 14;
const int BLOB__block3_2_Add_output_0 = 15;
const int LAYER__block4_1_conv_pw_conv_Conv = 15;
const int BLOB__block4_1_conv_pw_conv_Conv_output_0 = 16;
const int LAYER__block4_1_conv_pw_relu_Relu = 16;
const int BLOB__block4_1_conv_pw_relu_Relu_output_0 = 17;
const int LAYER__block4_1_conv_dw_conv_Conv = 17;
const int BLOB__block4_1_conv_dw_conv_Conv_output_0 = 18;
const int LAYER__block4_1_conv_dw_relu_Relu = 18;
const int BLOB__block4_1_conv_dw_relu_Relu_output_0 = 19;
const int LAYER__block4_1_conv_dw_linear_conv_Conv = 19;
const int BLOB__block4_1_conv_dw_linear_conv_Conv_output_0 = 20;
const int LAYER__block5_1_conv_pw_conv_Conv = 20;
const int BLOB__block5_1_conv_pw_conv_Conv_output_0 = 21;
const int LAYER__block5_1_conv_pw_relu_Relu = 21;
const int BLOB__block5_1_conv_pw_relu_Relu_output_0 = 22;
const int LAYER__block5_1_conv_dw_conv_Conv = 22;
const int BLOB__block5_1_conv_dw_conv_Conv_output_0 = 23;
const int LAYER__block5_1_conv_dw_relu_Relu = 23;
const int BLOB__block5_1_conv_dw_relu_Relu_output_0 = 24;
const int LAYER__block5_1_conv_dw_linear_conv_Conv = 24;
const int BLOB__block5_1_conv_dw_linear_conv_Conv_output_0 = 25;
const int LAYER_splitncnn_1 = 25;
const int BLOB__block5_1_conv_dw_linear_conv_Conv_output_0_splitncnn_0 = 26;
const int BLOB__block5_1_conv_dw_linear_conv_Conv_output_0_splitncnn_1 = 27;
const int LAYER__block5_2_conv_pw_conv_Conv = 26;
const int BLOB__block5_2_conv_pw_conv_Conv_output_0 = 28;
const int LAYER__block5_2_conv_pw_relu_Relu = 27;
const int BLOB__block5_2_conv_pw_relu_Relu_output_0 = 29;
const int LAYER__block5_2_conv_dw_conv_Conv = 28;
const int BLOB__block5_2_conv_dw_conv_Conv_output_0 = 30;
const int LAYER__block5_2_conv_dw_relu_Relu = 29;
const int BLOB__block5_2_conv_dw_relu_Relu_output_0 = 31;
const int LAYER__block5_2_conv_dw_linear_conv_Conv = 30;
const int BLOB__block5_2_conv_dw_linear_conv_Conv_output_0 = 32;
const int LAYER__block5_2_Add = 31;
const int BLOB__block5_2_Add_output_0 = 33;
const int LAYER__block6_1_conv_pw_conv_Conv = 32;
const int BLOB__block6_1_conv_pw_conv_Conv_output_0 = 34;
const int LAYER__block6_1_conv_pw_relu_Relu = 33;
const int BLOB__block6_1_conv_pw_relu_Relu_output_0 = 35;
const int LAYER__block6_1_conv_dw_conv_Conv = 34;
const int BLOB__block6_1_conv_dw_conv_Conv_output_0 = 36;
const int LAYER__block6_1_conv_dw_relu_Relu = 35;
const int BLOB__block6_1_conv_dw_relu_Relu_output_0 = 37;
const int LAYER__block6_1_conv_dw_linear_conv_Conv = 36;
const int BLOB__block6_1_conv_dw_linear_conv_Conv_output_0 = 38;
const int LAYER__avgpool_GlobalAveragePool = 37;
const int BLOB__avgpool_GlobalAveragePool_output_0 = 39;
const int LAYER__Flatten = 38;
const int BLOB__Flatten_output_0 = 40;
const int LAYER__fc_Gemm = 39;
const int BLOB_out = 41;
} // namespace 4_0_64x64_MobileNetHalf_Eyes_20221018_1014_param_id
#endif // NCNN_INCLUDE_GUARD_4_0_64x64_MobileNetHalf_Eyes_20221018_1014_id_h
