7767517
40 42
Input            input                    0 1 input
Convolution      /header_conv1/header_conv1.0/Conv 1 1 input /header_conv1/header_conv1.0/Conv_output_0 0=32 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=864
ReLU             /header_conv1/header_conv1.2/Relu 1 1 /header_conv1/header_conv1.0/Conv_output_0 /header_conv1/header_conv1.2/Relu_output_0
Convolution      /block3_1/conv/pw_conv/Conv 1 1 /header_conv1/header_conv1.2/Relu_output_0 /block3_1/conv/pw_conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2048
ReLU             /block3_1/conv/pw_relu/Relu 1 1 /block3_1/conv/pw_conv/Conv_output_0 /block3_1/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block3_1/conv/dw_conv/Conv 1 1 /block3_1/conv/pw_relu/Relu_output_0 /block3_1/conv/dw_conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=576 7=64
ReLU             /block3_1/conv/dw_relu/Relu 1 1 /block3_1/conv/dw_conv/Conv_output_0 /block3_1/conv/dw_relu/Relu_output_0
Convolution      /block3_1/conv/dw_linear_conv/Conv 1 1 /block3_1/conv/dw_relu/Relu_output_0 /block3_1/conv/dw_linear_conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
Split            splitncnn_0              1 2 /block3_1/conv/dw_linear_conv/Conv_output_0 /block3_1/conv/dw_linear_conv/Conv_output_0_splitncnn_0 /block3_1/conv/dw_linear_conv/Conv_output_0_splitncnn_1
Convolution      /block3_2/conv/pw_conv/Conv 1 1 /block3_1/conv/dw_linear_conv/Conv_output_0_splitncnn_1 /block3_2/conv/pw_conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
ReLU             /block3_2/conv/pw_relu/Relu 1 1 /block3_2/conv/pw_conv/Conv_output_0 /block3_2/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block3_2/conv/dw_conv/Conv 1 1 /block3_2/conv/pw_relu/Relu_output_0 /block3_2/conv/dw_conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=576 7=64
ReLU             /block3_2/conv/dw_relu/Relu 1 1 /block3_2/conv/dw_conv/Conv_output_0 /block3_2/conv/dw_relu/Relu_output_0
Convolution      /block3_2/conv/dw_linear_conv/Conv 1 1 /block3_2/conv/dw_relu/Relu_output_0 /block3_2/conv/dw_linear_conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
BinaryOp         /block3_2/Add            2 1 /block3_1/conv/dw_linear_conv/Conv_output_0_splitncnn_0 /block3_2/conv/dw_linear_conv/Conv_output_0 /block3_2/Add_output_0 0=0
Convolution      /block4_1/conv/pw_conv/Conv 1 1 /block3_2/Add_output_0 /block4_1/conv/pw_conv/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
ReLU             /block4_1/conv/pw_relu/Relu 1 1 /block4_1/conv/pw_conv/Conv_output_0 /block4_1/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block4_1/conv/dw_conv/Conv 1 1 /block4_1/conv/pw_relu/Relu_output_0 /block4_1/conv/dw_conv/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=576 7=64
ReLU             /block4_1/conv/dw_relu/Relu 1 1 /block4_1/conv/dw_conv/Conv_output_0 /block4_1/conv/dw_relu/Relu_output_0
Convolution      /block4_1/conv/dw_linear_conv/Conv 1 1 /block4_1/conv/dw_relu/Relu_output_0 /block4_1/conv/dw_linear_conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=8192
Convolution      /block5_1/conv/pw_conv/Conv 1 1 /block4_1/conv/dw_linear_conv/Conv_output_0 /block5_1/conv/pw_conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
ReLU             /block5_1/conv/pw_relu/Relu 1 1 /block5_1/conv/pw_conv/Conv_output_0 /block5_1/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block5_1/conv/dw_conv/Conv 1 1 /block5_1/conv/pw_relu/Relu_output_0 /block5_1/conv/dw_conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=2304 7=256
ReLU             /block5_1/conv/dw_relu/Relu 1 1 /block5_1/conv/dw_conv/Conv_output_0 /block5_1/conv/dw_relu/Relu_output_0
Convolution      /block5_1/conv/dw_linear_conv/Conv 1 1 /block5_1/conv/dw_relu/Relu_output_0 /block5_1/conv/dw_linear_conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Split            splitncnn_1              1 2 /block5_1/conv/dw_linear_conv/Conv_output_0 /block5_1/conv/dw_linear_conv/Conv_output_0_splitncnn_0 /block5_1/conv/dw_linear_conv/Conv_output_0_splitncnn_1
Convolution      /block5_2/conv/pw_conv/Conv 1 1 /block5_1/conv/dw_linear_conv/Conv_output_0_splitncnn_1 /block5_2/conv/pw_conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
ReLU             /block5_2/conv/pw_relu/Relu 1 1 /block5_2/conv/pw_conv/Conv_output_0 /block5_2/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block5_2/conv/dw_conv/Conv 1 1 /block5_2/conv/pw_relu/Relu_output_0 /block5_2/conv/dw_conv/Conv_output_0 0=256 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=2304 7=256
ReLU             /block5_2/conv/dw_relu/Relu 1 1 /block5_2/conv/dw_conv/Conv_output_0 /block5_2/conv/dw_relu/Relu_output_0
Convolution      /block5_2/conv/dw_linear_conv/Conv 1 1 /block5_2/conv/dw_relu/Relu_output_0 /block5_2/conv/dw_linear_conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
BinaryOp         /block5_2/Add            2 1 /block5_1/conv/dw_linear_conv/Conv_output_0_splitncnn_0 /block5_2/conv/dw_linear_conv/Conv_output_0 /block5_2/Add_output_0 0=0
Convolution      /block6_1/conv/pw_conv/Conv 1 1 /block5_2/Add_output_0 /block6_1/conv/pw_conv/Conv_output_0 0=128 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=16384
ReLU             /block6_1/conv/pw_relu/Relu 1 1 /block6_1/conv/pw_conv/Conv_output_0 /block6_1/conv/pw_relu/Relu_output_0
ConvolutionDepthWise /block6_1/conv/dw_conv/Conv 1 1 /block6_1/conv/pw_relu/Relu_output_0 /block6_1/conv/dw_conv/Conv_output_0 0=128 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=1152 7=128
ReLU             /block6_1/conv/dw_relu/Relu 1 1 /block6_1/conv/dw_conv/Conv_output_0 /block6_1/conv/dw_relu/Relu_output_0
Convolution      /block6_1/conv/dw_linear_conv/Conv 1 1 /block6_1/conv/dw_relu/Relu_output_0 /block6_1/conv/dw_linear_conv/Conv_output_0 0=256 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=32768
Pooling          /avgpool/GlobalAveragePool 1 1 /block6_1/conv/dw_linear_conv/Conv_output_0 /avgpool/GlobalAveragePool_output_0 0=1 4=1
Flatten          /Flatten                 1 1 /avgpool/GlobalAveragePool_output_0 /Flatten_output_0
InnerProduct     /fc/Gemm                 1 1 /Flatten_output_0 out 0=6 1=1 2=1536
