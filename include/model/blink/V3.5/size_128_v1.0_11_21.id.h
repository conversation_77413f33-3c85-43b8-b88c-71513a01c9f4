#ifndef NCNN_INCLUDE_GUARD_size_128_v1_0_11_21_id_h
#define NCNN_INCLUDE_GUARD_size_128_v1_0_11_21_id_h
namespace size_128_v1_0_11_21_param_id {
const int LAYER_in0 = 0;
const int BLOB_in0 = 0;
const int LAYER_convrelu_0 = 1;
const int BLOB_1 = 1;
const int LAYER_splitncnn_0 = 2;
const int BLOB_2 = 2;
const int BLOB_3 = 3;
const int LAYER_convdw_48 = 3;
const int BLOB_4 = 4;
const int LAYER_conv_3 = 4;
const int BLOB_5 = 5;
const int LAYER_add_0 = 5;
const int BLOB_6 = 6;
const int LAYER_relu_34 = 6;
const int BLOB_7 = 7;
const int LAYER_splitncnn_1 = 7;
const int BLOB_8 = 8;
const int BLOB_9 = 9;
const int LAYER_convdw_49 = 8;
const int BLOB_10 = 10;
const int LAYER_conv_4 = 9;
const int BLOB_11 = 11;
const int LAYER_maxpool2d_27 = 10;
const int BLOB_12 = 12;
const int LAYER_conv_5 = 11;
const int BLOB_13 = 13;
const int LAYER_add_1 = 12;
const int BLOB_14 = 14;
const int LAYER_relu_35 = 13;
const int BLOB_15 = 15;
const int LAYER_splitncnn_2 = 14;
const int BLOB_16 = 16;
const int BLOB_17 = 17;
const int LAYER_convdw_50 = 15;
const int BLOB_18 = 18;
const int LAYER_conv_6 = 16;
const int BLOB_19 = 19;
const int LAYER_add_2 = 17;
const int BLOB_20 = 20;
const int LAYER_relu_36 = 18;
const int BLOB_21 = 21;
const int LAYER_splitncnn_3 = 19;
const int BLOB_22 = 22;
const int BLOB_23 = 23;
const int LAYER_convdw_51 = 20;
const int BLOB_24 = 24;
const int LAYER_convrelu_1 = 21;
const int BLOB_25 = 25;
const int LAYER_conv_8 = 22;
const int BLOB_26 = 26;
const int LAYER_conv_9 = 23;
const int BLOB_27 = 27;
const int LAYER_maxpool2d_28 = 24;
const int BLOB_28 = 28;
const int LAYER_conv_10 = 25;
const int BLOB_29 = 29;
const int LAYER_add_3 = 26;
const int BLOB_30 = 30;
const int LAYER_relu_38 = 27;
const int BLOB_31 = 31;
const int LAYER_splitncnn_4 = 28;
const int BLOB_32 = 32;
const int BLOB_33 = 33;
const int LAYER_convdw_52 = 29;
const int BLOB_34 = 34;
const int LAYER_convrelu_2 = 30;
const int BLOB_35 = 35;
const int LAYER_conv_12 = 31;
const int BLOB_36 = 36;
const int LAYER_conv_13 = 32;
const int BLOB_37 = 37;
const int LAYER_add_4 = 33;
const int BLOB_38 = 38;
const int LAYER_relu_40 = 34;
const int BLOB_39 = 39;
const int LAYER_splitncnn_5 = 35;
const int BLOB_40 = 40;
const int BLOB_41 = 41;
const int BLOB_42 = 42;
const int LAYER_convdw_53 = 36;
const int BLOB_43 = 43;
const int LAYER_convrelu_3 = 37;
const int BLOB_44 = 44;
const int LAYER_conv_15 = 38;
const int BLOB_45 = 45;
const int LAYER_conv_16 = 39;
const int BLOB_46 = 46;
const int LAYER_maxpool2d_29 = 40;
const int BLOB_47 = 47;
const int LAYER_conv_17 = 41;
const int BLOB_48 = 48;
const int LAYER_add_5 = 42;
const int BLOB_49 = 49;
const int LAYER_relu_42 = 43;
const int BLOB_50 = 50;
const int LAYER_splitncnn_6 = 44;
const int BLOB_51 = 51;
const int BLOB_52 = 52;
const int LAYER_convdw_54 = 45;
const int BLOB_53 = 53;
const int LAYER_convrelu_4 = 46;
const int BLOB_54 = 54;
const int LAYER_conv_19 = 47;
const int BLOB_55 = 55;
const int LAYER_conv_20 = 48;
const int BLOB_56 = 56;
const int LAYER_add_6 = 49;
const int BLOB_57 = 57;
const int LAYER_relu_44 = 50;
const int BLOB_58 = 58;
const int LAYER_conv_21 = 51;
const int BLOB_59 = 59;
const int LAYER_silu_0 = 52;
const int BLOB_60 = 60;
const int LAYER_splitncnn_7 = 53;
const int BLOB_61 = 61;
const int BLOB_62 = 62;
const int LAYER_maxpool2d_30 = 54;
const int BLOB_63 = 63;
const int LAYER_splitncnn_8 = 55;
const int BLOB_64 = 64;
const int BLOB_65 = 65;
const int LAYER_maxpool2d_31 = 56;
const int BLOB_66 = 66;
const int LAYER_splitncnn_9 = 57;
const int BLOB_67 = 67;
const int BLOB_68 = 68;
const int LAYER_maxpool2d_32 = 58;
const int BLOB_69 = 69;
const int LAYER_cat_0 = 59;
const int BLOB_70 = 70;
const int LAYER_conv_22 = 60;
const int BLOB_71 = 71;
const int LAYER_silu_1 = 61;
const int BLOB_72 = 72;
const int LAYER_splitncnn_10 = 62;
const int BLOB_73 = 73;
const int BLOB_74 = 74;
const int LAYER_convrelu_5 = 63;
const int BLOB_75 = 75;
const int LAYER_upsample_47 = 64;
const int BLOB_76 = 76;
const int LAYER_cat_1 = 65;
const int BLOB_77 = 77;
const int LAYER_convrelu_6 = 66;
const int BLOB_78 = 78;
const int LAYER_conv_25 = 67;
const int BLOB_out0 = 79;
const int LAYER_conv_26 = 68;
const int BLOB_out1 = 80;
} // namespace size_128_v1_0_11_21_param_id
#endif // NCNN_INCLUDE_GUARD_size_128_v1_0_11_21_id_h
