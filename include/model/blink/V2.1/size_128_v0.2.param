7767517
76 88
Input            data                     0 1 data
Convolution      /model.0/conv/Conv       1 1 data /model.0/conv/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=648
ReLU             /model.0/act/Relu        1 1 /model.0/conv/Conv_output_0 /model.0/act/Relu_output_0
Split            splitncnn_0              1 2 /model.0/act/Relu_output_0 /model.0/act/Relu_output_0_splitncnn_0 /model.0/act/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.1/branch1/branch1.0/Conv 1 1 /model.0/act/Relu_output_0_splitncnn_1 /model.1/branch1/branch1.0/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=216 7=24
Convolution      /model.1/branch1/branch1.2/Conv 1 1 /model.1/branch1/branch1.0/Conv_output_0 /model.1/branch1/branch1.2/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=576
BinaryOp         /model.1/Add             2 1 /model.1/branch1/branch1.2/Conv_output_0 /model.0/act/Relu_output_0_splitncnn_0 /model.1/Add_output_0 0=0
ReLU             /model.1/relu/Relu       1 1 /model.1/Add_output_0 /model.1/relu/Relu_output_0
Split            splitncnn_1              1 2 /model.1/relu/Relu_output_0 /model.1/relu/Relu_output_0_splitncnn_0 /model.1/relu/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.2/branch1/branch1.0/Conv 1 1 /model.1/relu/Relu_output_0_splitncnn_1 /model.2/branch1/branch1.0/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=216 7=24
Convolution      /model.2/branch1/branch1.2/Conv 1 1 /model.2/branch1/branch1.0/Conv_output_0 /model.2/branch1/branch1.2/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
Pooling          /model.2/shortcut/shortcut.0/MaxPool 1 1 /model.1/relu/Relu_output_0_splitncnn_0 /model.2/shortcut/shortcut.0/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      /model.2/shortcut/shortcut.1/Conv 1 1 /model.2/shortcut/shortcut.0/MaxPool_output_0 /model.2/shortcut/shortcut.1/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
BinaryOp         /model.2/Add             2 1 /model.2/branch1/branch1.2/Conv_output_0 /model.2/shortcut/shortcut.1/Conv_output_0 /model.2/Add_output_0 0=0
ReLU             /model.2/relu/Relu       1 1 /model.2/Add_output_0 /model.2/relu/Relu_output_0
Split            splitncnn_2              1 2 /model.2/relu/Relu_output_0 /model.2/relu/Relu_output_0_splitncnn_0 /model.2/relu/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.3/branch1/branch1.0/Conv 1 1 /model.2/relu/Relu_output_0_splitncnn_1 /model.3/branch1/branch1.0/Conv_output_0 0=48 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=432 7=48
Convolution      /model.3/branch1/branch1.2/Conv 1 1 /model.3/branch1/branch1.0/Conv_output_0 /model.3/branch1/branch1.2/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
BinaryOp         /model.3/Add             2 1 /model.3/branch1/branch1.2/Conv_output_0 /model.2/relu/Relu_output_0_splitncnn_0 /model.3/Add_output_0 0=0
ReLU             /model.3/relu/Relu       1 1 /model.3/Add_output_0 /model.3/relu/Relu_output_0
Split            splitncnn_3              1 2 /model.3/relu/Relu_output_0 /model.3/relu/Relu_output_0_splitncnn_0 /model.3/relu/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.4/branch1/branch1.0/Conv 1 1 /model.3/relu/Relu_output_0_splitncnn_1 /model.4/branch1/branch1.0/Conv_output_0 0=48 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=432 7=48
Convolution      /model.4/branch1/branch1.2/Conv 1 1 /model.4/branch1/branch1.0/Conv_output_0 /model.4/branch1/branch1.2/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
ReLU             /model.4/branch1/branch1.4/Relu 1 1 /model.4/branch1/branch1.2/Conv_output_0 /model.4/branch1/branch1.4/Relu_output_0
Convolution      /model.4/branch1/branch1.5/Conv 1 1 /model.4/branch1/branch1.4/Relu_output_0 /model.4/branch1/branch1.5/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Convolution      /model.4/branch1/branch1.7/Conv 1 1 /model.4/branch1/branch1.5/Conv_output_0 /model.4/branch1/branch1.7/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
Pooling          /model.4/shortcut/shortcut.0/MaxPool 1 1 /model.3/relu/Relu_output_0_splitncnn_0 /model.4/shortcut/shortcut.0/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      /model.4/shortcut/shortcut.1/Conv 1 1 /model.4/shortcut/shortcut.0/MaxPool_output_0 /model.4/shortcut/shortcut.1/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=3072
BinaryOp         /model.4/Add             2 1 /model.4/branch1/branch1.7/Conv_output_0 /model.4/shortcut/shortcut.1/Conv_output_0 /model.4/Add_output_0 0=0
ReLU             /model.4/relu/Relu       1 1 /model.4/Add_output_0 /model.4/relu/Relu_output_0
Split            splitncnn_4              1 2 /model.4/relu/Relu_output_0 /model.4/relu/Relu_output_0_splitncnn_0 /model.4/relu/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.5/branch1/branch1.0/Conv 1 1 /model.4/relu/Relu_output_0_splitncnn_1 /model.5/branch1/branch1.0/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=576 7=64
Convolution      /model.5/branch1/branch1.2/Conv 1 1 /model.5/branch1/branch1.0/Conv_output_0 /model.5/branch1/branch1.2/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
ReLU             /model.5/branch1/branch1.4/Relu 1 1 /model.5/branch1/branch1.2/Conv_output_0 /model.5/branch1/branch1.4/Relu_output_0
Convolution      /model.5/branch1/branch1.5/Conv 1 1 /model.5/branch1/branch1.4/Relu_output_0 /model.5/branch1/branch1.5/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Convolution      /model.5/branch1/branch1.7/Conv 1 1 /model.5/branch1/branch1.5/Conv_output_0 /model.5/branch1/branch1.7/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
BinaryOp         /model.5/Add             2 1 /model.5/branch1/branch1.7/Conv_output_0 /model.4/relu/Relu_output_0_splitncnn_0 /model.5/Add_output_0 0=0
ReLU             /model.5/relu/Relu       1 1 /model.5/Add_output_0 /model.5/relu/Relu_output_0
Split            splitncnn_5              1 3 /model.5/relu/Relu_output_0 /model.5/relu/Relu_output_0_splitncnn_0 /model.5/relu/Relu_output_0_splitncnn_1 /model.5/relu/Relu_output_0_splitncnn_2
ConvolutionDepthWise /model.6/branch1/branch1.0/Conv 1 1 /model.5/relu/Relu_output_0_splitncnn_2 /model.6/branch1/branch1.0/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=2 13=2 4=1 14=1 15=1 16=1 5=1 6=576 7=64
Convolution      /model.6/branch1/branch1.2/Conv 1 1 /model.6/branch1/branch1.0/Conv_output_0 /model.6/branch1/branch1.2/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
ReLU             /model.6/branch1/branch1.4/Relu 1 1 /model.6/branch1/branch1.2/Conv_output_0 /model.6/branch1/branch1.4/Relu_output_0
Convolution      /model.6/branch1/branch1.5/Conv 1 1 /model.6/branch1/branch1.4/Relu_output_0 /model.6/branch1/branch1.5/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Convolution      /model.6/branch1/branch1.7/Conv 1 1 /model.6/branch1/branch1.5/Conv_output_0 /model.6/branch1/branch1.7/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
Pooling          /model.6/shortcut/shortcut.0/MaxPool 1 1 /model.5/relu/Relu_output_0_splitncnn_1 /model.6/shortcut/shortcut.0/MaxPool_output_0 0=0 1=2 11=2 2=2 12=2 3=0 13=0 14=0 15=0 5=1
Convolution      /model.6/shortcut/shortcut.1/Conv 1 1 /model.6/shortcut/shortcut.0/MaxPool_output_0 /model.6/shortcut/shortcut.1/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=4096
BinaryOp         /model.6/Add             2 1 /model.6/branch1/branch1.7/Conv_output_0 /model.6/shortcut/shortcut.1/Conv_output_0 /model.6/Add_output_0 0=0
ReLU             /model.6/relu/Relu       1 1 /model.6/Add_output_0 /model.6/relu/Relu_output_0
Split            splitncnn_6              1 2 /model.6/relu/Relu_output_0 /model.6/relu/Relu_output_0_splitncnn_0 /model.6/relu/Relu_output_0_splitncnn_1
ConvolutionDepthWise /model.7/branch1/branch1.0/Conv 1 1 /model.6/relu/Relu_output_0_splitncnn_1 /model.7/branch1/branch1.0/Conv_output_0 0=64 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=576 7=64
Convolution      /model.7/branch1/branch1.2/Conv 1 1 /model.7/branch1/branch1.0/Conv_output_0 /model.7/branch1/branch1.2/Conv_output_0 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
ReLU             /model.7/branch1/branch1.4/Relu 1 1 /model.7/branch1/branch1.2/Conv_output_0 /model.7/branch1/branch1.4/Relu_output_0
Convolution      /model.7/branch1/branch1.5/Conv 1 1 /model.7/branch1/branch1.4/Relu_output_0 /model.7/branch1/branch1.5/Conv_output_0 0=24 1=3 11=3 2=1 12=1 3=1 13=1 4=1 14=1 15=1 16=1 5=1 6=5184
Convolution      /model.7/branch1/branch1.7/Conv 1 1 /model.7/branch1/branch1.5/Conv_output_0 /model.7/branch1/branch1.7/Conv_output_0 0=64 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1536
BinaryOp         /model.7/Add             2 1 /model.7/branch1/branch1.7/Conv_output_0 /model.6/relu/Relu_output_0_splitncnn_0 /model.7/Add_output_0 0=0
ReLU             /model.7/relu/Relu       1 1 /model.7/Add_output_0 /model.7/relu/Relu_output_0
Convolution      /model.8/cv1/conv/Conv   1 1 /model.7/relu/Relu_output_0 /model.8/cv1/conv/Conv_output_0 0=32 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2048
Swish            /model.8/cv1/act/Mul     1 1 /model.8/cv1/conv/Conv_output_0 /model.8/cv1/act/Mul_output_0
Split            splitncnn_7              1 2 /model.8/cv1/act/Mul_output_0 /model.8/cv1/act/Mul_output_0_splitncnn_0 /model.8/cv1/act/Mul_output_0_splitncnn_1
Pooling          /model.8/m/MaxPool       1 1 /model.8/cv1/act/Mul_output_0_splitncnn_1 /model.8/m/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Split            splitncnn_8              1 2 /model.8/m/MaxPool_output_0 /model.8/m/MaxPool_output_0_splitncnn_0 /model.8/m/MaxPool_output_0_splitncnn_1
Pooling          /model.8/m_1/MaxPool     1 1 /model.8/m/MaxPool_output_0_splitncnn_1 /model.8/m_1/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Split            splitncnn_9              1 2 /model.8/m_1/MaxPool_output_0 /model.8/m_1/MaxPool_output_0_splitncnn_0 /model.8/m_1/MaxPool_output_0_splitncnn_1
Pooling          /model.8/m_2/MaxPool     1 1 /model.8/m_1/MaxPool_output_0_splitncnn_1 /model.8/m_2/MaxPool_output_0 0=0 1=5 11=5 2=1 12=1 3=2 13=2 14=2 15=2 5=1
Concat           /model.8/Concat          4 1 /model.8/cv1/act/Mul_output_0_splitncnn_0 /model.8/m/MaxPool_output_0_splitncnn_0 /model.8/m_1/MaxPool_output_0_splitncnn_0 /model.8/m_2/MaxPool_output_0 /model.8/Concat_output_0 0=0
Convolution      /model.8/cv2/conv/Conv   1 1 /model.8/Concat_output_0 /model.8/cv2/conv/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=6144
Swish            /model.8/cv2/act/Mul     1 1 /model.8/cv2/conv/Conv_output_0 /model.8/cv2/act/Mul_output_0
Split            splitncnn_10             1 2 /model.8/cv2/act/Mul_output_0 /model.8/cv2/act/Mul_output_0_splitncnn_0 /model.8/cv2/act/Mul_output_0_splitncnn_1
Convolution      /model.9/conv/Conv       1 1 /model.8/cv2/act/Mul_output_0_splitncnn_1 /model.9/conv/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=2304
ReLU             /model.9/act/Relu        1 1 /model.9/conv/Conv_output_0 /model.9/act/Relu_output_0
Interp           /model.10/Resize         1 1 /model.9/act/Relu_output_0 /model.10/Resize_output_0 0=1 1=2.000000e+00 2=2.000000e+00 3=0 4=0 6=0
Concat           /model.11/Concat         2 1 /model.10/Resize_output_0 /model.5/relu/Relu_output_0_splitncnn_0 /model.11/Concat_output_0 0=0
Convolution      /model.12/conv/Conv      1 1 /model.11/Concat_output_0 /model.12/conv/Conv_output_0 0=48 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=5376
ReLU             /model.12/act/Relu       1 1 /model.12/conv/Conv_output_0 /model.12/act/Relu_output_0
Convolution      /model.13/m.0/Conv       1 1 /model.12/act/Relu_output_0 stride_8 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
Convolution      /model.13/m.1/Conv       1 1 /model.8/cv2/act/Mul_output_0_splitncnn_0 stride_16 0=24 1=1 11=1 2=1 12=1 3=1 13=1 4=0 14=0 15=0 16=0 5=1 6=1152
