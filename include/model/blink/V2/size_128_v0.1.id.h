#ifndef NCNN_INCLUDE_GUARD_size_128_v0_1_id_h
#define NCNN_INCLUDE_GUARD_size_128_v0_1_id_h
namespace size_128_v0_1_param_id {
const int LAYER_data = 0;
const int BLOB_data = 0;
const int LAYER__model_0_conv_Conv = 1;
const int BLOB__model_0_conv_Conv_output_0 = 1;
const int LAYER__model_0_act_Relu = 2;
const int BLOB__model_0_act_Relu_output_0 = 2;
const int LAYER_splitncnn_0 = 3;
const int BLOB__model_0_act_Relu_output_0_splitncnn_0 = 3;
const int BLOB__model_0_act_Relu_output_0_splitncnn_1 = 4;
const int LAYER__model_1_branch1_branch1_0_Conv = 4;
const int BLOB__model_1_branch1_branch1_0_Conv_output_0 = 5;
const int LAYER__model_1_branch1_branch1_2_Conv = 5;
const int BLOB__model_1_branch1_branch1_2_Conv_output_0 = 6;
const int LAYER__model_1_Add = 6;
const int BLOB__model_1_Add_output_0 = 7;
const int LAYER__model_1_relu_Relu = 7;
const int BLOB__model_1_relu_Relu_output_0 = 8;
const int LAYER_splitncnn_1 = 8;
const int BLOB__model_1_relu_Relu_output_0_splitncnn_0 = 9;
const int BLOB__model_1_relu_Relu_output_0_splitncnn_1 = 10;
const int LAYER__model_2_branch1_branch1_0_Conv = 9;
const int BLOB__model_2_branch1_branch1_0_Conv_output_0 = 11;
const int LAYER__model_2_branch1_branch1_2_Conv = 10;
const int BLOB__model_2_branch1_branch1_2_Conv_output_0 = 12;
const int LAYER__model_2_shortcut_shortcut_0_MaxPool = 11;
const int BLOB__model_2_shortcut_shortcut_0_MaxPool_output_0 = 13;
const int LAYER__model_2_shortcut_shortcut_1_Conv = 12;
const int BLOB__model_2_shortcut_shortcut_1_Conv_output_0 = 14;
const int LAYER__model_2_Add = 13;
const int BLOB__model_2_Add_output_0 = 15;
const int LAYER__model_2_relu_Relu = 14;
const int BLOB__model_2_relu_Relu_output_0 = 16;
const int LAYER_splitncnn_2 = 15;
const int BLOB__model_2_relu_Relu_output_0_splitncnn_0 = 17;
const int BLOB__model_2_relu_Relu_output_0_splitncnn_1 = 18;
const int LAYER__model_3_branch1_branch1_0_Conv = 16;
const int BLOB__model_3_branch1_branch1_0_Conv_output_0 = 19;
const int LAYER__model_3_branch1_branch1_2_Conv = 17;
const int BLOB__model_3_branch1_branch1_2_Conv_output_0 = 20;
const int LAYER__model_3_Add = 18;
const int BLOB__model_3_Add_output_0 = 21;
const int LAYER__model_3_relu_Relu = 19;
const int BLOB__model_3_relu_Relu_output_0 = 22;
const int LAYER_splitncnn_3 = 20;
const int BLOB__model_3_relu_Relu_output_0_splitncnn_0 = 23;
const int BLOB__model_3_relu_Relu_output_0_splitncnn_1 = 24;
const int LAYER__model_4_branch1_branch1_0_Conv = 21;
const int BLOB__model_4_branch1_branch1_0_Conv_output_0 = 25;
const int LAYER__model_4_branch1_branch1_2_Conv = 22;
const int BLOB__model_4_branch1_branch1_2_Conv_output_0 = 26;
const int LAYER__model_4_branch1_branch1_4_Relu = 23;
const int BLOB__model_4_branch1_branch1_4_Relu_output_0 = 27;
const int LAYER__model_4_branch1_branch1_5_Conv = 24;
const int BLOB__model_4_branch1_branch1_5_Conv_output_0 = 28;
const int LAYER__model_4_branch1_branch1_7_Conv = 25;
const int BLOB__model_4_branch1_branch1_7_Conv_output_0 = 29;
const int LAYER__model_4_shortcut_shortcut_0_MaxPool = 26;
const int BLOB__model_4_shortcut_shortcut_0_MaxPool_output_0 = 30;
const int LAYER__model_4_shortcut_shortcut_1_Conv = 27;
const int BLOB__model_4_shortcut_shortcut_1_Conv_output_0 = 31;
const int LAYER__model_4_Add = 28;
const int BLOB__model_4_Add_output_0 = 32;
const int LAYER__model_4_relu_Relu = 29;
const int BLOB__model_4_relu_Relu_output_0 = 33;
const int LAYER_splitncnn_4 = 30;
const int BLOB__model_4_relu_Relu_output_0_splitncnn_0 = 34;
const int BLOB__model_4_relu_Relu_output_0_splitncnn_1 = 35;
const int LAYER__model_5_branch1_branch1_0_Conv = 31;
const int BLOB__model_5_branch1_branch1_0_Conv_output_0 = 36;
const int LAYER__model_5_branch1_branch1_2_Conv = 32;
const int BLOB__model_5_branch1_branch1_2_Conv_output_0 = 37;
const int LAYER__model_5_branch1_branch1_4_Relu = 33;
const int BLOB__model_5_branch1_branch1_4_Relu_output_0 = 38;
const int LAYER__model_5_branch1_branch1_5_Conv = 34;
const int BLOB__model_5_branch1_branch1_5_Conv_output_0 = 39;
const int LAYER__model_5_branch1_branch1_7_Conv = 35;
const int BLOB__model_5_branch1_branch1_7_Conv_output_0 = 40;
const int LAYER__model_5_Add = 36;
const int BLOB__model_5_Add_output_0 = 41;
const int LAYER__model_5_relu_Relu = 37;
const int BLOB__model_5_relu_Relu_output_0 = 42;
const int LAYER_splitncnn_5 = 38;
const int BLOB__model_5_relu_Relu_output_0_splitncnn_0 = 43;
const int BLOB__model_5_relu_Relu_output_0_splitncnn_1 = 44;
const int BLOB__model_5_relu_Relu_output_0_splitncnn_2 = 45;
const int LAYER__model_6_branch1_branch1_0_Conv = 39;
const int BLOB__model_6_branch1_branch1_0_Conv_output_0 = 46;
const int LAYER__model_6_branch1_branch1_2_Conv = 40;
const int BLOB__model_6_branch1_branch1_2_Conv_output_0 = 47;
const int LAYER__model_6_branch1_branch1_4_Relu = 41;
const int BLOB__model_6_branch1_branch1_4_Relu_output_0 = 48;
const int LAYER__model_6_branch1_branch1_5_Conv = 42;
const int BLOB__model_6_branch1_branch1_5_Conv_output_0 = 49;
const int LAYER__model_6_branch1_branch1_7_Conv = 43;
const int BLOB__model_6_branch1_branch1_7_Conv_output_0 = 50;
const int LAYER__model_6_shortcut_shortcut_0_MaxPool = 44;
const int BLOB__model_6_shortcut_shortcut_0_MaxPool_output_0 = 51;
const int LAYER__model_6_shortcut_shortcut_1_Conv = 45;
const int BLOB__model_6_shortcut_shortcut_1_Conv_output_0 = 52;
const int LAYER__model_6_Add = 46;
const int BLOB__model_6_Add_output_0 = 53;
const int LAYER__model_6_relu_Relu = 47;
const int BLOB__model_6_relu_Relu_output_0 = 54;
const int LAYER_splitncnn_6 = 48;
const int BLOB__model_6_relu_Relu_output_0_splitncnn_0 = 55;
const int BLOB__model_6_relu_Relu_output_0_splitncnn_1 = 56;
const int LAYER__model_7_branch1_branch1_0_Conv = 49;
const int BLOB__model_7_branch1_branch1_0_Conv_output_0 = 57;
const int LAYER__model_7_branch1_branch1_2_Conv = 50;
const int BLOB__model_7_branch1_branch1_2_Conv_output_0 = 58;
const int LAYER__model_7_branch1_branch1_4_Relu = 51;
const int BLOB__model_7_branch1_branch1_4_Relu_output_0 = 59;
const int LAYER__model_7_branch1_branch1_5_Conv = 52;
const int BLOB__model_7_branch1_branch1_5_Conv_output_0 = 60;
const int LAYER__model_7_branch1_branch1_7_Conv = 53;
const int BLOB__model_7_branch1_branch1_7_Conv_output_0 = 61;
const int LAYER__model_7_Add = 54;
const int BLOB__model_7_Add_output_0 = 62;
const int LAYER__model_7_relu_Relu = 55;
const int BLOB__model_7_relu_Relu_output_0 = 63;
const int LAYER__model_8_cv1_conv_Conv = 56;
const int BLOB__model_8_cv1_conv_Conv_output_0 = 64;
const int LAYER__model_8_cv1_act_Mul = 57;
const int BLOB__model_8_cv1_act_Mul_output_0 = 65;
const int LAYER_splitncnn_7 = 58;
const int BLOB__model_8_cv1_act_Mul_output_0_splitncnn_0 = 66;
const int BLOB__model_8_cv1_act_Mul_output_0_splitncnn_1 = 67;
const int LAYER__model_8_m_MaxPool = 59;
const int BLOB__model_8_m_MaxPool_output_0 = 68;
const int LAYER_splitncnn_8 = 60;
const int BLOB__model_8_m_MaxPool_output_0_splitncnn_0 = 69;
const int BLOB__model_8_m_MaxPool_output_0_splitncnn_1 = 70;
const int LAYER__model_8_m_1_MaxPool = 61;
const int BLOB__model_8_m_1_MaxPool_output_0 = 71;
const int LAYER_splitncnn_9 = 62;
const int BLOB__model_8_m_1_MaxPool_output_0_splitncnn_0 = 72;
const int BLOB__model_8_m_1_MaxPool_output_0_splitncnn_1 = 73;
const int LAYER__model_8_m_2_MaxPool = 63;
const int BLOB__model_8_m_2_MaxPool_output_0 = 74;
const int LAYER__model_8_Concat = 64;
const int BLOB__model_8_Concat_output_0 = 75;
const int LAYER__model_8_cv2_conv_Conv = 65;
const int BLOB__model_8_cv2_conv_Conv_output_0 = 76;
const int LAYER__model_8_cv2_act_Mul = 66;
const int BLOB__model_8_cv2_act_Mul_output_0 = 77;
const int LAYER_splitncnn_10 = 67;
const int BLOB__model_8_cv2_act_Mul_output_0_splitncnn_0 = 78;
const int BLOB__model_8_cv2_act_Mul_output_0_splitncnn_1 = 79;
const int LAYER__model_9_conv_Conv = 68;
const int BLOB__model_9_conv_Conv_output_0 = 80;
const int LAYER__model_9_act_Relu = 69;
const int BLOB__model_9_act_Relu_output_0 = 81;
const int LAYER__model_10_Resize = 70;
const int BLOB__model_10_Resize_output_0 = 82;
const int LAYER__model_11_Concat = 71;
const int BLOB__model_11_Concat_output_0 = 83;
const int LAYER__model_12_conv_Conv = 72;
const int BLOB__model_12_conv_Conv_output_0 = 84;
const int LAYER__model_12_act_Relu = 73;
const int BLOB__model_12_act_Relu_output_0 = 85;
const int LAYER__model_13_m_0_Conv = 74;
const int BLOB_stride_8 = 86;
const int LAYER__model_13_m_1_Conv = 75;
const int BLOB_stride_16 = 87;
} // namespace size_128_v0_1_param_id
#endif // NCNN_INCLUDE_GUARD_size_128_v0_1_id_h
