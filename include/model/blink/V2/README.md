cd /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/blink/V2
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn  size_128_v0.1.onnx size_128_v0.1.param size_128_v0.1.bin
/media/shining/work/work/code/ncnn-20230223/build/tools/onnx/onnx2ncnn  size_128_v0.2.onnx size_128_v0.2.param size_128_v0.2.bin

/media/shining/work/work/code/ncnn-20230223/build/tools/ncnnoptimize /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/V2/face_landmark_5p_attr_blur_mask.param /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/V2/face_landmark_5p_attr_blur_mask.bin /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/V2/face_landmark_5p_attr_blur_mask_opt.param /home/<USER>/work/bst/face_base/unlock/bst_face_unlock/include/model/landmarker/V2/face_landmark_5p_attr_blur_mask_opt.bin 65536

export PATH="/media/shining/work/work/code/ncnn-20230223/build/tools":${PATH}

ncnn2mem size_128_v0.1.param size_128_v0.1.bin size_128_v0.1.id.h size_128_v0.1.mem.h
ncnn2mem size_128_v0.2.param size_128_v0.2.bin size_128_v0.2.id.h size_128_v0.2.mem.h
ncnn2mem size_128_v0.1_opt.param size_128_v0.1_opt.bin size_128_v0.1.id.h size_128_v0.1.mem.h