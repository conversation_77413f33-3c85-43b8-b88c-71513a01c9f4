#!/usr/bin/env bash

NDK=/home/<USER>/Android/Sdk/ndk/25.1.8937393
export PATH=/Users/<USER>/Tools/teegris_sdk/teegris/source/bf_sdk/common/toolchains/aarch64-secureos-gnueabi-gcc_6_3-linux-x86/bin/:$PATH

echo "build android arm64-v8a ..."
#rm -rf build-android-arm64-v8a/*
mkdir -p build-android-arm64-v8a
cd build-android-arm64-v8a
cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSECUREOS=OFF -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-23 ..
#cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSECUREOS=OFF -DSIMSECUREOS=ON -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-23 ..
$NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/llvm-strip ./src/libbstFaceUnlock.so

make -j8
adb root
sleep 2
adb remount
sleep 2
adb shell setenforce 0

adb shell setprop debug.bstfu.dumpyuv 1
adb shell setprop debug.bstfu.log.enable 1
adb shell mkdir -p /data/vendor/bst_face_unlock/images
adb shell chmod 777 -R /data/vendor/bst_face_unlock/images
# O22 charge

adb -s R9YTB0N7Z6J push ./src/libbstFaceUnlock.so /data/app/~~GVH2ktDInj0L5qHIjpJzJg==/com.bst.facerecognize-dX7o3ElpLp-Gyn5ROGBBjw==/lib/arm64

adb -s R9YTB0N7Z6J push ./src/libbstMatch.so /data/app/~~GVH2ktDInj0L5qHIjpJzJg==/com.bst.facerecognize-dX7o3ElpLp-Gyn5ROGBBjw==/lib/arm64

adb -s R9YTB0N7Z6J push ../src/doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


adb -s 9XDYPJYXFMHYEUDQ push ./src/libbstFaceUnlock.so /data/app/~~JxKIixI_nLRgXeD5uBfNrA==/com.bst.facerecognize-aojMRoMOJN0ZfIf4CGTw1w==/lib/arm64

adb -s 9XDYPJYXFMHYEUDQ push ./src/libbstMatch.so /data/app/~~JxKIixI_nLRgXeD5uBfNrA==/com.bst.facerecognize-aojMRoMOJN0ZfIf4CGTw1w==/lib/arm64

adb -s 9XDYPJYXFMHYEUDQ push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


# O22 charge

adb -s 57D1C43B50EE402C push ./src/libbstFaceUnlock.so /data/app/~~KalbeevVwlSxQ2UADtg4WQ==/com.bst.facerecognize-xRFBNlWgCzcY7uKnFBJxVQ==/lib/arm64

adb -s 57D1C43B50EE402C push ./src/libbstMatch.so /data/app/~~KalbeevVwlSxQ2UADtg4WQ==/com.bst.facerecognize-xRFBNlWgCzcY7uKnFBJxVQ==/lib/arm64

adb -s 57D1C43B50EE402C push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg



adb -s NMQR150241 push ./src/libbstFaceUnlock.so data/app/~~1EbpbL3BPq91uq5353fMrg==/com.bst.facerecognize-hCiRH882aimedX3G8Mn3OQ==/lib/arm64

adb -s NMQR150241 push ./src/libbstMatch.so data/app/~~1EbpbL3BPq91uq5353fMrg==/com.bst.facerecognize-hCiRH882aimedX3G8Mn3OQ==/lib/arm64

adb -s NMQR150241 push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


adb -s NB4DXGW4YDIFWSZD push ./src/libbstFaceUnlock.so /system/lib64
adb -s NB4DXGW4YDIFWSZD push ./src/libbstFaceUnlock.so /system/priv-app/ApeFaceUnlock/lib/arm64/
adb -s NB4DXGW4YDIFWSZD push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/priv-app/ApeFaceUnlock/lib/arm64
adb -s NB4DXGW4YDIFWSZD push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/lib64/
adb -s NB4DXGW4YDIFWSZD push ../doc/faceunlock_config_U572AA.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s NB4DXGW4YDIFWSZD shell killall -9 com.ape.faceunlock




adb -s KBP7MNFIMBFIMVSK push ./src/libbstFaceUnlock.so /system/lib64
adb -s KBP7MNFIMBFIMVSK push ../doc/faceunlock_config.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s KBP7MNFIMBFIMVSK shell killall -9 com.ape.faceunlock
adb -s 5HSCKRQCWSCQMFU4 push ./src/libbstFaceUnlock.so /data/app/~~_orTqG1SCR3tMOx3DJlTyw==/com.bst.facerecognize-EKvT3JWft8P9iBX80wjwug==/lib/arm64
adb -s 5HSCKRQCWSCQMFU4 push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


adb -s 7D85FMZXFEMBHUDQ push ./src/libbstFaceUnlock.so /vendor/lib64
adb -s 7D85FMZXFEMBHUDQ push ../doc/faceunlock_config.json /vendor/etc/bst_face_unlock/bstFaceUnlock.cfg
adb -s 7D85FMZXFEMBHUDQ shell killall -9 vendor.bst.hardware.biometrics.face.jdm-service


adb -s DMUKTKAQU8PJQOYT push ./src/libbstFaceUnlock.so /vendor/lib64
adb -s DMUKTKAQU8PJQOYT push ../doc/faceunlock_config.json /vendor/etc/bst_face_unlock/bstFaceUnlock.cfg
adb -s DMUKTKAQU8PJQOYT shell killall -9 vendor.bst.hardware.biometrics.face.jdm-service


adb -s CE79319DA5BB177E push ./src/libbstFaceUnlock.so /data/app/~~L49tn-YVBt36_L8TMkUa6Q==/com.bst.facerecognize-RdR-z3MKvhl1tP89OzsEXQ==/lib/arm64
adb -s CE79319DA5BB177E push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg

adb -s CE79319DA815177E push ./src/libbstFaceUnlock.so /data/app/~~1YbkWb4vqAu-1vuYoCPtig==/com.bst.facerecognize-gAf75U8AyleTPdMNbR1Mdg==/lib/arm64
adb -s CE79319DA815177E push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg

# 655
adb -s MJBU8LIN9PEUFEAM push ./src/libbstFaceUnlock.so /system/lib64
adb -s MJBU8LIN9PEUFEAM push ../doc/faceunlock_config_U655AA.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s MJBU8LIN9PEUFEAM shell killall -9 com.ape.faceunlock

adb -s 4X55HA8L99SOXKUG push ./src/libbstFaceUnlock.so /system/lib64
adb -s 4X55HA8L99SOXKUG push ../doc/faceunlock_config_U655AA.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s 4X55HA8L99SOXKUG shell killall -9 com.ape.faceunlock


#572

adb -s KBP7MNFIMBFIMVSK push ./src/libbstFaceUnlock.so /system/lib64
adb -s KBP7MNFIMBFIMVSK push ./src/libbstFaceUnlock.so /system/priv-app/ApeFaceUnlock/lib/arm64/
adb -s KBP7MNFIMBFIMVSK push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/priv-app/ApeFaceUnlock/lib/arm64
adb -s KBP7MNFIMBFIMVSK push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/lib64/
adb -s KBP7MNFIMBFIMVSK push ../doc/faceunlock_config_U655AA.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s KBP7MNFIMBFIMVSK shell killall -9 com.ape.faceunlock

#P811

adb -s ce152f07 push ./src/libbstFaceUnlock.so /system/lib64
adb -s ce152f07 push ./src/libbstFaceUnlock.so /system/priv-app/ApeFaceUnlock/lib/arm64/
adb -s ce152f07 push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/priv-app/ApeFaceUnlock/lib/arm64
adb -s ce152f07 push ./src/libbstFaceUnlock.so /mnt/scratch/overlay/system/upper/lib64/

adb -s ce152f07 push ../doc/faceunlock_config_P811.json /data/user_de/0/com.ape.faceunlock/files/faceunlock.cfg
adb -s ce152f07 shell killall -9 com.ape.faceunlock



adb -s 5HSCKRQCWSCQMFU4 push ./src/libbstFaceUnlock.so /data/app/~~_orTqG1SCR3tMOx3DJlTyw==/com.bst.facerecognize-EKvT3JWft8P9iBX80wjwug==/lib/arm64
adb -s 5HSCKRQCWSCQMFU4 push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


adb -s 7D85FMZXFEMBHUDQ push ./src/libbstFaceUnlock.so /vendor/lib64
adb -s 7D85FMZXFEMBHUDQ push ../doc/faceunlock_config.json /vendor/etc/bst_face_unlock/bstFaceUnlock.cfg
adb -s 7D85FMZXFEMBHUDQ shell killall -9 vendor.bst.hardware.biometrics.face.jdm-service

# W3 APK
adb -s CE79C5CA09840F7E push ./src/libbstFaceUnlock.so /data/app/~~__v2TCi2ZTURnvnzu4INRg==/com.bst.facerecognize-OlMrHcQwCkaP89GOsCK0jA==/lib/arm64
adb -s CE79C5CA09840F7E push ../doc/faceunlock_config_W3.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg

adb -s CE83885D610B2C7E push ./src/libbstFaceUnlock.so /data/app/~~5_jTFHJH4tlJaMtVUQCdCA==/com.bst.facerecognize-g4KT-3ZXpxAHPxgoG9JZYw==/lib/arm64
adb -s CE83885D610B2C7E push ../doc/faceunlock_config_W3.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg



adb -s CE79319DA815177E push ./src/libbstFaceUnlock.so /data/app/~~1SSGg9OfTuVyXvo7rv6zqw==/com.bst.facerecognize-Oz7QnuyC5tAMbunX9xFVmw==/lib/arm64
adb -s CE79319DA815177E push ../doc/faceunlock_config_W3.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg

adb -s CE8245811987257E push ./src/libbstFaceUnlock.so /data/app/~~8Nl5W9RYvFcwrsfFKmQSWg==/com.bst.facerecognize-CcRiiZrq8iCYYPyU6InOWQ==/lib/arm64
adb -s CE8245811987257E push ../doc/faceunlock_config_W3.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg


#339D apk
adb -s LBQGS875FAIZY5VC push ./src/libbstFaceUnlock.so /data/app/~~NluZdsTERo7HpawxCa1cug==/com.bst.facerecognize-GYGELk892Qb1jvoQv3psFQ==/lib/arm64
adb -s LBQGS875FAIZY5VC push ../doc/faceunlock_config.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg

adb -s J7HUYXVCQ4P7LFEM push ./src/libbstFaceUnlock.so /vendor/lib64/libbstFaceUnlock.so
adb -s J7HUYXVCQ4P7LFEM push ../doc/faceunlock_config_W3.json /vendor/etc/bst_face_unlock/bstFaceUnlock.cfg
adb -s J7HUYXVCQ4P7LFEM shell killall -9 vendor.bst.hardware.biometrics.face.jdm-service


#197 3#
adb -s 0123456789ABCDEF push ./src/libbstFaceUnlock.so /data/app/~~TRg5ddlJ6Djbxua6riN9lw==/com.bst.facerecognize-k2PZn392izX9vJuqYw6gVA==/lib/arm64
adb -s 0123456789ABCDEF push ../doc/faceunlock_config_M197.json /data/data/com.bst.facerecognize/bstFaceUnlock.cfg
