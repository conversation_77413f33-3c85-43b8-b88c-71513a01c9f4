#include "BSTFaceUnlock.h"

#include <cstdio>
#include <string.h>
#include <stdio.h>

#include "CommonDef.h"
#include "LibDef.h"
#include "Version.h"
#include "BSTFaceUnlockConfig.h"
#include "model/ModuleDetect.h"
#include "model/ModuleScreenPaperDetect.h"
#include "model/ModuleLandmarker.h"
#include "model/ModuleQualityAssessor.h"
#include "model/ModuleAlign.h"
#include "model/ModuleLiving.h"
#include "model/ModuleBlinkDetector.h"
#include "model/ModuleRecognizer.h"
#include "model/ModuleMotion.h"
#include "model/ModuleNoseDetector.h"
#include <sys/time.h>
#ifndef SECUREOS
#include <sys/stat.h>
#include "mutex"
#define DEBUG_MATCH
#endif

static BSTFaceUnlockVersion g_version;
static char                 g_build_date[256];
static char                 g_lib_version[256];
static char                 g_cfg_version[256] = "built-in";
static FaceUnlockConfig     g_config;
#ifndef SECUREOS
static std::mutex           g_api_mutex;
#endif
static int  g_enroll_frame_num;
static int  g_auth_frame_num;
static long g_auth_start;
static int  g_suspected_attack_cnt;
static int  g_suspected_attack_frame_cnt;
static int  g_continue_real_cnt;
static int  g_continue_multi_person_cnt;
static int  g_continue_livingD_cnt;
static int  g_continue_livingC_cnt;
static int  g_processing_max_continue_real;
static float g_last_feature_database[FEATURE_DIM_SDK];
static int  g_first_in;
static float  g_enroll_quality;
static unsigned char  *g_enroll_align_face = NULL;///
static Bbox g_last_cache_bbox;
#ifdef DEBUG_MATCH
static float g_feature_database[FEATURE_DIM_SDK];
#endif

struct DumpInfo {
    float face_brightness;
    float bg_brightness;
    float model_blur_score;
    float quality_blur_score;
    float mask_score;
    int  blink_score;
    int  nose_status;
    float liveC_score;
    float liveD_score;
    float match_score;

    int rotate_order;
    float yaw;
    float pitch;
    float roll;
    float quality_score;

    int facebox_x1;
    int facebox_y1;
    int facebox_x2;
    int facebox_y2;
    int livingM_flag;

    int iso;
    int ret_val;
} g_dump_info;

#ifndef SECUREOS
struct AutoLock {
    std::mutex* m_mutex;
    AutoLock(std::mutex* g_api_mutex) {
        m_mutex = g_api_mutex;
        m_mutex->lock();
    }
    ~AutoLock() {
        m_mutex->unlock();
    }
};
#endif

static bool                   g_init_flag        = false;
static ModuleDetect*          g_detect           = nullptr;
static ModuleScreenPaperDetect*  g_screen_paper_detect  = nullptr;
static ModuleLandmarker*      g_landmarker       = nullptr;
static ModuleQualityAssessor* g_quality_assessor = nullptr;
static ModuleLiving*          g_dark_live             = nullptr;
static ModuleLiving*          g_normal_live             = nullptr;
//static ModuleLiving*          g_big_angel_live             = nullptr;
static ModuleMotion*          g_motion_detector            = nullptr;
static ModuleBlink*           g_blink            = nullptr;
static ModuleNose*            g_nose            = nullptr;
static ModuleRecognizer*      g_recognizer       = nullptr;

#ifndef SECUREOS
    static char                    g_dump_folder[1000];
    static char                    g_meta_filename[1000];
    static char                    g_dump_img_filename[1000];
    static char                    g_dump_img_path[1000];
    static char                    g_meta_data[2000];
#ifdef DEBUG_MATCH
    float InnerProduct(const float* const feat1, const float* const feat2, int dim) {
        float sim = 0;
        for (int i = 0; i < dim; ++i) {
            sim += feat1[i] * feat2[i];
        }
        return sim;
    }
#endif
    void SaveNV21(const BSTFaceUnlockFrameData* const bst_face_input, const char* const filename) {
        if (bst_face_input->format != BST_IMAGE_TYPE_NV21) {
            LOGI("wrong data format %d", bst_face_input->format);
            return;
        }

        FILE* dump_image = fopen(filename, "wb");
        if (dump_image == NULL) {
            LOGI("write image error: %s", filename);

            return;
        }
        fwrite(bst_face_input->data, 1, 1.5 * bst_face_input->width * bst_face_input->height, dump_image);
        fclose(dump_image);
    }
    void FaceUnlockDump(bool is_enroll, int frame_number, const BSTFaceUnlockFrameData* const bst_face_input /*, BSTFaceFeature* bst_face_output*/) {
        int dump_enable = 0;
#ifdef __ANDROID__
        char dumpyuv[PROP_VALUE_MAX];
        __system_property_get("debug.bstfu.dumpyuv", dumpyuv);
        printf("debug.bstfu.dumpyuv : 0x%x \n", dumpyuv[0]);
        dump_enable = dumpyuv[0] - 0x30;
#endif
        // LOGI("g_config.common_config.dump_enable: %d dump_enable: %d", g_config.common_config.dump_enable, dump_enable)
        if (dump_enable == 1 || g_config.common_config.dump_enable) {
            char ext[5];
            sprintf(ext, "%s", "nv21");
            if (frame_number == 1) {
                // create folder
                struct timeval dumptime;
                gettimeofday(&dumptime, NULL);
                long long time_usec =  dumptime.tv_usec;

                struct tm *timeinfo = localtime(&dumptime.tv_sec);

                char time_buffer[80];
                strftime(time_buffer, sizeof(time_buffer), "%Y%m%d_%H_%M_%S", timeinfo);

                if (is_enroll) {
                    sprintf(g_dump_folder, "%s/bst_enroll_[brb]%.1f_[brd]%.1f_[bl]%.2f_%.2f_[ey]%.2f_[lv]%.4f_%.4f_[match]%.2f_%s_%lld",
                            g_config.common_config.default_dump_path.c_str(),
                            g_config.enroll_config.bright_thres, g_config.enroll_config.face_dark_level_1_thres,
                            g_config.enroll_config.model_blur_thres, g_config.enroll_config.quality_blur_thres, g_config.enroll_config.blink_thres, g_config.enroll_config.live_dark_thres,g_config.enroll_config.live_normal_thres, g_config.enroll_config.mask_thres, time_buffer,time_usec);
                } else {
                    sprintf(g_dump_folder, "%s/bst_auth_[brb]%.1f_[brd]%.1f_[bl]%.2f_%.2f_[ey]%.2f_[lv]%.4f_%.4f_[match]%.2f_%s_%lld",
                            g_config.common_config.default_dump_path.c_str(),
                            g_config.authen_config.bright_thres, g_config.authen_config.face_dark_level_1_thres,
                            g_config.authen_config.model_blur_thres, g_config.authen_config.quality_blur_thres, g_config.authen_config.blink_thres, g_config.authen_config.live_dark_thres, g_config.authen_config.live_normal_thres, g_config.authen_config.mask_thres, time_buffer,time_usec);
                }

                if (-1 == mkdir(g_dump_folder, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH)) {
                    LOGE("Create folder error, %s\n", g_dump_folder);
                } else {
                    LOGE("Create folder success, %s\n", g_dump_folder);
                }
                sprintf(g_meta_filename, "%s/metaData.csv", g_dump_folder);
                FILE* metadata_file = fopen(g_meta_filename, "w");
                if (metadata_file != NULL) {
                    fprintf(metadata_file, "%s%s%s%s%s%s%s%s",
                            "folder,filename,img_path,",
                            "rotate,faceBbox_x1,faceBbox_y1,faceBbox_x2,faceBbox_y2,",
                            "brightness,",
                            "model_blur_score,",
                            "quality_blur_score,",
                            "blink_score,",
                            "liveC_score,",
                            "match_score\n");
                    fclose(metadata_file);
                }
            }

            sprintf(g_dump_img_filename, "bst_%d_%d_%d_[f]%d_%d_%d_%d[br]%.1f[bl]%.1f_%.4f[ey]%d[qu]%.3f[lv]%.3f_%.4f[match]%.2f[iso]%d_0x%x.%s",
                    frame_number, bst_face_input->width, bst_face_input->height,g_dump_info.facebox_x1, g_dump_info.facebox_y1, g_dump_info.facebox_x2, g_dump_info.facebox_y2,
                    g_dump_info.face_brightness, g_dump_info.model_blur_score, g_dump_info.quality_blur_score, g_dump_info.blink_score, g_dump_info.quality_score,
                    g_dump_info.liveD_score,g_dump_info.liveC_score, g_dump_info.match_score, g_dump_info.iso, g_dump_info.ret_val, ext);

            sprintf(g_meta_data, "%s,%s,%s,%d,%d,%d,%d,%d,%.4f,%.4f,%.4f,%d,%.4f,%.4f,",
                    g_dump_folder, g_dump_img_filename, "N/A",
                    bst_face_input->rotation, g_dump_info.facebox_x1, g_dump_info.facebox_y1, g_dump_info.facebox_x2, g_dump_info.facebox_y2,
                    g_dump_info.face_brightness,
                    g_dump_info.model_blur_score,
                    g_dump_info.quality_blur_score,
                    g_dump_info.blink_score,
                    g_dump_info.liveC_score,
                    g_dump_info.match_score); // 将格式化文本发送到字符串缓冲区

            LOGI("dump file : %s", g_dump_img_filename)
            LOGI("meta_data : %s", g_meta_data)

            FILE* metadata_file = fopen(g_meta_filename, "a");
            if (metadata_file != NULL) {
                fprintf(metadata_file, "%s\n", g_meta_data);
                fclose(metadata_file);
            }
            sprintf(g_dump_img_path, "%s/%s", g_dump_folder, g_dump_img_filename);
            SaveNV21(bst_face_input, g_dump_img_path);
        }
    }

#endif
/**
 *
 * @param lmk_info
 * @param judge_type:
 *          0 is only eyes
 *          2 is nose
 *          1 is full
 * @return
 */
int judge_occlusion(LmkInfo& lmk_info, int judge_type = 1) {
    // 遮挡逻辑
    int not_vis_pts_num = 0;
    if (judge_type == 1) {
        for (int i = 0; i < lmk_info.pts.size(); ++i) {
            float vis_score = lmk_info.vis_probs[i];
            if (vis_score < 0.4) {
                not_vis_pts_num += 1;
            }
            if (not_vis_pts_num >= lmk_info.pts.size()) {
                return 1; // 遮挡
            } else {
                return 0; // 未遮挡
            }
        }
    }else if (judge_type == 2) {
        float vis_score = lmk_info.vis_probs[2];
        if (vis_score < 0.3) {
            not_vis_pts_num += 1;
        }
        if (not_vis_pts_num >= 1) {
            return 1;  // 遮挡
        } else {
            return 0;
        }

    }
    return 0;
}




int MultiFrameSpoofCheck(int ret_val, FilterConfig config) {

    // if (ret_val == BST_FU_C_IGNORE) {
    //     return BST_FU_CONTINUE;
    //     }
    if (BST_FU_C_SUSPECTED == ret_val) {
        if (g_suspected_attack_cnt >= config.max_suspected_live_attack) {
            g_suspected_attack_cnt++;
            g_suspected_attack_frame_cnt++;
            return BST_FU_F_ATTACK;
        } else {
            g_suspected_attack_cnt++;
        }
    }
    if (g_suspected_attack_cnt != 0) {
        g_suspected_attack_frame_cnt++;
        if (g_suspected_attack_frame_cnt >= config.max_suspected_live_attack_range) {
            g_suspected_attack_frame_cnt = 0;
            g_suspected_attack_cnt       = 0;
        }
    }

    return ret_val;
}





/***
 *
 * @param bst_face_input
 * @param bst_face_output
 * @param filter_config
 * @param inner_type
 *  1 is enroll
 *  2 is auth
 * @return
 */
int InnerAlgorithm(const BSTFaceUnlockFrameData* const bst_face_input,const int iso, BSTFaceFeature* const bst_face_output, FilterConfig filter_config, int inner_type) {
    TIMER_START(InnerAlgoTotal)
    TIMER_START(PreProcess)
    if (inner_type == 1) {
        LOGI("InnerEnroll");
    } else if (inner_type == 2) {
        LOGI("InnerAuth");
    }
    int            ret = BST_FU_E_INNER_ERROR;
    unsigned char* input_rotate = NULL;
    unsigned char* input_rgb = NULL;
    unsigned char* tmp_src_img = NULL;
    unsigned char* top_src_img = NULL;
    unsigned char* input_gray = NULL;

    int            rotate_width;
    int            rotate_height;
    int            top_order = 0;
    ncnn::Mat      align_face(NORM_W, NORM_H, NORM_C, 1);
    memset(&g_dump_info, 0, sizeof(DumpInfo));
    g_dump_info.iso = iso;
    do {
/***********************************************************************************************************************************************************************
 *  1.       . frame data transformation
 */
        int input_yuv_size;
        if (bst_face_input->format == BST_IMAGE_TYPE_NV21 || bst_face_input->format == BST_IMAGE_TYPE_NV12) {
            input_yuv_size = bst_face_input->width * bst_face_input->height * 1.5;
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        input_rotate = new unsigned char[input_yuv_size];
        if (NULL == input_rotate) {
            ret = BST_FU_E_OOM;
            break;
        }

        if (bst_face_input->rotation == 0) {
            memcpy(input_rotate, bst_face_input->data, input_yuv_size);
            rotate_width  = bst_face_input->width;
            rotate_height = bst_face_input->height;
        } else if (bst_face_input->rotation == 270) {
            bstutils::rotate_yuv_nv21_270((unsigned char*)bst_face_input->data, input_rotate, bst_face_input->width, bst_face_input->height, bst_face_input->stride);
            rotate_width  = bst_face_input->height;
            rotate_height = bst_face_input->width;
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        input_rgb           = new unsigned char[rotate_width * rotate_height * CHANNEL];
        if (NULL == input_rgb) {
            ret = BST_FU_E_OOM;
            break;
        }
        if (bst_face_input->format == BST_IMAGE_TYPE_NV21) {
//            ncnn::yuv420sp2rgb(input_rotate, rotate_width, rotate_height, input_rgb);
             bstutils::nv21_to_rgb_keepsize(input_rotate, rotate_width, rotate_height, rotate_width, rotate_width, input_rgb);
        } else if (bst_face_input->format == BST_IMAGE_TYPE_NV12) {
            ncnn::yuv420sp2rgb_nv12(input_rotate, rotate_width, rotate_height, input_rgb);
            // bstutils::nv12_to_rgb_keepsize(input_rotate, rotate_width, rotate_height, rotate_width, rotate_width, input_rgb);
        } else {
            ret = BST_FU_E_INVALID_INPUT;
            break;
        }
        TIMER_STOP(PreProcess)

#ifdef DUMP
#define DUMP
        g_blink->g_count++;
        bstutils::save_image_png("input_rgb"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif
/***********************************************************************************************************************************************************************
 *   2. 人脸 detect
 */
        TIMER_START(Detect)
        std::vector<BBox> rotate_objects;
        float               max_area                      = 0;
        float               max_idx                       = -1;
//        if (inner_type == 1) {
//            g_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects);
//        }
//        else if (inner_type == 2)

        {
            int rotate_order[4] = {0, 1, 3, 2};
            /// 4 次人脸检测
            {
                if (top_src_img == NULL){
                    top_src_img = new unsigned char[rotate_width * rotate_height * CHANNEL];
                    if (NULL == top_src_img) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }


                for (int i = 0; i < 4; ++i) {


                    int rotate_type = rotate_order[i];

                    int tmp_width  = (rotate_type == 2 || rotate_type == 0) ? rotate_width : rotate_height;
                    int tmp_height = (rotate_type == 2 || rotate_type == 0) ? rotate_height : rotate_width;
                    if (tmp_src_img == NULL) {
                        tmp_src_img = new unsigned char[tmp_width * tmp_height * CHANNEL];
                        if (NULL == tmp_src_img) {
                            ret = BST_FU_E_OOM;
                            break;
                        }
                    }

                    bstutils::rotate_c3((uint8_t*)input_rgb, tmp_src_img, rotate_width, rotate_height, rotate_type);
                    std::vector<BBox> tmp_objects;
                    if (inner_type == 1){
                        g_detect->perfom(tmp_src_img, tmp_width, tmp_height, CHANNEL, tmp_objects, true, filter_config.min_face);
                    } else{
                        g_detect->perfom(tmp_src_img, tmp_width, tmp_height, CHANNEL, tmp_objects, false, filter_config.min_face);
                    }

                    if (tmp_objects.size() > 0){
                        LOGI("rotate_type:%d, face_num:%d", rotate_type, tmp_objects.size())
                        std::vector<BBox> swap_objects = tmp_objects;
                        tmp_objects = rotate_objects;
                        rotate_objects = swap_objects;
                        if ( i != 0){
                            memcpy(input_rgb, tmp_src_img, sizeof(unsigned char) * rotate_width * rotate_height * CHANNEL);
                        }

                        rotate_width  = tmp_width;
                        rotate_height = tmp_height;
                        top_order         = rotate_type;

                        break;
                    }

                }

            }
        }
#ifdef RECORDING_MODE
        if (inner_type == 2){
            LOGI("RECORDING_MODE");
            ret = BST_FU_CONTINUE; /// BST_FU_CONTINUE 30fps  BST_FU_C_FACE_NOT_FOUND 20fps  BST_FU_SUCCESS  1fps
            break;
        }
       
#endif
        g_dump_info.rotate_order = top_order;
        if (inner_type == 1){ /// 注册
            if (rotate_objects.size() <= 0) {
                g_continue_multi_person_cnt = 0;
                ret = BST_FU_C_FACE_NOT_FOUND;
                break;
            } else if (rotate_objects.size() > 1 ) {
                int board_face_num = 0;
                for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                    if (!outside_border(rotate_objects[i], rotate_width, rotate_height, filter_config)) {
                        board_face_num++;
                    }
                }
                //        LOGE("Enroll,board_face_num: %d", (int)board_face_num);
                if (board_face_num > 1) {
                    g_continue_multi_person_cnt ++;
                }else{
                    g_continue_multi_person_cnt = 0;
                }
            } else if (rotate_objects.size() == 1) {
                for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                    // 选取面积最大人脸框
                    if (rotate_objects[i].w * rotate_objects[i].h > max_area) {
                        max_area = rotate_objects[i].w * rotate_objects[i].h;
                        max_idx  = i;
                    }
                }
                g_continue_multi_person_cnt = 0;
            }
            if (g_continue_multi_person_cnt >= filter_config.max_continue_real){
                LOGE("Enroll, Continue Multi Person, Frame Num: %d", (int)g_continue_multi_person_cnt);
                ret = BST_FU_F_ENROLL_MPERSON;
                g_continue_real_cnt = 0;
                break;
            }else if (g_continue_multi_person_cnt > 0){
                ret = BST_FU_CONTINUE;
                break;
            }else{
                int ret_judge = 0;
                if (!judge_border(rotate_objects[max_idx], rotate_width, rotate_height, filter_config, ret_judge)) {
                    ret = ret_judge;
                    g_continue_real_cnt = 0;
                    break;
                }
            }

        }else if (inner_type == 2){  ///解锁
            if (rotate_objects.size() <= 0) {
                ret = BST_FU_C_FACE_NOT_FOUND;
                break;
            }
            for (int i = 0; i < (int)rotate_objects.size(); ++i) {
                // 选取面积最大人脸框
                if (rotate_objects[i].w * rotate_objects[i].h > max_area) {
                    max_area = rotate_objects[i].w * rotate_objects[i].h;
                    max_idx  = i;
                }
            }
        }

//#define DUMP
#ifdef DUMP
        bstutils::DrawRect((uint8_t*)input_rgb, rotate_width, rotate_height, 3, rotate_objects[0].x, rotate_objects[0].y, rotate_objects[0].x + rotate_objects[0].w, rotate_objects[0].y + rotate_objects[0].h, 1, 0, 255, 0);

        bstutils::save_image_png("rectImage"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif
        g_dump_info.facebox_x1 = rotate_objects[max_idx].x;
        g_dump_info.facebox_y1 = rotate_objects[max_idx].y;
        g_dump_info.facebox_x2 = rotate_objects[max_idx].x + rotate_objects[max_idx].w;
        g_dump_info.facebox_y2 = rotate_objects[max_idx].y + rotate_objects[max_idx].h;
/***********************************************************************************************************************************************************************
 *   2.1 靠近边缘提示
 */
        //        if (!g_detect->judge_border(rotate_objects[max_idx], rotate_width, rotate_height, filter_config)) {
        //            ret = BST_FU_C_NEAR_BORDER;
        //            break;
        //        }

        //        LOGI("object max_idx : %d, %d, %d, %d, %d", max_idx, rotate_objects[max_idx].x, rotate_objects[max_idx].y, rotate_objects[max_idx].w, rotate_objects[max_idx].h);
        TIMER_STOP(Detect)
        Bbox box;
        box.x1 = rotate_objects[max_idx].x;
        box.y1 = rotate_objects[max_idx].y;
        box.x2 = rotate_objects[max_idx].w + rotate_objects[max_idx].x;
        box.y2 = rotate_objects[max_idx].h + rotate_objects[max_idx].y;




/***********************************************************************************************************************************************************************
 *  2.3       motion detect，人脸运动检测 plus for motion detection
 */
        if ( inner_type ==2 && filter_config.live_filter_enable){

            if (g_auth_frame_num % 1 == 0
                //                && filter_config.max_continue_real > 1
            ){

                TIMER_START(LivingM);
                if(input_gray == NULL){
                    input_gray = new unsigned char [rotate_width*rotate_height];
                    if (NULL == input_gray) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }
                bstutils::RGB2Gray((uint8_t*)input_rgb, rotate_width, rotate_height, input_gray);

                if (g_first_in == 0){
                    // 3 plus for motion detection
                    move_param_t m_param = {0};
                    m_param.height_divide_count = 2;
                    m_param.width_divide_count = 2;
                    std::vector<int> thresholds;
                    thresholds.clear();
                    for (int i = 0; i < m_param.height_divide_count * m_param.width_divide_count; ++i) {
                        thresholds.push_back(filter_config.motion_abs_thres);
                    }
                    m_param.thresholds = thresholds.data();
                    m_param.roi_detects_count = m_param.height_divide_count * m_param.width_divide_count;
                    m_param.motion_rel_thres = filter_config.motion_rel_thres;
                    int ret_value = g_motion_detector->motion_cache(m_param,input_gray, rotate_width,rotate_height,box);
                    g_dump_info.livingM_flag = -1;
                    if(ret_value!= 0){
                        LOGE("LivingM_cache failed");
                        g_continue_real_cnt = 0;
                        memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                        g_first_in = 0;
                        ret = BST_FU_E_INNER_ERROR;
                        TIMER_STOP(LivingM);
                        break;
                    }
                    memcpy(&g_last_cache_bbox, &box, sizeof(Bbox));
                    g_first_in ++;

                    g_continue_real_cnt = 1;
                    ret = BST_FU_CONTINUE;
                    TIMER_STOP(LivingM);
                    break;

                }
                else{

                    int livingM_flag = -1;
                    int ret_value = g_motion_detector->motion_detect(input_gray, rotate_width,rotate_height,g_last_cache_bbox,livingM_flag, g_first_in);
                    if(ret_value!= 0){
                        LOGE("LivingM_detect failed");
                        g_continue_real_cnt = 0;
                        g_first_in = 0;
                        memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                        TIMER_STOP(LivingM);
                    }else
                    {
                        g_first_in ++;
                        g_dump_info.livingM_flag = livingM_flag;

                        memcpy(&g_last_cache_bbox, &box, sizeof(Bbox));

                        move_param_t m_param = {0};

                        m_param.height_divide_count = filter_config.motion_height_divide_count;
                        m_param.width_divide_count = filter_config.motion_width_divide_count;
                        std::vector<int> thresholds;
                        thresholds.clear();
                        for (int i = 0; i < m_param.height_divide_count * m_param.width_divide_count; ++i) {
                            thresholds.push_back(filter_config.motion_abs_thres);
                        }
                        m_param.thresholds = thresholds.data();
                        m_param.roi_detects_count = m_param.height_divide_count * m_param.width_divide_count;
                        m_param.motion_rel_thres = filter_config.motion_rel_thres;
                        ret_value = g_motion_detector->motion_cache(m_param,input_gray, rotate_width,rotate_height,box);
                        if(ret_value!= 0){
                            LOGE("LivingM_cache failed");
                            g_continue_real_cnt = 0;
                            g_first_in = 0;
                            memset(&g_last_cache_bbox, 0, sizeof(Bbox));
                            ret = BST_FU_E_INNER_ERROR;
                            TIMER_STOP(LivingM);
                            break;
                        }
                        if (livingM_flag == 0){
                            g_continue_real_cnt = 0;
                            ret = BST_FU_C_SUSPECTED;
                            TIMER_STOP(LivingM);
                            break;
                        }
                    }
                }
                TIMER_STOP(LivingM);
            }

        }


/****************************************************************************************************************************************************************
* 2.2 屏幕与打印纸检测
*/
        if(filter_config.live_filter_enable){
            TIMER_START(LivingD);
#ifdef SECUREOS
            g_screen_paper_detect = new ModuleScreenPaperDetect();
            g_screen_paper_detect->init();
#endif
            g_dump_info.liveD_score = -1.f;
            std::vector<Object> screen_paper_objects;
            if (inner_type == 1){
                g_screen_paper_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, screen_paper_objects,box, true, filter_config.min_face);
            } else{
                g_screen_paper_detect->perfom(input_rgb, rotate_width, rotate_height, CHANNEL, screen_paper_objects,box, false, filter_config.min_face);
            }
            if(screen_paper_objects.size() > 0){
                if(screen_paper_objects[0].class_id == 0){
                    g_dump_info.liveD_score = screen_paper_objects[0].score;
                }else if (screen_paper_objects[0].class_id == 1){
                    g_dump_info.liveD_score = 1 + screen_paper_objects[0].score;
                }
                g_continue_real_cnt = 0;
                g_continue_livingD_cnt +=1;
                if (g_continue_livingD_cnt >= filter_config.max_livingD_suspected_live_attack){
                   
                    if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                        g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                    }else{
                        g_processing_max_continue_real = filter_config.max_continue_real;
                    }

                }

                /// cache features

                ret = BST_FU_C_SUSPECTED;
//                if (inner_type == 1){
//                    TIMER_STOP(LivingD);
//                    break;
//                }

            }else{
//                g_continue_livingD_cnt = 0;
            }
#ifdef SECUREOS
            g_screen_paper_detect->release();
            delete g_screen_paper_detect;
            g_screen_paper_detect = NULL;
#endif
            TIMER_STOP(LivingD);
        }

/***********************************************************************************************************************************************************************
 *   3. 人脸 关键点 + blur + mask  属性
 */
        TIMER_START(Landmark)
#ifdef SECUREOS
        g_landmarker = new ModuleLandmarker();
        g_landmarker->init();
#endif
        LmkInfo lmk_info;
        bool land_ret =g_landmarker->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], lmk_info);
        if (land_ret == false){
            ret = BST_FU_E_INNER_ERROR;
            TIMER_STOP(Landmark)
            break;
        }
#ifdef SECUREOS
        g_landmarker->release();
        delete g_landmarker;
        g_landmarker = NULL;
#endif
        /***********************************************************************************************************************************************************************
 *   3.1 angel，角度过滤
 */
        g_dump_info.yaw = lmk_info.poses[0];
        g_dump_info.pitch = lmk_info.poses[1];
        g_dump_info.roll = lmk_info.poses[2];
        if (filter_config.angel_filter_enable) {
            float yaw = lmk_info.poses[0];  // yaw,pitch,roll
            float pitch = lmk_info.poses[1];  // yaw,pitch,roll
            /// yaw > 负值 left_pitch_thres抬头  正值  right_yaw_thres 低头角度
           
            if (yaw < -filter_config.left_yaw_thres || yaw > filter_config.right_yaw_thres ) {
                ret = BST_FU_C_POSE_ABNORMAL;
                g_continue_real_cnt = 0;
                TIMER_STOP(Landmark)
                break;
            }
            /// pitch 负值 up_pitch_thres抬头  正值  down_pitch_thres 低头角度
            if (pitch < -filter_config.up_pitch_thres || pitch > filter_config.down_pitch_thres) {
                ret = BST_FU_C_POSE_ABNORMAL;
                g_continue_real_cnt = 0;
                TIMER_STOP(Landmark)
                break;
            }
        }



        for (int i = 0; i < LMK_NUM; i++) {
            box.ppoint[i * 2 + 0] = lmk_info.pts[i].x;
            box.ppoint[i * 2 + 1] = lmk_info.pts[i].y;
        }
//#define DUMP
#ifdef DUMP
        bstutils::DrawRect((uint8_t*)input_rgb, rotate_width, rotate_height, 3, rotate_objects[0].x, rotate_objects[0].y, rotate_objects[0].x + rotate_objects[0].w, rotate_objects[0].y + rotate_objects[0].h, 1, 0, 255, 0);

        for (int i = 0; i < lmk_info.pts.size(); ++i) {
            // if (lmk_info.vis_probs[i] > 0) {
            bstutils::DrawPoint((uint8_t*)input_rgb, rotate_width, rotate_height, 3, lmk_info.pts[i].x, lmk_info.pts[i].y, 2, 255, 0, 0);
            //     //            LOGI("DrawPoint:%f , %f",lmk_info.pts[i].x,lmk_info.pts[i].y);
            // }
        }
        bstutils::save_image_png("landmarkImage"+std::to_string(g_blink->g_count), input_rgb, rotate_width, rotate_height, 3);
#endif

        g_dump_info.mask_score =  lmk_info.mask_prob;
        TIMER_STOP(Landmark)






/***********************************************************************************************************************************************************************
 *  4.       bright，亮度估计
 */
        TIMER_START(Quality)
        QualityOutput quality_output;
        if (inner_type == 1){
            if (iso >0){
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], false,true,quality_output);
            }else
            {
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], true,true,quality_output);
            }
            g_dump_info.quality_score = g_quality_assessor->getQualityScore(quality_output,lmk_info);
        }else if (inner_type == 2){
            if (iso >0){
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], false, false,quality_output);
            }else
            {
                g_quality_assessor->check((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], true,false,quality_output);
            }
        }


        // 6.bright，亮度估计
        if (filter_config.bright_filter_enable) {

            g_dump_info.face_brightness = quality_output.face_brightness;
            g_dump_info.bg_brightness = quality_output.bg_brightness;
            // if (1 == inner_type) {
            //     if (iso >= filter_config.iso_dark_thres) {
            //         g_continue_real_cnt=0;
            //         LOGI("enroll iso check : %d", iso);
            //         ret = BST_FU_C_TOO_DARK;
            //         break;
            //     }
            // }
            if(inner_type == 1){
                if (quality_output.face_brightness <= filter_config.face_dark_level_1_thres) {
                    g_continue_real_cnt=0;
                    // LOGI("too dark");
                    ret = BST_FU_C_TOO_DARK;
                    break;
                }
            }else if(inner_type == 2){
                if(iso < filter_config.iso_dark_level_1_thres){ // 更亮的时候，人脸逐步变亮，过滤之前更暗的人脸
                    if (quality_output.face_brightness <= filter_config.face_dark_level_2_thres) {
                        g_continue_real_cnt=0;
                        // LOGI("too dark");
                        ret = BST_FU_C_TOO_DARK;
                        break;
                    }
                }else{
                    if (quality_output.face_brightness <= filter_config.face_dark_level_1_thres) {
                        g_continue_real_cnt=0;
                        // LOGI("too dark");
                        ret = BST_FU_C_TOO_DARK;
                        break;
                    }
                }
            }
            if (quality_output.face_brightness >= filter_config.bright_thres) {
                g_continue_real_cnt=0;
                // LOGI("too bright");
                ret = BST_FU_C_TOO_BRIGHT;
                break;
            }
        }

/***********************************************************************************************************************************************************************
 *  5.       模糊估计
 */


        // 7.blur，模糊估计
        if (filter_config.blur_filter_enable) {
            // 量产
            g_dump_info.model_blur_score = lmk_info.blur_prob;
            g_dump_info.quality_blur_score = quality_output.blurriness;
            if (quality_output.blurriness > filter_config.quality_blur_thres) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_TOO_BLUR;
                    break;
                }
                // }
            if(iso <= 4800){
                if (lmk_info.blur_prob > filter_config.model_blur_thres) {
                    //            LOGI("too blur,model_blur_thres:%f blur_prob:%f",g_config.authen_config.model_blur_thres,lmk_info.blur_prob);
                    g_continue_real_cnt = 0;
                    ret= BST_FU_C_TOO_BLUR;
                    break;
                }
             }
        }
        TIMER_STOP(Quality)


        /***********************************************************************************************************************************************************************
 *  6.       face align，人脸对齐
 */
        TIMER_START(Align)
        std::vector<float> lmks;
        for (int j = 0; j < LMK_NUM; ++j) {
            lmks.push_back(box.ppoint[2 * j]);
            lmks.push_back(box.ppoint[2 * j + 1]);
        }
        uint8_t* p_src   = (uint8_t*)input_rgb;
        uint8_t* p_align = (uint8_t*)align_face.data;
        memset(align_face,0,NORM_W * NORM_H * NORM_C);
        if (!faceAlign((unsigned char*)input_rgb,rotate_width, rotate_height,CHANNEL,(unsigned char*)p_align,NORM_W, NORM_H,box)) {
            LOGE("failed to get norm face!");
            // TODO: wrong return value
            ret = BST_FU_E_INNER_ERROR;
            TIMER_STOP(Align)
            break;
        }
       
        if(quality_output.face_brightness < (filter_config.live_face_dark_thres))
        {
            bstutils::ColorHistogramEqualization(p_align,NORM_W, NORM_H,true,0.5);
            //  bstutils::ColorHistogramEqualizationOriginal(p_align,NORM_W, NORM_H);
        }

#ifdef DUMP
#define DUMP
        bstutils::save_image_png("faceAlign"+std::to_string(g_blink->g_count), p_align, NORM_W, NORM_H, 3);
#endif
        TIMER_STOP(Align)



/***********************************************************************************************************************************************************************
 *  7.       blink judge，睁闭眼判断
 */

    if (filter_config.blink_filter_enable) {
        TIMER_START(Blink)
#ifdef SECUREOS
    g_blink = new ModuleBlink();
    g_blink->init();
#endif
            /// 角度过滤 // yaw,pitch,roll
            // TODO 参数外放
//            if (abs(lmk_info.poses[0]) > 40 || abs(lmk_info.poses[1]) > 40) {
//                ret = BST_FU_C_POSE_ABNORMAL;
//                break;
//            }
/*** landmark vis may not work
            int occ_flag = g_landmarker->judge_occlusion(lmk_info, 0); // 1 遮挡，0 未遮挡
            if (occ_flag == 1) {
                g_dump_info.blink_score = -1.; /// 关键点支持遮挡检测
                ret = BST_FU_C_CLOSED_EYE;
                break;
            }

*/
            int blink_target = 224;
            ncnn::Mat      blinkface(blink_target, blink_target, NORM_C, 1);
            if (!faceAlign((unsigned char*)input_rgb,rotate_width, rotate_height,CHANNEL,(unsigned char*)blinkface,blink_target, blink_target,box)) {
                LOGE("failed to get norm face!");
                // TODO: wrong return value
                ret = BST_FU_E_INNER_ERROR;
                TIMER_STOP(Blink)
                break;
            }                                                                                                                                             // [lx1, ly1, lx2, ly2, lscore, lstatus, rx1, ry1, rx2, ry2, rscore, rstatus]
            bool run_ok         = g_blink->perfom((uint8_t*)blinkface, blink_target, blink_target, CHANNEL, rotate_objects[max_idx], filter_config.blink_thres,filter_config.mask_hole_thres,quality_output.face_brightness,lmk_info); // open score
//            bool run_ok         = g_blink->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], filter_config.blink_thres,quality_output.face_brightness,lmk_info); // open score
#ifdef SECUREOS
    g_blink->release();
    delete g_blink;
    g_blink = NULL;
#endif

            if (run_ok) {
                g_dump_info.blink_score = lmk_info.eye_open_status;
                if (lmk_info.eye_open_status < 1) {
                    ret = BST_FU_C_CLOSED_EYE;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
//                    g_continue_real_cnt = 0;
                    TIMER_STOP(Blink)
                    break;
                }
                else if (lmk_info.eye_open_status > 1 && filter_config.live_dark_thres >0 && filter_config.live_normal_thres > 0  && filter_config.live_filter_enable) {
                    ret = BST_FU_C_SUSPECTED;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
//                    g_continue_real_cnt = 0;
                    TIMER_STOP(Blink)
                    break;
                }

            }
            else {
                ret = BST_FU_E_INNER_ERROR;
                g_continue_real_cnt = 0;
//                g_continue_livingD_cnt = 0;
//                g_continue_livingC_cnt = 0;
                TIMER_STOP(Blink)
                break;
            }
              TIMER_STOP(Blink)
        }

/***********************************************************************************************************************************************************************
 *  8.       遮挡判断
 */

        if (filter_config.occ_filter_enable) {
            if (inner_type == 1) {
                int occ_flag = judge_occlusion(lmk_info,1); // 1 遮挡，0 未遮挡
                if (occ_flag == 1) {
                    ret = BST_FU_C_OCCLUDED;
                    g_continue_real_cnt = 0;
    //                g_continue_livingD_cnt = 0;
    //                g_continue_livingC_cnt = 0;
                    break;
                }
                TIMER_START(Nose)
                #ifdef SECUREOS
                    g_nose = new ModuleNose();
                    g_nose->init();
                #endif                                                                                                                                         // [lx1, ly1, lx2, ly2, lscore, lstatus, rx1, ry1, rx2, ry2, rscore, rstatus]
                bool run_ok         = g_nose->perfom((uint8_t*)align_face, NORM_W, NORM_H, CHANNEL, rotate_objects[max_idx], 0.5,quality_output.face_brightness,lmk_info); // open score
                #ifdef SECUREOS
                    g_nose->release();
                    delete g_nose;
                    g_nose = NULL;
                #endif
                if (run_ok) {
                    g_dump_info.nose_status = lmk_info.nose_status;
                    if (lmk_info.nose_status == 0) {
                        ret = BST_FU_C_OCCLUDED;
                        g_continue_real_cnt = 0;
                        TIMER_STOP(Nose)
                        break;
                    }
                }
                else {
                    ret = BST_FU_E_INNER_ERROR;
                    g_continue_real_cnt = 0;
                    //                g_continue_livingD_cnt = 0;
                    //                g_continue_livingC_cnt = 0;
                    TIMER_STOP(Nose)
                    break;
                }
                TIMER_STOP(Nose)
            }


            /// 口罩
            if (inner_type == 1 ) { /// || inner_type == 2
                if (lmk_info.mask_prob > filter_config.mask_thres) { // 口罩遮挡
                    // LOGI("mask");
                    ret = BST_FU_C_OCCLUDED;
                    g_continue_real_cnt = 0;
//                    g_continue_livingD_cnt = 0;
//                    g_continue_livingC_cnt = 0;
                  
                    break;
                }
            }
        }


/***********************************************************************************************************************************************************************
 *  9.       cal live score，活体检测
 */
        TIMER_START(LivingC)
        if (filter_config.live_filter_enable and ret != BST_FU_C_SUSPECTED) {
            float liveC_score = -1;
            if (
                // iso <=0 ||  
                (quality_output.bg_brightness <= filter_config.live_bg_dark_thres && quality_output.bg_brightness > 0)
                || (iso > filter_config.live_ISO_thres || quality_output.face_brightness <= filter_config.live_face_dark_thres)
                ) {
                LOGI("dark mode");
                float liveC_threshold = filter_config.live_dark_thres;

                {
#ifdef SECUREOS
                g_dark_live = new ModuleLiving();
                if (NULL == g_dark_live) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_dark_live->init(dark_mode);
#endif

                    g_dark_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
#ifdef SECUREOS
                    g_dark_live->release();
                    delete g_dark_live;
                    g_dark_live = NULL;
#endif
                }


                g_dump_info.liveC_score = liveC_score;
                if (liveC_score < liveC_threshold) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_SUSPECTED;
                    g_continue_livingC_cnt +=1;
                    if (g_continue_livingC_cnt >= filter_config.max_livingC_suspected_live_attack){

                        if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                            g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                        }else{
                            g_processing_max_continue_real = filter_config.max_continue_real;
                        }

                    }
                }
                else{
                    g_continue_real_cnt++;
                }
            } else{
                LOGI("normal mode");
                float liveC_threshold = filter_config.live_normal_thres;
//                if (abs(lmk_info.poses[0]) > filter_config.match_thres_pitch_thres || abs(lmk_info.poses[1]) > filter_config.match_thres_pitch_thres) { /// pitch大角度
//                    liveC_threshold = filter_config.live_normal_thres;
//                    LOGI("big_angel_mode");
//#ifdef SECUREOS
//                    g_big_angel_live = new ModuleLiving();
//                    g_big_angel_live->init(big_angel_mode);
//#endif
//
//                    g_big_angel_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
//#ifdef SECUREOS
//                    g_big_angel_live->release();
//                    delete g_big_angel_live;
//                    g_big_angel_live = NULL;
//#endif
//                }
//                else
                {
#ifdef SECUREOS
                g_normal_live = new ModuleLiving();
                if (NULL == g_normal_live) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_normal_live->init(normal_mode);
#endif

                    g_normal_live->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, rotate_objects[max_idx], liveC_score,lmk_info,liveC_threshold);
#ifdef SECUREOS
                    g_normal_live->release();
                    delete g_normal_live;
                    g_normal_live = NULL;
#endif
                }


                g_dump_info.liveC_score = liveC_score;
                if (liveC_score < liveC_threshold) {
                    g_continue_real_cnt = 0;
                    ret = BST_FU_C_SUSPECTED;
                    g_continue_livingC_cnt +=1;
                    if (g_continue_livingC_cnt >= filter_config.max_livingC_suspected_live_attack){

                        if ( filter_config.after_suspected_continue_real > filter_config.max_continue_real){
                            g_processing_max_continue_real = filter_config.after_suspected_continue_real;
                        }else{
                            g_processing_max_continue_real = filter_config.max_continue_real;
                        }

                    }
                }
                else{
                    g_continue_real_cnt++;
                }
            }

        } 
        else {
            g_continue_real_cnt++;
        }
        TIMER_STOP(LivingC)
/**********************************************************************************************************************************
 * 7. 活体攻击策略，判为活体攻击后，如果判断是同一个人，则一直返回活体攻击
 */
        TIMER_START(LivingStrategy)
        std::vector<float> sus_feature;
       {

            if ( g_suspected_attack_cnt ==0){ ///BST_FU_C_SUSPECTED == ret &&
                // get face feature，人脸特征 arcface by dong.wang

#ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
#endif

                g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);

#ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
#endif
                memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_last_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
            }
            else { /// (g_suspected_attack_cnt != 0)

#ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
#endif

                g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);

#ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
#endif
                LOGI("g_continue_livingD_cnt:%d g_continue_livingC_cnt:%d"
                     "",g_continue_livingD_cnt,g_continue_livingC_cnt)
                if (g_continue_livingD_cnt >= filter_config.max_livingD_suspected_live_attack ||
                    g_continue_livingC_cnt  >= filter_config.max_livingC_suspected_live_attack){
                    float  score = 0;
                    bstutils::bstFeatureCompare(g_last_feature_database,sus_feature.data(),sus_feature.size(),score);
                    if (score > filter_config.match_thres){
                        /// 连续是同一个人
                        LOGI("%.2f same person,keep suspected",score)
                        ret = BST_FU_C_SUSPECTED;

                    }else{
                        LOGI("%.2f,not same person",score)
                        g_suspected_attack_cnt = 0;
                        g_continue_livingD_cnt = 0;
                        g_continue_livingC_cnt = 0;
                        g_suspected_attack_frame_cnt = 0;
                    }
                }
//                LOGI("FET: %.4f, %.4f, %.4f", feature[0], feature[100], feature[FEATURE_DIM_SDK - 1]);

                memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_last_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
            }
            LOGI("ret:%x",ret);
            if (BST_FU_C_SUSPECTED == ret){
                g_continue_real_cnt = 0;
                TIMER_STOP(LivingStrategy)
                break;
            }
        }
        TIMER_STOP(LivingStrategy)

/***********************************************************************************************************************************************************************
 *  10.        人脸识别
 */
        TIMER_START(Feature)
        LOGI("InnerAlgorithm Feature");
        bst_face_output->threshold = 0.;
        if ( inner_type == 1){
            if (g_enroll_quality > g_dump_info.quality_score){
                g_enroll_quality = g_dump_info.quality_score;
                if(g_enroll_align_face == NULL){
                    g_enroll_align_face = new unsigned char[NORM_W*NORM_H* NORM_C];
                    if (NULL == g_enroll_align_face) {
                        ret = BST_FU_E_OOM;
                        break;
                    }
                }
                memcpy(g_enroll_align_face,align_face.data,NORM_W*NORM_H*NORM_C*sizeof(unsigned char) );
                LOGI("best face quality");
            }
        }

        // 增加活体检测连续次数判断
        LOGI("g_continue_real_cnt: %d , g_processing_max_continue_real: %d", g_continue_real_cnt, g_processing_max_continue_real)

        if (g_continue_real_cnt >= g_processing_max_continue_real) { ///

#ifdef DUMP
            bstutils::save_image_png("align_face"+std::to_string(g_blink->g_count), align_face, NORM_W, NORM_H, CHANNEL);
#endif

            // get face feature，人脸特征 arcface by dong.wang
            if(sus_feature.size() == 0){


                std::vector<float> feature;
    #ifdef SECUREOS
                g_recognizer = new ModuleRecognizer();
                if (NULL == g_recognizer) {
                    ret = BST_FU_E_OOM;
                    break;
                }
                g_recognizer->init();
    #endif


                if ( inner_type == 1){
                    g_recognizer->perm((uint8_t*)g_enroll_align_face, NORM_W, NORM_H, NORM_C, sus_feature);
                }else{
                    g_recognizer->perm((uint8_t*)align_face.data, NORM_W, NORM_H, NORM_C, sus_feature);
                }
    #ifdef SECUREOS
                g_recognizer->release();
                delete g_recognizer;
                g_recognizer = NULL;
    #endif
            }
            memset(bst_face_output->feature, 0, FEATURE_DIM_SDK * sizeof(float));
            memcpy(bst_face_output->feature, sus_feature.data(), sizeof(float) * sus_feature.size());

            if (inner_type == 1){
#ifdef DEBUG_MATCH
                //            int offset = FEATURE_DIM_SDK / 2;
                //            offset     = offset > feature.size() ? feature.size() : offset;
                //            memcpy(g_feature_database, bst_face_output->feature, offset);
                //            memcpy(g_feature_database + offset, bst_face_output->feature, offset);
                memset(g_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
                memcpy(g_feature_database, sus_feature.data(), sizeof(float) * sus_feature.size());
#endif
            }
            if (inner_type == 1){
                ret = BST_FU_SUCCESS;
            } else {

                if (lmk_info.mask_prob > filter_config.mask_thres) { /// 带口罩
                    bst_face_output->threshold = filter_config.match_thres_withmask;
                }

                else if (lmk_info.poses[0] < -filter_config.match_thres_left_pitch_thres || lmk_info.poses[0] > filter_config.match_thres_right_pitch_thres) {
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }

                //pitch 大角度 // yaw,pitch,roll
                // pitch 负值 up_pitch_thres抬头  正值  down_pitch_thres 低头角度
                else if (lmk_info.poses[1] < -filter_config.match_thres_up_pitch_thres || lmk_info.poses[1] > filter_config.match_thres_down_pitch_thres) {
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }
                else if (quality_output.face_brightness < filter_config.live_face_dark_thres) { /// 大角度
                    bst_face_output->threshold = filter_config.match_thres_big_pitch;
                }
                else {
                    bst_face_output->threshold = filter_config.match_thres;
                }
                // 暗光、逆光 模糊人脸，阈值降低
                if(iso > filter_config.iso_dark_level_1_thres){
                    if(g_dump_info.model_blur_score > 0.5){
                        bst_face_output->threshold = filter_config.match_thres_big_pitch;
                    }
                }

                ret = BST_FU_CONTINUE;
            }
//            g_continue_real_cnt = 0;
            TIMER_STOP(Feature)
            break;
        }
        /// 加入注册选择质量最优
        else {

        }
        TIMER_STOP(Feature)
        ret = BST_FU_CONTINUE;
        break;
    } while (false);

    if (input_rotate) delete[] input_rotate;
    if (input_rgb) delete[] input_rgb;
    if (tmp_src_img)  delete[] tmp_src_img;
    if (top_src_img) delete[] top_src_img;
    if (input_gray) delete[] input_gray;

    TIMER_STOP(InnerAlgoTotal)
    g_dump_info.ret_val = ret;

    return ret;
}

int InnerEnroll(const BSTFaceUnlockFrameData* const bst_face_input,const int iso, BSTFaceFeature* const bst_face_output) {
    return InnerAlgorithm(bst_face_input,iso, bst_face_output, g_config.enroll_config, 1);
}

int InnerAuth(const BSTFaceUnlockFrameData* const bst_face_input,const int iso, BSTFaceFeature* bst_face_output) {
    return InnerAlgorithm(bst_face_input,iso, bst_face_output, g_config.authen_config, 2);
}

PUBLIC_API
#ifdef MOCK_TEE
const BSTFaceUnlockVersion* bstFaceUnlockGetVersionInner() {
#else
const BSTFaceUnlockVersion* bstFaceUnlockGetVersion() {
#endif
    g_version.base        = VERSION_BASE;
    g_version.major       = VERSION_MAJOR;
    g_version.minor       = VERSION_MINOR;
    g_version.build       = VERSION_BUILD;
    g_version.build_date  = g_build_date;
    g_version.lib_version = g_lib_version;
    memset(g_build_date, 0, 256);
    memset(g_build_date, 0, 256);
    sprintf(g_build_date, "%s %s", __DATE__, __TIME__);
    sprintf(g_lib_version, "%d.%d.%d.%d.%d.%s", VERSION_BASE, VERSION_MAJOR, VERSION_MINOR, VERSION_BUILD, VERSION_MODEL, VERSION_TARGET);

    g_version.config_version = nullptr;

    return &g_version;
};

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockInitInner(
#else
int bstFaceUnlockInit(
#endif
    const BSTFaceUnlockConfig* const config) {
    LOGI("BSFU Init");
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    int ret = 0;
    LOGE("BlackSesame FaceUnlock Alg Version : %s", g_lib_version);
    LOGE("BlackSesame FaceUnlock Build Date  : %s %s", __DATE__, __TIME__);
    if (true == g_init_flag) {
        LOGE("Already initialized.")
        return BST_FU_E_INVALID_INPUT;
    }
    // Load Cfg
#ifdef SECUREOS
    ((BSTFaceUnlockConfig*)config)->config_type = BST_FU_CONFIG_TYPE_BUILT_IN;
    if (!LoadConfig(*config, g_config, (char*)g_cfg_version)) {
        LOGE("Load cfg fail.");
        return BST_FU_E_INVALID_CONFIG;
    }
#else
    // if (config->config_type == BST_FU_CONFIG_TYPE_BUILT_IN) {
    //     return BST_FU_E_INVALID_CONFIG;
    // }
    if (!LoadConfig(*config, g_config, (char*)g_cfg_version)) {
        return BST_FU_E_INVALID_CONFIG;
    }
#endif

    // log
    if (g_config.common_config.log_enable == true) {
        bstutils::update_log_setting(true);
    } else {
        bstutils::update_log_setting(false);
    }

    if (g_config.common_config.verfy_time == true) {
        LOGE("===========BlackSesameFU is debug version===========");
        LOGE("**************************************** BlackSesameFaceUnlock Version: %s Evaluation Only ****************************************", g_lib_version);
    }

    g_detect = new ModuleDetect();
    if (NULL == g_detect) {
        return BST_FU_E_OOM;
    }
    ret      = g_detect->init();
    if (ret != 0) {
        LOGE("ModuleDetect init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
#ifndef SECUREOS
    g_screen_paper_detect = new ModuleScreenPaperDetect();
    if (NULL == g_screen_paper_detect) {
        return BST_FU_E_OOM;
    }
    ret      = g_screen_paper_detect->init();
    if (ret != 0) {
        LOGE("ModuleScreenPaperDetect init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
    g_landmarker = new ModuleLandmarker();
    if (NULL == g_landmarker) {
        return BST_FU_E_OOM;
    }
    ret          = g_landmarker->init();
    if (ret != 0) {
        LOGE("ModuleLandmarker init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
#endif
    g_quality_assessor = new ModuleQualityAssessor();
    if (NULL == g_quality_assessor) {
        return BST_FU_E_OOM;
    }
    ret                = g_quality_assessor->init();
    if (ret != 0) {
        LOGE("ModuleQualityAssessor init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
#ifndef SECUREOS
     g_dark_live = new ModuleLiving();
     if (NULL == g_dark_live) {
        return BST_FU_E_OOM;
    }
    ret    = g_dark_live->init(dark_mode);
    if (ret != 0) {
        LOGE("ModuleLiving init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
    g_normal_live = new ModuleLiving();
     if (NULL == g_normal_live) {
        return BST_FU_E_OOM;
    }
    ret    = g_normal_live->init(normal_mode);
    if (ret != 0) {
        LOGE("ModuleLiving init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
#endif





    g_motion_detector = new ModuleMotion();
#ifndef SECUREOS
    g_blink = new ModuleBlink();
    if (NULL == g_blink) {
        return BST_FU_E_OOM;
    }
    ret     = g_blink->init();
    if (ret != 0) {
        LOGE("ModuleBlink init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }
    g_nose = new ModuleNose();
    if (NULL == g_nose) {
        return BST_FU_E_OOM;
    }
    ret     = g_nose->init();
    if (ret != 0) {
        LOGE("ModuleNose init wrong");
#ifdef MOCK_TEE
        bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
        return BST_FU_E_NOT_INITED;
    }


     g_recognizer = new ModuleRecognizer();
     if (NULL == g_recognizer) {
        return BST_FU_E_OOM;
    }
     ret          = g_recognizer->init();
     if (ret != 0) {
         LOGE("ModuleRecognizer init wrong");
#ifdef MOCK_TEE
         bstFaceUnlockUninitInner();
#else
        bstFaceUnlockUninit();
#endif
         return BST_FU_E_NOT_INITED;
     }
#endif
    g_enroll_quality = FLT_MAX;
#ifdef DEBUG_MATCH
    memset(g_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
#endif
    memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
    g_init_flag = true;
    return BST_FU_SUCCESS;
}

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockUninitInner() {
#else
int bstFaceUnlockUninit() {
#endif
    LOGI("BSFU Uninit");
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    if (true == g_init_flag) {
        LOGE("Already uninitialized.");
        return BST_FU_E_INVALID_INPUT;
    }
    if (g_detect != NULL) {
        g_detect->release();
        delete g_detect;
        g_detect = NULL;
    }
    if (g_screen_paper_detect != NULL) {
        g_screen_paper_detect->release();
        delete g_screen_paper_detect;
        g_screen_paper_detect = NULL;
    }

    if (g_landmarker != NULL) {
        g_landmarker->release();
        delete g_landmarker;
        g_landmarker = NULL;
    }
    if (g_quality_assessor != NULL) {
        g_quality_assessor->release();
        delete g_quality_assessor;
        g_quality_assessor = NULL;
    }
    if (g_dark_live != NULL) {
        g_dark_live->release();
        delete g_dark_live;
        g_dark_live = NULL;
    }
    if (g_normal_live != NULL) {
        g_normal_live->release();
        delete g_normal_live;
        g_normal_live = NULL;
    }
//    if (g_big_angel_live != NULL) {
//        g_big_angel_live->release();
//        delete g_big_angel_live;
//        g_big_angel_live = NULL;
//    }
    if (g_motion_detector != NULL) {
        g_motion_detector->release();
        delete g_motion_detector;
        g_motion_detector = NULL;
    }
    if (g_blink != NULL) {
        g_blink->release();
        delete g_blink;
        g_blink = NULL;
    }
    if (g_nose != NULL) {
        g_nose->release();
        delete g_nose;
        g_nose = NULL;
    }
    if (g_recognizer != NULL) {
        g_recognizer->release();
        delete g_recognizer;
        g_recognizer = NULL;
    }
    if(g_enroll_align_face){
        delete[] g_enroll_align_face;
        g_enroll_align_face = NULL;
    }
    g_init_flag = false;
    return BST_FU_SUCCESS;
}

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockEnrollInner(
#else
int bstFaceUnlockEnroll(
#endif
    const BSTFaceUnlockImage* const p_img_in,
    BSTFaceFeature* const           p_feature) {

    LOGI("BSFU Enroll");
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    if (false == g_init_flag) {
        LOGE("Not initialized.");
        return BST_FU_E_NOT_INITED;
    }
    // log
    if (g_config.common_config.log_enable == true) {
        bstutils::update_log_setting(true);
    } else {
        bstutils::update_log_setting(false);
    }

    LOGE("BlackSesame FaceUnlock Alg Version : %s", g_lib_version);
    LOGE("BlackSesame FaceUnlock Build Date  : %s %s", __DATE__, __TIME__);
    LOGE("BlackSesame FaceUnlock Cfg Version : %s", g_cfg_version);

    LOGI("Enroll, format: %d, width: %d, height: %d, stride: %d, rotation: %d",
         p_img_in->img_data.format, p_img_in->img_data.width, p_img_in->img_data.height,
         p_img_in->img_data.stride, p_img_in->img_data.rotation);
    LOGE("Enroll, iso: %d", p_img_in->meta_data.iso);

    // do enroll
    if (g_enroll_frame_num > g_config.enroll_config.timeout_frame) {
        return BST_FU_F_TIMEOUT;
    }
    if (g_enroll_frame_num == 0){
        g_processing_max_continue_real = g_config.enroll_config.max_continue_real;
    }
    g_enroll_frame_num++;
    memset(&g_dump_info, 0, sizeof(g_dump_info));
    memset(p_feature->feature, 0, FEATURE_DIM_SDK * sizeof(float));
    p_feature->threshold = 0.;
    int ret = InnerEnroll(&p_img_in->img_data,p_img_in->meta_data.iso, p_feature);
    ret     = MultiFrameSpoofCheck(ret, g_config.enroll_config);
    g_dump_info.ret_val = ret;

#ifndef SECUREOS
    FaceUnlockDump(true, g_enroll_frame_num, &p_img_in->img_data);
#endif

    // return info

    float* p_reserved    = (float*)&(p_img_in->meta_data.reserved);
    p_reserved[0]        = g_dump_info.liveC_score;
    LOGI("%d frame,R: %d,Face: (%d, %d) (%d, %d),y: %.1f,p: %.1f,r: %.1f,nose: %d,eye: %d,blur: %.2f,%.2f，mask: %.2f,lvD: %.2f,lvC: %.4f,face_bri: %.f,bg_bri: %.f,quality: %.2f,In_ret: %x",
         g_enroll_frame_num,g_dump_info.rotate_order, g_dump_info.facebox_x1, g_dump_info.facebox_y1, g_dump_info.facebox_x2, g_dump_info.facebox_y2,g_dump_info.yaw, g_dump_info.pitch, g_dump_info.roll,
         g_dump_info.nose_status,g_dump_info.blink_score, g_dump_info.model_blur_score, g_dump_info.quality_blur_score, g_dump_info.mask_score, g_dump_info.liveD_score, g_dump_info.liveC_score, g_dump_info.face_brightness,g_dump_info.bg_brightness, g_dump_info.quality_score,g_dump_info.ret_val);

//    LOGI("THR: %f, LIV: %f, RET: %X", p_feature->threshold, p_reserved[0], ret);
    LOGI("FET: %.4f, %.4f, %.4f", p_feature->feature[0], p_feature->feature[100], p_feature->feature[FEATURE_DIM_SDK - 1]);

    LOGI("Enroll, ret: %x", ret);
    return ret;
}

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockEnrollResetInner() {
#else
int bstFaceUnlockEnrollReset() {
#endif
    LOGI("BSFU EnrollReset");
    LOGE("BlackSesame FaceUnlock Cfg Version : %s", g_cfg_version);
    if (g_config.common_config.verfy_manu) {
        LOGI("Manu Check On - %s", g_config.common_config.manufacturer.c_str());
    }
    if (g_config.common_config.verfy_prod) {
        LOGI("Product Check On - %s", g_config.common_config.product_name.c_str());
    }
    if (g_config.common_config.verfy_time) {
        LOGI("Time Check On - %s", g_config.common_config.time_end.c_str());
    }
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    if (false == g_init_flag) {
        LOGE("Not initialized.");
        return BST_FU_E_NOT_INITED;
    }

    g_enroll_frame_num           = 0;
    g_auth_frame_num       = 0;
    g_suspected_attack_cnt       = 0;
    g_continue_livingD_cnt       = 0;
    g_continue_livingC_cnt       = 0;
    g_processing_max_continue_real   = g_config.enroll_config.max_continue_real;
    g_suspected_attack_frame_cnt = 0;
    g_continue_real_cnt          = 0;
    g_continue_multi_person_cnt = 0;
    g_first_in = 0;
    g_enroll_quality = FLT_MAX;
    memset(&g_last_cache_bbox, 0, sizeof(Bbox));
    memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
    if(g_enroll_align_face){
        delete[] g_enroll_align_face;
        g_enroll_align_face = NULL;
    }

    return BST_FU_SUCCESS;
}

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockAuthenticateInner(
#else
int bstFaceUnlockAuthenticate(
#endif
    const BSTFaceUnlockImage* const p_img_in,
    BSTFaceFeature* const           p_feature) {
    LOGI("BSFU Auth");
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    if (false == g_init_flag) {
        LOGE("Not initialized.");
        return BST_FU_E_NOT_INITED;
    }
    
    // log
    if (g_config.common_config.log_enable == true) {
        bstutils::update_log_setting(true);
    } else {
        bstutils::update_log_setting(false);
    }

    LOGI("Auth, format: %d, width: %d, height: %d, stride: %d, rotation: %d",
         p_img_in->img_data.format, p_img_in->img_data.width, p_img_in->img_data.height,
         p_img_in->img_data.stride, p_img_in->img_data.rotation);
    LOGI("Auth, iso: %d", p_img_in->meta_data.iso);
    
    // do auth
    if (g_auth_frame_num <= 0) {

        g_processing_max_continue_real = g_config.authen_config.max_continue_real;

        g_auth_start = time(nullptr);
    } else {
        long diff = time(nullptr) - g_auth_start;
        if (diff >= g_config.authen_config.timeout_second)
            return BST_FU_F_TIMEOUT;
    }
    if (g_auth_frame_num > g_config.authen_config.timeout_frame) {
        return BST_FU_F_TIMEOUT;
    }
    g_auth_frame_num++;
    memset(&g_dump_info, 0, sizeof(g_dump_info));
    memset(p_feature->feature, 0, FEATURE_DIM_SDK * sizeof(float));
    p_feature->threshold = 0.;
    int ret = InnerAuth(&p_img_in->img_data,p_img_in->meta_data.iso, p_feature);

    ret     = MultiFrameSpoofCheck(ret, g_config.authen_config);
#ifndef SECUREOS
    FaceUnlockDump(false, g_auth_frame_num, &p_img_in->img_data);
#endif
#ifdef DEBUG_MATCH
    g_dump_info.match_score = InnerProduct(p_feature->feature, g_feature_database, FEATURE_DIM_SDK);
#else
    g_dump_info.match_score = -1.0;
#endif
    g_dump_info.ret_val = ret;

    LOGI("%d frame,R: %d,Face: (%d, %d) (%d, %d),y: %.1f,p: %.1f,r: %.1f,eye: %d,blur: %.2f,%.2f,mask: %.2f,lvD: %.2f,lvC: %.4f,lvM:%d,face_bri: %.1f,bg_bri: %.1f,sim: %.2f,In_ret: %x",
         g_auth_frame_num,g_dump_info.rotate_order, g_dump_info.facebox_x1, g_dump_info.facebox_y1, g_dump_info.facebox_x2, g_dump_info.facebox_y2,g_dump_info.yaw, g_dump_info.pitch, g_dump_info.roll,
         g_dump_info.blink_score, g_dump_info.model_blur_score, g_dump_info.quality_blur_score, g_dump_info.mask_score, g_dump_info.liveD_score, g_dump_info.liveC_score, g_dump_info.livingM_flag,g_dump_info.face_brightness,g_dump_info.bg_brightness,g_dump_info.match_score ,g_dump_info.ret_val);

//    LOGI("THR: %f, LIV: %f,COUNT %d, RET: %X", p_feature->threshold, p_reserved[0],g_suspected_attack_cnt, ret);
    LOGI("FET: %.4f, %.4f, %.4f", p_feature->feature[0], p_feature->feature[100], p_feature->feature[FEATURE_DIM_SDK - 1]);

    LOGI("Auth, ret: %x", ret);
    // return info
    float* p_reserved    = (float*)&(p_img_in->meta_data.reserved);
    p_reserved[0]        = g_dump_info.liveC_score;
    return ret;
}

PUBLIC_API
#ifdef MOCK_TEE
int bstFaceUnlockAuthenticateResetInner() {
#else
int bstFaceUnlockAuthenticateReset() {
#endif
    LOGI("BSFU AuthReset");
#ifndef SECUREOS
    AutoLock auto_lock(&g_api_mutex);
#endif
    if (false == g_init_flag) {
        LOGE("Not initialized.");
        return BST_FU_E_NOT_INITED;
    }
    g_enroll_frame_num           = 0;
    g_auth_frame_num       = 0;
    g_suspected_attack_cnt = 0;
    g_continue_livingD_cnt = 0;
    g_continue_livingC_cnt = 0;
    g_suspected_attack_frame_cnt = 0;
    g_continue_real_cnt = 0;
    g_processing_max_continue_real = g_config.authen_config.max_continue_real;
    g_continue_multi_person_cnt = 0;
    g_first_in = 0;
    g_enroll_quality = FLT_MAX;
    memset(&g_last_cache_bbox, 0, sizeof(Bbox));
    memset(g_last_feature_database, 0, FEATURE_DIM_SDK * sizeof(float));
    if(g_enroll_align_face){
        delete[] g_enroll_align_face;
        g_enroll_align_face = NULL;
    }
    return BST_FU_SUCCESS;
}

