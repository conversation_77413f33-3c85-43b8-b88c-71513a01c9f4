cmake_minimum_required (VERSION 3.4)

project("bstFaceUnlock")

# source code
file(GLOB_RECURSE SRCS_CPP ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp)
file(GLOB_RECURSE SRCS_C ${CMAKE_CURRENT_SOURCE_DIR}/*.c)

# build lib
add_library(BSTFaceAuthTEE STATIC ${SRCS_CPP} ${SRCS_C} model/ModuleBlinkDetect.cpp model/ModuleRecognizer.cpp)
# add_library(bstFaceUnlock SHARED ${SRCS_CPP} ${SRCS_C})
# target_link_libraries(bstFaceUnlock ncnn openmath)

if(TARGET_PLATFORM STREQUAL "android-arm64-v8a" OR TARGET_PLATFORM STREQUAL "android-armeabi-v7a" OR TARGET_PLATFORM STREQUAL "android-sim-secureos-arm64-v8a")
    add_library(bstFaceUnlock SHARED ${SRCS_CPP} ${SRCS_C})
    # target_compile_options(bstFaceUnlock PUBLIC -fsanitize=address -fno-omit-frame-pointer)
    # set_target_properties(bstFaceUnlock PROPERTIES LINK_FLAGS -fsanitize=address)
    target_link_libraries(bstFaceUnlock infer log)
endif()