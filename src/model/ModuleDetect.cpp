#include "model/ModuleDetect.h"
#include "CommonUtils.h"
#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif



struct indexed_value {
    float value;
    int index;
    indexed_value(float v, int i) : value(v), index(i) {};
};

indexed_value min(indexed_value a, indexed_value b) {
    if (a.value > b.value) {
        return b;
    } else {
        return a;
    }
}


bool outside_border(const BBox& box, int width, int height, const FilterConfig& cfg) {
    //     LOGI("judge_border width: %d height: %d box.bbox.w: %d box.bbox.h: %d box.bbox.x: %d box.bbox.y: %d",
    //         width, height, box.bbox.w, box.bbox.h, box.bbox.x, box.bbox.y);
    /// 归一化到100了
    float left = box.x * 100. / width;
    float right = 100. - (box.w + box.x) * 100. / width;
    float top = box.y * 100. / height;
    float bottom = 100. - (box.y + box.h) * 100. / height;



    Object border;
    border.x1 = cfg.border_left;
    border.x2 = 100 - cfg.border_right;
    border.y1 = cfg.border_top;
    border.y2 = 100 - cfg.border_bottom;
    Point left_top;
    left_top.x = left;
    left_top.y = top;
    Point right_top;
    right_top.x = right;
    right_top.y = top;
    Point left_bottom;
    left_bottom.x = left;
    left_bottom.y = bottom;
    Point right_bottom;
    right_bottom.x = right;
    right_bottom.y = bottom;
    if (!bstutils::pointInsideBox(left_top,border) &&
        !bstutils::pointInsideBox(right_top,border) &&
        !bstutils::pointInsideBox(left_bottom,border) &&
        !bstutils::pointInsideBox(right_bottom,border)){
        return true;
    }else{
        return false;
    }

}


bool judge_border(const BBox& box, int width, int height, const FilterConfig& cfg, int& ret) {
    //     LOGI("judge_border width: %d height: %d box.bbox.w: %d box.bbox.h: %d box.bbox.x: %d box.bbox.y: %d",
    //         width, height, box.bbox.w, box.bbox.h, box.bbox.x, box.bbox.y);

    float left = box.x * 100. / width;
    float right = 100. - (box.w + box.x) * 100. / width;
    float top = box.y * 100. / height;
    float bottom = 100. - (box.y + box.h) * 100. / height;

    float left_diff = left - cfg.border_left;
    float right_diff = right - cfg.border_right;
    float bottom_diff = bottom - cfg.border_bottom;
    float top_diff = top - cfg.border_top;

    //    LOGE("judge : %f %f %f %f", left_diff, right_diff, bottom_diff, top_diff);

    indexed_value min_dis = min(min(indexed_value(left_diff, 0), indexed_value(right_diff, 1)), min(indexed_value(top_diff, 2), indexed_value(bottom_diff, 3)));
    //    LOGE("judge : %d %f", min_dis.value, min_dis.index);
    if (min_dis.value < 0) {
        switch (min_dis.index) {
        case 0:
            ret = BST_FU_C_NEAR_BORDER_LEFT;
            break;
        case 1:
            ret = BST_FU_C_NEAR_BORDER_RIGHT;
            break;
        case 2:
            ret = BST_FU_C_NEAR_BORDER_TOP;
            break;
        case 3:
            ret = BST_FU_C_NEAR_BORDER_BOTTOM;
            break;
        }
        return false;
    } else {
        //        LOGE("judge : true");
        return true;
    }
}


void ModuleDetect::decode(const ncnn::Mat& data, const int* const anchor, std::vector<Object>& prebox, int stride,float  conf_thd) {
    int    fea_h        = data.h;
    int    fea_w        = data.w;
    int    spacial_size = fea_w * fea_h;
    int    channels     = NUM_KEYPOINTS * 3 + 6;
    float* ptr          = (float*)(data.data);
    for (int c = 0; c < 3; c++) { // sizeof anchor = 6; 3 = 6 / 2
        float  anchor_w = float(anchor[c * 2 + 0]);
        float  anchor_h = float(anchor[c * 2 + 1]);
        float* ptr_x    = ptr + spacial_size * (c * channels + 0);
        float* ptr_y    = ptr + spacial_size * (c * channels + 1);
        float* ptr_w    = ptr + spacial_size * (c * channels + 2);
        float* ptr_h    = ptr + spacial_size * (c * channels + 3);
        float* ptr_s    = ptr + spacial_size * (c * channels + 4);
        float* ptr_c    = ptr + spacial_size * (c * channels + 5);

        for (int i = 0; i < fea_h; i++) {
            for (int j = 0; j < fea_w; j++) {
                int   index      = i * fea_w + j;
                float confidence = bstutils::sigmoid(ptr_s[index]) * bstutils::sigmoid(ptr_c[index]);
                if (confidence > conf_thd) {
                    Object temp_box;
                    float  dx = bstutils::sigmoid(ptr_x[index]);
                    float  dy = bstutils::sigmoid(ptr_y[index]);
                    float  dw = bstutils::sigmoid(ptr_w[index]);
                    float  dh = bstutils::sigmoid(ptr_h[index]);

                    float pb_cx = (dx * 2.f - 0.5f + j) * stride;
                    float pb_cy = (dy * 2.f - 0.5f + i) * stride;

                    float pb_w = pow(dw * 2.f, 2) * anchor_w;
                    float pb_h = pow(dh * 2.f, 2) * anchor_h;

                    temp_box.score = confidence;
                    temp_box.x1    = pb_cx - pb_w * 0.5f;
                    temp_box.y1    = pb_cy - pb_h * 0.5f;
                    temp_box.x2    = pb_cx + pb_w * 0.5f;
                    temp_box.y2    = pb_cy + pb_h * 0.5f;

                    for (int l = 0; l < NUM_KEYPOINTS; l++) {
                        temp_box.landmark[l].x    = (ptr[(spacial_size * (c * channels + l * 3 + 6)) + index] * 2 - 0.5 + j) * stride;
                        temp_box.landmark[l].y    = (ptr[(spacial_size * (c * channels + l * 3 + 7)) + index] * 2 - 0.5 + i) * stride;
                        temp_box.landmark[l].prob = bstutils::sigmoid(ptr[spacial_size * (c * channels + l * 3 + 8) + index]);
                    }
                    prebox.push_back(temp_box);
                }
            }
        }
    }
}

int ModuleDetect::init() {
    LOGI("ModuleDetect init");
    int ret = 0;
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    m_net.load_param(models_ncnn.detect_param_bin);
    m_net.load_model(models_ncnn.detect_bin);
#else
    ret = m_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.param");
    if (ret != 0) {
        return ret;
    }
    ret = m_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.bin");
#endif
    return ret;
}

bool ModuleDetect::preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* dst, int out_w, int out_h,
                              std::vector<float>& pad_info) {
    float          scale       = std::min((float)out_w / in_w, (float)out_h / in_h);
    int            mid_w       = static_cast<int>(in_w * scale);
    int            mid_h       = static_cast<int>(in_h * scale);
    unsigned char* dst_resized = new unsigned char[mid_w * mid_h * 3];
    if (NULL == dst_resized) return false;
    ncnn::resize_bilinear_c3(src, in_w, in_h, dst_resized, mid_w, mid_h);

    int top  = (static_cast<int>(out_h) - mid_h) / 2;
    int left = (static_cast<int>(out_w) - mid_w) / 2;
    //    memset(dst, 114. / 255., sizeof(float) * out_w * out_h * 3); /// /255.

    //    memset(dst, 114. / 255. , sizeof(float) * out_w * out_h * 3); /// /255.

    bstutils::norm_and_zeropadding(dst_resized, dst, mid_w, mid_h, out_w, out_h);

    pad_info.resize(3);
    pad_info[0] = static_cast<float>(left);
    pad_info[1] = static_cast<float>(top);
    pad_info[2] = scale;
    delete[] dst_resized;

    return true;
}

bool ModuleDetect::perfom(const unsigned char* const img, int w, int h, int c, std::vector<BBox>& objects, bool isEnroll, float min_face) {

    objects.clear();

    std::vector<unsigned char> ncnn_input(m_param.net_input_size * m_param.net_input_size * 3, 114);
    std::vector<float>         pad_info;

    if (!preprocess(img, w, h, ncnn_input.data(), m_param.net_input_size, m_param.net_input_size, pad_info)) return false;
    ncnn::Mat net_in = ncnn::Mat::from_pixels(ncnn_input.data(),
                                              ncnn::Mat::PIXEL_RGB, m_param.net_input_size, m_param.net_input_size);

#ifdef DUMP_DETECT
#define DUMP_DETECT
   //    bstutils::pretty_print(net_in);
//    bstutils::save_image_png("input", (unsigned char*)img,w,h,c);
    bstutils::save_image_png("PIXEL_RGB", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(0, scale);

    std::vector<Object> tmp_objects;
    ncnn::Extractor     ex = m_net.create_extractor();
    ex.set_light_mode(true);
    ex.input(0, net_in);

    // stride 8
    {
        ncnn::Mat out;
        ex.extract(156, out);
        if (isEnroll){
            decode(out, m_anchor0, tmp_objects, 8,m_param.conf_enroll_thd);
        }else{
            decode(out, m_anchor0, tmp_objects, 8,m_param.conf_auth_thd);
        }

    }
    // stride 16
    {
        ncnn::Mat out;
        ex.extract(157, out);
        if (isEnroll){
            decode(out, m_anchor1, tmp_objects, 16,m_param.conf_enroll_thd);
        }else{
            decode(out, m_anchor1, tmp_objects, 16,m_param.conf_auth_thd);
        }
    }
    // stride 32

    {
        ncnn::Mat out;
        ex.extract(158, out);
        if (isEnroll){
            decode(out, m_anchor2, tmp_objects, 32,m_param.conf_enroll_thd);
        }else{
            decode(out, m_anchor2, tmp_objects, 32,m_param.conf_auth_thd);
        }
    }

    bstutils::bubble_sort(tmp_objects.begin(), tmp_objects.end(), bstutils::cmp);
    bstutils::nms(tmp_objects,m_param.iou_thd);
    for (int i = 0; i < (int)tmp_objects.size(); i++) {
        tmp_objects[i].x1 = (tmp_objects[i].x1 - pad_info[0]) / pad_info[2];
        tmp_objects[i].y1 = (tmp_objects[i].y1 - pad_info[1]) / pad_info[2];
        tmp_objects[i].x2 = (tmp_objects[i].x2 - pad_info[0]) / pad_info[2];
        tmp_objects[i].y2 = (tmp_objects[i].y2 - pad_info[1]) / pad_info[2];
        float face_width = (tmp_objects[i].x2 - tmp_objects[i].x1);
        float face_height = (tmp_objects[i].y2 - tmp_objects[i].y1);
        if (face_width < w * min_face){
            continue;
        }
        if (face_height < h * min_face){
            continue;
        }

        BBox det_box(tmp_objects[i].x1, tmp_objects[i].y1, face_width,
                     face_height, tmp_objects[i].score);

#ifdef DUMP_DETECT
#define DUMP_DETECT
        bstutils::DrawRect((uint8_t*)img, w, h, 3, tmp_objects[i].x1, tmp_objects[i].y1, tmp_objects[i].x2, tmp_objects[i].y2, 1, 0, 255, 0);
#endif
       objects.push_back(det_box);
    }

#ifdef DUMP_DETECT
    bstutils::save_image_png("detectImage", (uint8_t*)img, w, h, 3);
#endif
    return true;
}

int ModuleDetect::release() {
    m_net.clear();
    return 0;
}

