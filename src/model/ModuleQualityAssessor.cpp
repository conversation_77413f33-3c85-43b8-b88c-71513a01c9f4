/**  
 * All rights Reserved, Designed By BST
 * @projectName bstFaceUnlock
 * @title     ModuleQualityAssessor   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 上午1:32  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//
#include "model/ModuleQualityAssessor.h"

#include <math.h>

ModuleQualityAssessor::~ModuleQualityAssessor() {
    release();
}

int ModuleQualityAssessor::init() {
    return 0;
}

int ModuleQualityAssessor::release() {
    return 0;
}
/**
 *
 * @param src
 * @param src_w
 * @param src_h
 * @param top y1
 * @param bottom y2
 * @param left x1
 * @param right x2
 * @param dst
 */
void CropImageC3_YH(unsigned char* src, int src_w, int src_h, int top, int bottom, int left, int right, unsigned char* dst) {
    // printf("left: %d, top: %d, right: %d, bottom: %d\n", left, top, right, bottom);
    left   = std::max(0, left);
    top    = std::max(0, top);
    right  = std::max(0, right);
    bottom = std::max(0, bottom);

    int dst_w = src_w - (left + right);
    int dst_h = src_h - (top + bottom);

    unsigned char* src_row;
    unsigned char* dst_row;

    for (int y = 0; y < dst_h; ++y) {
        const int src_y = y + top;
        src_row         = src + (src_y * src_w * 3);
        dst_row         = dst + (y * dst_w * 3);

        for (int x = 0; x < dst_w; ++x) {
            const int src_x = x + left;

            dst_row[x * 3 + 0] = src_row[src_x * 3 + 0];
            dst_row[x * 3 + 1] = src_row[src_x * 3 + 1];
            dst_row[x * 3 + 2] = src_row[src_x * 3 + 2];
        }
    }
}

void GetLaplacian(unsigned int width, unsigned int height, unsigned char* inImg, unsigned char* outImg) {
    // float kernel[9] = {2.f, 0.f, 2.f, 0.f, -8.f, 0.f, 2.f, 0.f, 2.f};   // OpenCV ksize = 3
    float kernel[9] = {0.f, 1.f, 0.f, 1.f, -4.f, 1.f, 0.f, 1.f, 0.f}; // OpenCV ksize = 1
    int   dx[9]     = {-1, 0, 1, -1, 0, 1, -1, 0, 1};
    int   dy[9]     = {-1, -1, -1, 0, 0, 0, 1, 1, 1};

    // Skip four borders
    for (int y = 1; y < (int)height - 1; y++) {
        for (int x = 1; x < (int)width - 1; x++) {
            int sum = 0;
            for (int k = 0; k < 9; k++) {
                sum += inImg[(y + dy[k]) * width + x + dx[k]] * kernel[k];
            }

            // Clip
            sum = std::max(0, sum);
            sum = std::min(255, sum);

            outImg[y * width + x] = sum;
        }
    }
}

float GetGrayAverage(unsigned char* img, int w, int h) {
    float sum = 0;
    for (int y = 0; y < h; ++y) {
        unsigned char* row = img + y * w;
        for (int x = 0; x < w; ++x) {
            sum += *(row + x);
        }
    }

    return sum / (w * h);
}

float GetGrayVariance(unsigned int width, unsigned int height, unsigned char* img) {
    if (width == 0 || height == 0) {
        return 0;
    }
    float mean       = GetGrayAverage(img, width, height);
    float difference = 0;
    for (int y = 1; y < (int)height - 1; y++) {
        unsigned char* row = img + y * width;
        for (int x = 1; x < (int)width - 1; x++) {
            difference += (row[x] - mean) * (row[x] - mean);
        }
    }

    return difference / (width * height);
}

void CvtRGBToGray(unsigned char* rgbImg, int width, int height, unsigned char* grayImg) {
    int b = 0, g = 0, r = 0;
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            r = rgbImg[(y * width + x) * 3];
            g = rgbImg[(y * width + x) * 3 + 1];
            b = rgbImg[(y * width + x) * 3 + 2];

            grayImg[y * width + x] = b * 0.0722 + g * 0.7152 + r * 0.2126;
        }
    }
}

float getProductBlur(unsigned char* rgbImg, int width, int height) {
    // LOGD("========== Check Blurriness ==========\n");
    // (BST) Variance of Laplacian
    unsigned char* grayImg = new unsigned char[width * height];
    if (NULL == grayImg) return 0.;
    CvtRGBToGray(rgbImg, width, height, grayImg);

    unsigned char* laplacianImg = new unsigned char[width * height];
    if (NULL == laplacianImg) return 0.;
    GetLaplacian(width, height, grayImg, laplacianImg);

    int            border_cut_size         = 5;
    unsigned char* laplacianImg_cut_border = new unsigned char[(width - border_cut_size * 2) * (height - border_cut_size * 2)];
    if (NULL == laplacianImg_cut_border) return 0.;
    for (int i = 0; i < height - border_cut_size * 2; i++) {
        memcpy(laplacianImg_cut_border + i * (width - border_cut_size * 2), laplacianImg + (border_cut_size + i) * width + border_cut_size,
               sizeof(unsigned char) * (width - border_cut_size * 2));
    }
    float var_cut_border = GetGrayVariance(width - border_cut_size * 2, height - border_cut_size * 2, laplacianImg_cut_border);

    delete[] grayImg;
    delete[] laplacianImg;
    delete[] laplacianImg_cut_border;

    return var_cut_border;
}


float reblur(const unsigned char *data, int width, int height)
{
    float blur_val = 0.0f;
    float kernel[9] = { 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f, 1.0f / 9.0f };
    float *BVer = new float[width * height];//垂直方向低通滤波后的结果
    float *BHor = new float[width * height];//水平方向低通滤波后的结果
    if (NULL == BVer || NULL == BHor) return 0.;

    float filter_data = 0.0;
    for (int i = 0; i < height; ++i)//均值滤波
    {
        for (int j = 0; j < width; ++j)
        {
            if (i < 4 || i > height - 5)
            {//处理边界 直接赋值原数据
                BVer[i * width + j] = data[i * width + j];
            }
            else
            {
                filter_data = kernel[0] * data[(i - 4) * width + j] + kernel[1] * data[(i - 3) * width + j] + kernel[2] * data[(i - 2) * width + j] +
                              kernel[3] * data[(i - 1) * width + j] + kernel[4] * data[(i)* width + j] + kernel[5] * data[(i + 1) * width + j] +
                              kernel[6] * data[(i + 2) * width + j] + kernel[7] * data[(i + 3) * width + j] + kernel[8] * data[(i + 4) * width + j];
                BVer[i * width + j] = filter_data;
            }

            if (j < 4 || j > width - 5)
            {
                BHor[i * width + j] = data[i * width + j];
            }
            else
            {
                filter_data = kernel[0] * data[i * width + (j - 4)] + kernel[1] * data[i * width + (j - 3)] + kernel[2] * data[i * width + (j - 2)] +
                              kernel[3] * data[i * width + (j - 1)] + kernel[4] * data[i * width + j] + kernel[5] * data[i * width + (j + 1)] +
                              kernel[6] * data[i * width + (j + 2)] + kernel[7] * data[i * width + (j + 3)] + kernel[8] * data[i * width + (j + 4)];
                BHor[i * width + j] = filter_data;
            }

        }
    }

    float D_Fver = 0.0;
    float D_FHor = 0.0;
    float D_BVer = 0.0;
    float D_BHor = 0.0;
    float s_FVer = 0.0;//原始图像数据的垂直差分总和 对应论文中的 s_Fver
    float s_FHor = 0.0;//原始图像数据的水平差分总和 对应论文中的 s_Fhor
    float s_Vver = 0.0;//模糊图像数据的垂直差分总和 s_Vver
    float s_VHor = 0.0;//模糊图像数据的水平差分总和 s_VHor
    for (int i = 1; i < height; ++i)
    {
        for (int j = 1; j < width; ++j)
        {
            D_Fver = abs((float)data[i * width + j] - (float)data[(i - 1) * width + j]);
            s_FVer += D_Fver;
            D_BVer = abs((float)BVer[i * width + j] - (float)BVer[(i - 1) * width + j]);
            s_Vver += std::max((float)0.0, D_Fver - D_BVer);

            D_FHor = abs((float)data[i * width + j] - (float)data[i * width + (j - 1)]);
            s_FHor += D_FHor;
            D_BHor = abs((float)BHor[i * width + j] - (float)BHor[i * width + (j - 1)]);
            s_VHor += std::max((float)0.0, D_FHor - D_BHor);
        }
    }
    float b_FVer = (s_FVer - s_Vver) / s_FVer;
    float b_FHor = (s_FHor - s_VHor) / s_FHor;
    blur_val = std::max(b_FVer, b_FHor);

    delete[] BVer;
    delete[] BHor;

    return blur_val;
}

float grid_max_reblur(const unsigned char *img,int img_height,int img_width, int rows, int cols)
{
    int row_height = img_height / rows;
    int col_width = img_width / cols;
    float blur_val = 0;
    /// change to avg
    for (int y = 0; y < rows; ++y)
    {
        for (int x = 0; x < cols; ++x)
        {    int                            face_size   = col_width * row_height*1;
            unsigned char* grid = new unsigned char[face_size];
            if (NULL == grid) return 0.;
            bstutils::CropImageC1(img, img_width, img_height, grid, x * col_width, y * row_height, col_width, row_height);
            auto this_grad_blur_val = reblur(grid, col_width, row_height);
            delete []grid;
            blur_val +=this_grad_blur_val;
        }
    }
    return blur_val/(rows*cols);
}
float clarity_estimate(unsigned char* grayImg, int width, int height) {
    // LOGD("========== Check Blurriness ==========\n");
    // (BST) Variance of Laplacian
    //    unsigned char* grayImg = new unsigned char[width * height];
    //    CvtRGBToGray(rgbImg, width, height, grayImg);

    // float blur_val = ReBlur(src_data.data(), src_data.width(), src_data.height());
    float blur_val = grid_max_reblur(grayImg,height,width, 2, 2);
    float clarity = blur_val;

    float T1 = 0.0f;
    float T2 = 1.0f;
    if (clarity <= T1)
    {
        clarity = 0.0;
    }
    else if (clarity >= T2)
    {
        clarity = 1.0;
    }
    else
    {
        clarity = (clarity - T1) / (T2 - T1);
    }

    return (clarity);
}


int getRectBrightnessLevel(unsigned char* data, int w, int h,int c, int bbox_x, int bbox_y, int bbox_w, int bbox_h) {
    if (c!= 3){
        return -1;
    }

    // crop image
    int                            face_size   = bbox_w * bbox_h * c;
    unsigned char * cropped_img = new unsigned char[face_size];
    if (NULL == cropped_img) return 0;

    CropImageC3_YH(data, w, h, bbox_y, h - (bbox_y + bbox_h), bbox_x, w - (bbox_x + bbox_w), cropped_img);



    double red_channel_mean = 0.0, green_channel_mean = 0.0, blue_channel_mean = 0.0;
    red_channel_mean = GetGrayAverage(cropped_img, bbox_w, bbox_h);
    green_channel_mean = GetGrayAverage(cropped_img + bbox_w * bbox_h, bbox_w, bbox_h);
    blue_channel_mean = GetGrayAverage(cropped_img + bbox_w * bbox_h * 2, bbox_w, bbox_h);

    double image_bright_value = sqrt(0.299 * pow(red_channel_mean, 2)
                                     + 0.587 * pow(green_channel_mean, 2)
                                     + 0.114 * pow(blue_channel_mean, 2));

    int image_bright_level = static_cast<int>(round(image_bright_value));
    delete[] cropped_img;
    return image_bright_level;
}

bool ModuleQualityAssessor::check(unsigned char* img, int w, int h, int c, const BBox& face, bool check_bg_light,bool check_clarity, QualityOutput& quality_output) {
    //check input
    if (img == NULL || w <= 0 || h <= 0 || (c != 1 && c != 3)) {
        LOGE("input image data error, img=%p,w=%d,h=%d,c=%d", (void*)img, w, h, c);
        return false;
    }


    // crop image
    int                            face_size   = face.w * face.h * c;
    unsigned char* cropped_img = new unsigned char[face_size];
    if (NULL == cropped_img) return false;
    if (c == 3) {
        CropImageC3_YH(img, w, h, face.y, h - (face.y + face.h), face.x, w - (face.x + face.w), cropped_img);
        // CropImageC3(img, w, h, cropped_img, face.x, face.y, face.w, face.h);
        // CropImageC3_YH(img, w, h, bg_box.y, h - (bg_box.y + bg_box.h), bg_box.x, w - (bg_box.x + bg_box.w), bg_cropped_img);
    } else if (c == 1) {
        bstutils::CropImageC1(img, w, h, cropped_img, face.x, face.y, face.w, face.h);
    }

    // to gray
    unsigned char* gray;
    if (c != 1) {
        int gray_face_size = face.w * face.h;
        gray               = new unsigned char[gray_face_size];
        if (NULL == gray) return false;
        bstutils::BGR2Gray(cropped_img, face.w, face.h, gray);
    } else {
        gray = cropped_img;
    }

    if (check_clarity) {

        float blurriness          = clarity_estimate(gray, face.w, face.h);
        quality_output.blurriness = blurriness;
    }else{
        quality_output.blurriness = -1;
    }

    if (param_.check_light) {
        //        float brightness          = getBrightness(cropped_img, face.w, face.h);
        //        quality_output.brightness = brightness;

        int brightness          = 0;
        if (face.w > 0 && face.h > 0){
            brightness = getRectBrightnessLevel(img, w, h,c, face.x, face.y, face.w, face.h);
        }

        quality_output.face_brightness = float(brightness);
        if(check_bg_light){
            int bg_left_brightness = 0;
            if (face.x > 0 && face.y > 0){
                bg_left_brightness = getRectBrightnessLevel(img, w, h,c, 0,0, face.x, face.y);

            }


            int bg_right_brightness = 0;
            if(face.x+w < w && face.y > 0){
                bg_right_brightness =         getRectBrightnessLevel(img, w, h,c,face.x+w, 0,w-(face.w+face.x), face.y);
            }

            int bg_brightness = floor(bg_left_brightness + bg_right_brightness) / 2;
            quality_output.bg_brightness = float(bg_brightness);
        }else{
            quality_output.bg_brightness = -1;
        }



    }

    if (cropped_img)  delete[] cropped_img;
    if (gray) delete[] gray;
    return true;
}


float ModuleQualityAssessor::getQualityScore(QualityOutput quality_output, LmkInfo info){
    float brightness_score = (abs(quality_output.face_brightness - 127.5)/255.);
    float blurriness_score = (1-quality_output.blurriness) + (1-info.blur_prob);
    float pose_score = abs(info.poses[0]/90.)+ abs(info.poses[1]/90.);
    float quality_score = 1.0*brightness_score +
                          1.0*blurriness_score +
                          2.0*pose_score;  // yaw,pitch,roll
                                            //    LOGI( "brightness_score:%.2f,blurriness_score:%.2f,pose_score:%.2f",brightness_score,blurriness_score,pose_score);
    return quality_score;
}

void GetYFromRGB(unsigned char* img, int w, int h, unsigned char* y_img) {
    int y_idx = 0;
    for (int i = 0; i < h; ++i) {
        for (int j = 0; j < w; ++j) {
            int B = (img + (i * w + j) * 3)[0];
            int G = (img + (i * w + j) * 3)[1];
            int R = (img + (i * w + j) * 3)[2];

            int Y = 0.299 * R + 0.587 * G + 0.114 * B;

            y_img[y_idx++] = (Y < 0) ? 0 : ((Y > 255) ? 255 : Y);
        }
    }
}

float ModuleQualityAssessor::getBrightness(unsigned char* data, int w, int h) {
    unsigned char* y_img = new unsigned char[w * h];
    if (NULL == y_img) return 0.;
    GetYFromRGB(data, w, h, y_img);

    float avg = GetGrayAverage(y_img, w, h);
    delete[] y_img;
    return avg;
}
