
#include "CommonDef.h"
#include "CommonUtils.h"
#include "model/ModuleAlign.h"


void faceAlign(unsigned char* img, int w, int h, int c, unsigned char* dst_img,int target_width,int target_height,float *points_src) {
    // const float points_from[4] = {w / 8.f, h / 8.f, w / 8.f + 1.f, h / 8.f + 3.f};
    float points_to[5 * 2] = {38.2946, 51.6963, 73.5318, 51.5014, 56.0252,
                                    71.7366, 41.5493, 92.3655, 70.7299, 92.2041};
    float ratio = target_width/112.;
    for (int i = 0; i < 5*2; ++i) {
        points_to[i] = points_to[i]*ratio;
    }
    float tm[6];
    float tm_inv[6];
    ncnn::get_affine_transform(points_src, points_to, 5, tm);
    ncnn::invert_affine_transform(tm, tm_inv);
    // for (int i = 0; i < 5; ++i) {
    //     bstutils::DrawPoint((uint8_t*)img, w, h, 3, points_src[i * 2 + 0], points_src[i * 2 + 1], 2, 255 - 255 / i, 255 - 255 / i, 255 / i);
    //     bstutils::DrawPoint((uint8_t*)img, w, h, 3, points_to[i * 2 + 0], points_to[i * 2 + 1], 2, 255 - 255 / i, 255 - 255 / i, 255 / i);
    // }
//    bstutils::save_image_png("faceAlign01", img, w, h, 3);
    ncnn::warpaffine_bilinear_c3(img, w, h, dst_img, target_width, target_height, tm_inv, 0);
//    bstutils::save_image_png("faceAlign02", dst_img, target_width, target_height, 3);
    // printf("target_width: %d, %d\n", target_width, target_height);
}

BBox CalculateBox(Bbox &box, int w, int h, float config_shift_x,float config_shift_y,float config_scale) {
    int x = static_cast<int>(box.x1);
    int y = static_cast<int>(box.y1);
    int box_width = static_cast<int>(box.x2 - box.x1 + 1);
    int box_height = static_cast<int>(box.y2 - box.y1 + 1);

    int shift_x = static_cast<int>(box_width * config_shift_x);
    int shift_y = static_cast<int>(box_height * config_shift_y);

    float scale = std::min(
        config_scale,
        std::min((w - 1) / (float) box_width, (h - 1) / (float) box_height)
    );

    int box_center_x = box_width / 2 + x;
    int box_center_y = box_height / 2 + y;

    int new_width = static_cast<int>(box_width * scale);
    int new_height = static_cast<int>(box_height * scale);

    int new_side_len_half = std::min(new_width, new_height) / 2 ;
    new_side_len_half = std::min(new_side_len_half, box_center_x);
    new_side_len_half = std::min(new_side_len_half, box_center_y);
    new_side_len_half = std::min(new_side_len_half, w - box_center_x);
    new_side_len_half = std::min(new_side_len_half, h - box_center_y);


    int left_top_x = box_center_x - new_side_len_half + shift_x;
    int left_top_y = box_center_y - new_side_len_half + shift_y;
    int right_bottom_x = box_center_x + new_side_len_half + shift_x;
    int right_bottom_y = box_center_y + new_side_len_half + shift_y;

    if (left_top_x < 0) {
        left_top_x = 0;
    }

    if (left_top_y < 0) {
        left_top_y = 0;
    }

    if (right_bottom_x >= w) {
        right_bottom_x = w -1;
    }

    if (right_bottom_y >= h) {
        right_bottom_y = h - 1;
    }
    BBox outRect = {left_top_x, left_top_y, right_bottom_x - left_top_x + 1, right_bottom_y - left_top_y + 1,0};

    return outRect;
}



bool faceAlign(unsigned char* img, int w, int h, int c, unsigned char* dst_img,int target_width,int target_height, Bbox cropBox) {

    faceAlign(img, w,h,3,dst_img,target_width,target_height, cropBox.ppoint);

    return true;
}
