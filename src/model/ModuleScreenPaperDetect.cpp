#include "model/ModuleScreenPaperDetect.h"
#include "CommonUtils.h"
#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif


bool boxInsideBox(const Object& smallObject, const Object& bigObject) {

    Point left_top;
    left_top.x = smallObject.x1;
    left_top.y = smallObject.y1;
    Point right_top;
    right_top.x = smallObject.x2;
    right_top.y = smallObject.y1;
    Point left_bottom;
    left_bottom.x = smallObject.x1;
    left_bottom.y = smallObject.y2;
    Point right_bottom;
    right_bottom.x = smallObject.x2;
    right_bottom.y = smallObject.y2;
    bool  left_top_in = bstutils::pointInsideBox(left_top,bigObject);
    bool  right_top_in =    bstutils::pointInsideBox(right_top,bigObject);
    bool  left_bottom_in =    bstutils::pointInsideBox(left_bottom,bigObject);
    bool  right_bottom_in =bstutils::pointInsideBox(right_bottom,bigObject);
    if (left_top_in &&
        right_top_in &&
        left_bottom_in &&
        right_bottom_in){
        return true;
    }else{
        return false;
    }

}


static void qsort_descent_inplace(std::vector<Object>& faceobjects, int left, int right)
{
    int i = left;
    int j = right;
    float p = faceobjects[(left + right) / 2].score;

    while (i <= j)
    {
        while (faceobjects[i].score > p)
            i++;

        while (faceobjects[j].score < p)
            j--;

        if (i <= j)
        {
            // swap
            std::swap(faceobjects[i], faceobjects[j]);

            i++;
            j--;
        }
    }

#pragma omp parallel sections
    {
#pragma omp section
        {
            if (left < j) qsort_descent_inplace(faceobjects, left, j);
        }
#pragma omp section
        {
            if (i < right) qsort_descent_inplace(faceobjects, i, right);
        }
    }
}

static void qsort_descent_inplace(std::vector<Object>& objects)
{
    if (objects.empty())
        return;

    qsort_descent_inplace(objects, 0, objects.size() - 1);
}




int ModuleScreenPaperDetect::init() {
    LOGI("ModuleScreenPaperDetect init");
    int ret = 0;
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    m_net.load_param(models_ncnn.paper_screen_detect_param_bin);
    m_net.load_model(models_ncnn.paper_screen_detect_bin);
#else
    ret = m_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.param");
    if (ret != 0) {
        return ret;
    }
    ret = m_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.bin");
#endif

    return ret;
}

void ModuleScreenPaperDetect::preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* dst, int out_w, int out_h,
                              std::vector<float>& pad_info) {
    float          scale       = std::min((float)out_w / in_w, (float)out_h / in_h);
    int            mid_w       = static_cast<int>(in_w * scale);
    int            mid_h       = static_cast<int>(in_h * scale);
    unsigned char* dst_resized = new unsigned char[mid_w * mid_h * 3];
    if (NULL == dst_resized) return;
    ncnn::resize_bilinear_c3(src, in_w, in_h, dst_resized, mid_w, mid_h);

    int top  = (static_cast<int>(out_h) - mid_h) / 2;
    int left = (static_cast<int>(out_w) - mid_w) / 2;
    //    memset(dst, 114. / 255., sizeof(float) * out_w * out_h * 3); /// /255.

    //    memset(dst, 114. / 255. , sizeof(float) * out_w * out_h * 3); /// /255.

    bstutils::norm_and_zeropadding(dst_resized, dst, mid_w, mid_h, out_w, out_h);

    pad_info.resize(3);
    pad_info[0] = static_cast<float>(left);
    pad_info[1] = static_cast<float>(top);
    pad_info[2] = scale;
    delete[] dst_resized;

    return;
}

static inline float clampf(float d, float min, float max) {
    const float t = d < min ? min : d;
    return t > max ? max : t;
}
static void parse_yolo11_detections(
        float *inputs, float confidence_threshold,
        int num_channels, int num_anchors, int num_class,
        int infer_img_width, int infer_img_height,
        std::vector<Object> &objects) {


    for (int i = 0; i < num_anchors; i++) {
        const float *row_ptr = inputs + i*( 4+ num_class) ;
        const float *bboxes_ptr = row_ptr;
        const float *scores_ptr = row_ptr + 4;
        float score = - 1.0f;
        int class_id = 0;
        for (int j = 0; j < num_class; j++) {
            if (scores_ptr[j] > score) {
                score = scores_ptr[j];
                class_id = j;
            }
        }
        if (score > confidence_threshold) {
            float x = *bboxes_ptr++;
            float y = *bboxes_ptr++;
            float w = *bboxes_ptr++;
            float h = *bboxes_ptr;

            float x0 = clampf((x - 0.5f * w), 0.f, (float) infer_img_width);
            float y0 = clampf((y - 0.5f * h), 0.f, (float) infer_img_height);
            float x1 = clampf((x + 0.5f * w), 0.f, (float) infer_img_width);
            float y1 = clampf((y + 0.5f * h), 0.f, (float) infer_img_height);
            Object obj;
            obj.x1 = x0;
            obj.y1 = y0;
            obj.x2 = x1;
            obj.y2 = y1;

            obj.score = score;
            obj.class_id =  class_id;

            objects.push_back(obj);

        }
    }

}
bool ModuleScreenPaperDetect::perfom(const unsigned char* const img, int w, int h, int c, std::vector<Object>& objects,Bbox face, bool isEnroll, float min_face) {

    objects.clear();

    std::vector<unsigned char> ncnn_input(m_param.net_input_h * m_param.net_input_w * 3, 114);
    std::vector<float>         pad_info;

    preprocess(img, w, h, ncnn_input.data(), m_param.net_input_w, m_param.net_input_h, pad_info);
    ncnn::Mat net_in = ncnn::Mat::from_pixels(ncnn_input.data(),
                                              ncnn::Mat::PIXEL_RGB, m_param.net_input_w, m_param.net_input_h);
// #define DUMP_DETECT
#ifdef DUMP_DETECT

   //    bstutils::pretty_print(net_in);
//    bstutils::save_image_png("input", (unsigned char*)img,w,h,c);
    bstutils::save_image_png("PIXEL_RGB", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(0, scale);

    ncnn::Extractor     ex = m_net.create_extractor();
    ex.set_light_mode(true);
    ex.input(0, net_in);

    std::vector<Object> proposals;

    {
        ncnn::Mat out;
        ex.extract(219, out);
        const int num_labels = out.w - 4; //修改类数
        if(isEnroll){

            parse_yolo11_detections(
                        (float *) out.data, m_param.conf_enroll_thd,
                        out.w, out.h, num_labels,
                        m_param.net_input_w, m_param.net_input_h,
                        proposals);
        }else{
            parse_yolo11_detections(
                                    (float *) out.data, m_param.conf_auth_thd,
                                    out.w, out.h, num_labels,
                                    m_param.net_input_w, m_param.net_input_h,
                                    proposals);

        }


    }

    // sort all proposals by score from highest to lowest
    qsort_descent_inplace(proposals);

    // apply nms with nms_threshold

    bstutils::nms(proposals, m_param.iou_thd);

    for (int i = 0; i < (int)proposals.size(); i++) {
        // adjust offset to original unpadded
        proposals[i].x1 = (proposals[i].x1 - pad_info[0]) / pad_info[2];
        proposals[i].y1 = (proposals[i].y1 - pad_info[1]) / pad_info[2];
        proposals[i].x2 = (proposals[i].x2 - pad_info[0]) / pad_info[2];
        proposals[i].y2 = (proposals[i].y2 - pad_info[1]) / pad_info[2];



        // clip 非常重要，w,h不能 -1
        proposals[i].x1 = std::max(std::min(proposals[i].x1, (float)(w)), 0.f);
        proposals[i].y1 = std::max(std::min(proposals[i].y1, (float)(h)), 0.f);
        proposals[i].x2 = std::max(std::min(proposals[i].x2, (float)(w)), 0.f);
        proposals[i].y2 = std::max(std::min(proposals[i].y2, (float)(h)), 0.f);
        bstutils::expand_box(proposals[i],proposals[i],1.05);
        float face_width = (proposals[i].x2 - proposals[i].x1);
        float face_height = (proposals[i].y2 - proposals[i].y1);
        if (face_width < w * min_face){
            continue;
        }
        if (face_height < h * min_face){
            continue;
        }

#ifdef DUMP_DETECT
#define DUMP_DETECT
        bstutils::DrawRect((uint8_t*)img, w, h, 3, face.x1, face.y1, face.x2, face.y2, 1, 0, 255, 0);
        bstutils::DrawRect((uint8_t*)img, w, h, 3, proposals[i].x1, proposals[i].y1, proposals[i].x2, proposals[i].y2, 1, 0, 255, 0);
#endif
        Object small_obj;
        small_obj.x1 = face.x1;
        small_obj.y1 = face.y1;
        small_obj.x2 = face.x2;
        small_obj.y2 = face.y2;
        if (!boxInsideBox(small_obj,proposals[i])){
            continue;
        }

       objects.push_back(proposals[i]);
    }

#ifdef DUMP_DETECT
    bstutils::save_image_png("detectImage", (uint8_t*)img, w, h, 3);
#endif
    return true;
}

int ModuleScreenPaperDetect::release() {
    m_net.clear();
    return 0;
}

