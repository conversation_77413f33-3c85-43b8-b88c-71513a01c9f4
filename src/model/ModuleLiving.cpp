/**  
 * All rights Reserved, Designed By MI
 * @projectName bstFaceUnlock
 * @title     ModuleLiving   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2023/12/10 上午1:41  
 * @version V0.0.0
 * @copyright 2023 <EMAIL>
 */
//
#include "model/ModuleLiving.h"
#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif
#include "Version.h"

ModuleLiving::ModuleLiving() {
}

int ModuleLiving::init(LivingMode mode) {
    int ret = 0;
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    mode_ = mode;
    if (mode == dark_mode){
        m_net.load_param(models_ncnn.dark_living_param_bin);
        m_net.load_model(models_ncnn.dark_living_bin);
    }else if ( mode == normal_mode){
        m_net.load_param(models_ncnn.normal_living_param_bin);
        m_net.load_model(models_ncnn.normal_living_bin);
    }else if ( mode == big_angel_mode){
        m_net.load_param(models_ncnn.big_angel_living_param_bin);
        m_net.load_model(models_ncnn.big_angel_living_bin);
    }


#else // TODO: add param
    ret = m_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/living/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.param");
    ret = m_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/living/step_248000_eer_weak+kaggleMaskReal0.8714_weak0.9397_hard0.7091_eval_haibo0.9465_multiframe_3frame_v20.9128_ce_camera0.8829acc_0.871392_sim.bin");

#endif
    return ret;
}

ModuleLiving::~ModuleLiving() {
}

int ModuleLiving::release() {
    m_net.clear();
    return 0;
}

int ModuleLiving::perfom(unsigned char* img, int img_w, int img_h, int img_c, BBox& box, float& liv_predicted_score, LmkInfo lmk_info,float &living_threshold) {
    int            out_w     = img_w;
    int            out_h     = img_h;
    unsigned char* input_ptr = nullptr;

    input_ptr = (uint8_t*)img;

    //extend face bbox
#define EXPAND_FACE
#ifdef EXPAND_FACE
    int extend_x, extend_y, extend_w, extend_h;
    int cx = box.x + (box.w) * 0.5;
    int cy = box.y + (box.h)* 0.5;
    extend_w = box.w;
    extend_h =  box.h;

    float max_wd_ht = std::max((box.w), (box.h));
    float ratio     = 1.0;

    if (mode_ == big_angel_mode) { /// pitch大角度
        ratio = 1.05;
    }
#if defined(M197)
    else if (mode_ == dark_mode){
        ratio = 1.0;
    }
    else{
        ratio = 1.0;
    }
#elif defined(M196)
    else if (mode_ == dark_mode){
        ratio = 1.0;
    }
    else{
        ratio = 1.0;
    }
#endif

    extend_w *= ratio;
    extend_h *= ratio;
    // Calculate expanded side lengths
    int expand_side_w = (int)(extend_w * ratio);
    int expand_side_h = (int)(extend_h * ratio);

    // Calculate cut coordinates

    extend_x = (int)(cx - expand_side_w / 2.0f);
    extend_y = (int)(cy - expand_side_h / 2.0f);


    int extend_x3 = (int)(cx + expand_side_w / 2.0f);
    int extend_y3 = (int)(cy + expand_side_h / 2.0f);
    extend_x3 = std::min(extend_x3, img_w - 1);
    extend_y3 = std::min(extend_y3, img_h - 1);
    extend_w = extend_x3 - extend_x;
    extend_h = extend_y3 - extend_y;
    // out of bound check
    if (extend_x < 0)
        extend_x = 0;
    if (extend_y < 0)
        extend_y = 0;
    if (extend_x + extend_w >= img_w)
        extend_w = img_w - extend_x - 1;
    if (extend_y + extend_h >= img_h)
        extend_h = img_h - extend_y - 1;

    // crop extend BBox image
    int x          = extend_x;
    int y          = extend_y;
    int box_width  = extend_w;
    int box_height = extend_h;
#else
    int x          = box.x;
    int y          = box.y;
    int box_width  = box.w;
    int box_height = box.h;
#endif
    /// 加入人脸框的限制
    if (x < 0) {
        x = 0;
    } else if (x > img_w) {
        x = img_w;
    }

    if (y < 0) {
        y = 0;
    } else if (y > img_h) {
        y = img_h;
    }

    if (x + box_width > img_w) {
        box_width = img_w - x;
        if (box_width <= 0) {
            return -1;
        }
    }
    if (y + box_height > img_h) {
        box_height = img_h - y;
        if (box_height <= 0) {
            return -1;
        }
    }

    ncnn::Mat net_in = ncnn::Mat::from_pixels_roi_resize((unsigned char*)img, ncnn::Mat::PIXEL_RGB, img_w, img_h, x, y, box_width, box_height, net_in_w, net_in_h);
// #define DUMP_LIVING
#ifdef DUMP_LIVING
    bstutils::save_image_png("bst_net_in", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(this->mean, this->scale);

    ncnn::Extractor ex = m_net.create_extractor();
    ex.set_light_mode(true);
    ex.input(0, net_in);

    ncnn::Mat output_mat;
#if defined(M197)
    if (mode_ == dark_mode) {
        ex.extract(220, output_mat);
    }else{
        ex.extract(221, output_mat);
    }
#elif defined(M196)
    if (mode_ == dark_mode) {
        ex.extract(173, output_mat);
    }else{
        ex.extract(173, output_mat);
    }
#endif
    float* output = (float*)output_mat.data;
    //    liv_predicted_score = sigmoid(output[1]); /// real confidence
    bstutils::softmax(output, 2);
    liv_predicted_score = output[1]; /// real confidence

    return 0;
}