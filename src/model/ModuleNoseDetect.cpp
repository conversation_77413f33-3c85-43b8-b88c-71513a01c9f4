//
// Created by shining on 24-4-18.
//
#include "model/ModuleNoseDetector.h"
#include "CommonUtils.h"
#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif


#define  NUM_KEYPOINTS 0




void ModuleNose::decode(float* data_ptr,  int fea_h, int fea_w,int feat_c, const int* const anchor, std::vector<Object>& prebox, float threshold, int stride) {

    int spacial_size = fea_w * fea_h;
    float zero_point=0;
    float scale=1.0;
    size_t anchor_size = 3;
    //    int channels     = feat_c;///NUM_KEYPOINTS * 3 + 6;
    const int num_class = feat_c / (anchor_size) - 5;
    int feat_offset = num_class + 5;
    for (int c = 0; c < (int)anchor_size; c++) {
        float anchor_w = float(anchor[c * 2 + 0]);
        float anchor_h = float(anchor[c * 2 + 1]);
        float*    ptr_x    = data_ptr + spacial_size * (c * feat_offset + 0);
        float*    ptr_y    = data_ptr + spacial_size * (c * feat_offset + 1);
        float*    ptr_w    = data_ptr + spacial_size * (c * feat_offset + 2);
        float*    ptr_h    = data_ptr + spacial_size * (c * feat_offset + 3);
        float*    ptr_s    = data_ptr + spacial_size * (c * feat_offset + 4); /// box score

        for (int i = 0; i < fea_h; i++) {
            for (int j = 0; j < fea_w; j++) {

                int class_index = 0;
                float class_score = -100.;
                int index = i * fea_w + j;
                for(int k=0; k<num_class; k++)
                {
                    float score = (data_ptr[spacial_size * (c * feat_offset + 5 + k ) + index]-zero_point)* scale;
                    if(score>class_score)
                    {
                        class_score = score;
                        class_index = k;
                    }
                }
                float box_score =  (ptr_s[index]-zero_point)* scale;
                float confidence = bstutils::sigmoid(box_score)*bstutils::sigmoid(class_score);
                // std::cout<<"conf:"<<confidence<<std::endl;
                // std::cout<<"pro:"<<prob_threshold<<std::endl;
                if(confidence>=threshold)
                {

                    float  dx = bstutils::sigmoid((ptr_x[index] - zero_point) * scale);
                    float  dy = bstutils::sigmoid((ptr_y[index] - zero_point) * scale);
                    float  dw = bstutils::sigmoid((ptr_w[index] - zero_point) * scale);
                    float  dh = bstutils::sigmoid((ptr_h[index] - zero_point) * scale);


                    float pb_cx = (dx * 2.f - 0.5f + j) * stride;
                    float pb_cy = (dy * 2.f - 0.5f + i) * stride;

                    float pb_w = pow(dw * 2.f, 2) * anchor_w;
                    float pb_h = pow(dh * 2.f, 2) * anchor_h;
                    Object temp_box;
                    temp_box.score = confidence;
                    temp_box.x1    = pb_cx - pb_w * 0.5f;
                    temp_box.y1    = pb_cy - pb_h * 0.5f;
                    temp_box.x2    = pb_cx + pb_w * 0.5f;
                    temp_box.y2    = pb_cy + pb_h * 0.5f;
                    temp_box.class_id = class_index; /// ['face',"normal_glasses","dark_glasses","close_eye","open_eye"]

                    prebox.push_back(temp_box);
                }
            }
        }
    }
}



void ModuleNose::decode(const ncnn::Mat& data, const int* const anchors, std::vector<Object>& prebox, float threshold, int stride) {

    const int num_grid_x = data.w;
    const int num_grid_y = data.h;

    const int num_anchors = 3;

    const int num_class = data.c / num_anchors - 5;

    const int feat_offset = num_class + 5;

    for (int q = 0; q < num_anchors; q++) {
        const float anchor_w = anchors[q * 2];
        const float anchor_h = anchors[q * 2 + 1];

        for (int i = 0; i < num_grid_y; i++) {
            for (int j = 0; j < num_grid_x; j++) {
                // find class index with max class score
                int class_index = 0;
                float class_score = -200;
                for (int k = 0; k < num_class; k++) {
                    float score = data.channel(q * feat_offset + 5 + k).row(i)[j];
                    if (score > class_score) {
                        class_index = k;
                        class_score = score;
                    }
                }

                float box_score = data.channel(q * feat_offset + 4).row(i)[j];

                float confidence = bstutils::sigmoid(box_score) * bstutils::sigmoid(class_score);

                if (confidence >= threshold) { /// && class_index == 1
                    // yolov5/models/yolo.py Detect forward
                    // y = x[i].sigmoid()
                    // y[..., 0:2] = (y[..., 0:2] * 2. - 0.5 + self.grid[i].to(x[i].device)) * self.stride[i]  # xy
                    // y[..., 2:4] = (y[..., 2:4] * 2) ** 2 * self.anchor_grid[i]  # wh

                    float dx = bstutils::sigmoid(data.channel(q * feat_offset + 0).row(i)[j]);
                    float dy = bstutils::sigmoid(data.channel(q * feat_offset + 1).row(i)[j]);
                    float dw = bstutils::sigmoid(data.channel(q * feat_offset + 2).row(i)[j]);
                    float dh = bstutils::sigmoid(data.channel(q * feat_offset + 3).row(i)[j]);

                    float pb_cx = (dx * 2.f - 0.5f + j) * stride;
                    float pb_cy = (dy * 2.f - 0.5f + i) * stride;

                    float pb_w = pow(dw * 2.f, 2) * anchor_w;
                    float pb_h = pow(dh * 2.f, 2) * anchor_h;

                    float x0 = pb_cx - pb_w * 0.5f;
                    float y0 = pb_cy - pb_h * 0.5f;
                    float x1 = pb_cx + pb_w * 0.5f;
                    float y1 = pb_cy + pb_h * 0.5f;

                    Object obj;
                    obj.x1 = x0;
                    obj.y1 = y0;
                    obj.x2 = x1;
                    obj.y2 = y1;
                    obj.class_id = class_index;
                    obj.score = confidence;

                    prebox.push_back(obj);
                }
            }
        }
    }
}

int ModuleNose::init() {
    LOGI("ModuleNose init");
    int ret = 0;
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    m_net.load_param(models_ncnn.nose_param_bin);
    m_net.load_model(models_ncnn.nose_bin);
#else
    ret = m_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.param");
    if (ret != 0) {
        return ret;
    }
    ret = m_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/detector/face_detection_yolo_v0.6.bin");
#endif
    return ret;
}

bool ModuleNose::preprocess(const unsigned char* const src, int in_w, int in_h, unsigned char* dst, int out_w, int out_h,float face_brightness,
                             std::vector<float>& pad_info) {
    float          scale       = std::min((float)out_w / in_w, (float)out_h / in_h);
    int            mid_w       = static_cast<int>(in_w * scale);
    int            mid_h       = static_cast<int>(in_h * scale);
    unsigned char* dst_resized = new unsigned char[mid_w * mid_h * 3];
    if (NULL == dst_resized) return false;
    ncnn::resize_bilinear_c3(src, in_w, in_h, dst_resized, mid_w, mid_h);

    int top  = (static_cast<int>(out_h) - mid_h) / 2;
    int left = (static_cast<int>(out_w) - mid_w) / 2;
    //    memset(dst, 114. / 255., sizeof(float) * out_w * out_h * 3); /// /255.

//    memset(dst, 114. / 255. , sizeof(float) * out_w * out_h * 3); /// /255.

#ifdef DUMP_DETECT
#define DUMP_DETECT
    bstutils::save_image_png("crop_src_before", dst_resized, mid_w, mid_h,3);
    //

#endif
    // #ifndef SECUREOS
    if(face_brightness < 60){
        bstutils::ColorHistogramEqualization(dst_resized,mid_h, mid_w);
    }
    // #endif
    bstutils::norm_and_zeropadding(dst_resized, dst, mid_w, mid_h, out_w, out_h);
#ifdef DUMP_DETECT
#define DUMP_DETECT
    bstutils::save_image_png("crop_src_after",  dst_resized, mid_w, mid_h,3);
#endif

    pad_info.resize(3);
    pad_info[0] = static_cast<float>(left);
    pad_info[1] = static_cast<float>(top);
    pad_info[2] = scale;
    delete[] dst_resized;

    return true;
}



bool ModuleNose::perfom(const unsigned char* const img, int img_w, int img_h, int c, BBox& bbox, float score_TH,float face_brightness, LmkInfo& info) {
    std::vector<unsigned char> ncnn_input(m_param.net_input_w * m_param.net_input_h * 3, 114);
    std::vector<float>         pad_info;
    float points_to[5 * 2] = {38.2946, 51.6963, 73.5318, 51.5014, 56.0252,
                                                   71.7366, 41.5493, 92.3655, 70.7299, 92.2041};

    Point nose;
    nose.x = 56.0252;
    nose.y =71.7366;

    if (!preprocess(img, img_w, img_h, ncnn_input.data(), m_param.net_input_w, m_param.net_input_h,face_brightness, pad_info)) return false;



    ncnn::Mat net_in = ncnn::Mat::from_pixels(ncnn_input.data(),
                                              ncnn::Mat::PIXEL_RGB, m_param.net_input_w, m_param.net_input_h);
//#define DUMP_DETECT
#ifdef DUMP_DETECT

    //    bstutils::pretty_print(net_in);

    bstutils::save_image_png("Nose_input", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(0, scale);

    std::vector<Object> tmp_objects;
    ncnn::Extractor     ex = m_net.create_extractor();
    ex.set_light_mode(true);
    ex.input(0, net_in);

    // stride 8
    {
        ncnn::Mat out;
        ex.extract(79, out);
        //        decode((float *)out.data,out.h,out.w,out.c,m_anchor0,tmp_objects,m_param.conf_thd,8);
        decode(out, m_anchor0, tmp_objects,m_param.conf_thd, 8);
    }
    // stride 16
    {
        ncnn::Mat out;
        ex.extract(80, out);
        //        decode((float *)out.data,out.h,out.w,out.c,m_anchor1,tmp_objects,m_param.conf_thd,16);
        decode(out, m_anchor1, tmp_objects,m_param.conf_thd, 16);
    }
    // stride 32
    /*
    {
        ncnn::Mat out;
        ex.extract(face_detection_yolo_v0_6_param_id::BLOB_stride_32, out);
        decode(out, m_anchor2, tmp_objects, 32);
    }
    */
    bstutils::bubble_sort(tmp_objects.begin(), tmp_objects.end(), bstutils::cmp);
    bstutils::nms(tmp_objects,m_param.iou_thd);
    int nose_status = 0;

    /// ['face',"normal_glasses","dark_glasses","close_eye","open_eye"]
    for (int i = 0; i < (int)tmp_objects.size(); i++) {
        tmp_objects[i].x1 = (tmp_objects[i].x1 - pad_info[0]) / pad_info[2] ;
        tmp_objects[i].y1 = (tmp_objects[i].y1 - pad_info[1]) / pad_info[2] ;
        tmp_objects[i].x2 = (tmp_objects[i].x2 - pad_info[0]) / pad_info[2] ;
        tmp_objects[i].y2 = (tmp_objects[i].y2 - pad_info[1]) / pad_info[2] ;

        // nose
        if(tmp_objects[i].class_id == 0 && tmp_objects[i].score > score_TH) //

        {
#ifdef DUMP_DETECT
            bstutils::DrawPoint((uint8_t*)img,  img_w, img_h, 3, nose.x, nose.y, 2, 255, 0, 0);
            bstutils::DrawRect((uint8_t*)img, img_w, img_h, 3, tmp_objects[i].x1,tmp_objects[i].y1, tmp_objects[i].x2, tmp_objects[i].y2, 1, 0, 255, 0);
#endif


            if(bstutils::pointInsideBox(nose,tmp_objects[i]))
            {
                nose_status=1;
            }
        }
    }
#ifdef DUMP_DETECT
    bstutils::save_image_png("nose_result", (unsigned char*)img, img_w, img_h, 3);

#endif
 ///&& close_eyes_count < 2
    if((nose_status == 1 ) ){
        info.nose_status = 1;
    }else{
        info.nose_status = 0;
    }

    return true;
}

int ModuleNose::release() {
    m_net.clear();
    return 0;
}

