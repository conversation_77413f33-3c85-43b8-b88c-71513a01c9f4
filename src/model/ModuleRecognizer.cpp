//
// Created by shining on 2023/12/14.
//

#include "model/ModuleRecognizer.h"

#include <math.h>
#ifndef USING_MODEL_FILE
#include "model/Models.h"
#endif

using namespace std;

ModuleRecognizer::~ModuleRecognizer() {
    release();
}

int ModuleRecognizer::init() {
    int ret = 0;
    LOGI("ModuleRecognizer init");
#ifndef USING_MODEL_FILE
    // TODO: add param
    Models models_ncnn;
    m_net.load_param(models_ncnn.recognizer_param_bin);
    m_net.load_model(models_ncnn.recognizer_bin);
#else // TODO: add param
    ret = m_net.load_param("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/recognizer/backbone_epoch_24_loss_7.0121.param");
    ret = m_net.load_model("/Users/<USER>/BstProject/face_unlock_shininggitee/include/model/recognizer/backbone_epoch_24_loss_7.0121.bin");

#endif
    return 0;
}

int ModuleRecognizer::release() {
    m_net.clear();
    return 0;
}

void ModuleRecognizer::normalize(std::vector<float>& feats) {
    sum = 0;
    for (unsigned int i = 0; i < feats.size(); i++) {
        sum += feats[i] * feats[i];
    }
    sum = sqrt(sum);
    for (unsigned int i = 0; i < feats.size(); i++) {
        feats[i] = feats[i] / sum;
    }
}

bool ModuleRecognizer::perm(unsigned char* img, int img_w, int img_h, int img_c, std::vector<float>& feats) {
    // check forward engine
    // check input
    if (img == NULL || img_w <= 0 || img_h <= 0 || (img_c != 3)) {
        LOGE("ModuleRecognizer input image data error, img=%p,w=%d,h=%d,c=%d", (void*)img, img_w, img_h, img_c);
        return false;
    }
    feats.clear();
    // preprocess
    ncnn::Mat net_in = ncnn::Mat::from_pixels(img,
                                              ncnn::Mat::PIXEL_RGB, img_w, img_h);
#ifdef DUMP_RECOGNIZER
    bstutils::save_image_png("REC_RGB", net_in, ncnn::Mat::PIXEL_RGB);
#endif
    net_in.substract_mean_normalize(mean, scale);

    // forward
    ncnn::Extractor ex = m_net.create_extractor();
    ex.set_light_mode(true);
    ex.input(0, net_in);

    //
    {
        ncnn::Mat out;

        ex.extract(694, out);
        float feat_size = out.c * out.h * out.w;
        feats.resize(feat_size);
        memcpy(feats.data(), out.data, sizeof(float) * feat_size);

        if (param_.normalized) {
            normalize(feats);
        }
    }

    return true;
}

float ModuleRecognizer::getScore(const std::vector<float>& f1, const std::vector<float>& f2) {
    float score = 0.0;
    for (unsigned int i = 0; i < f1.size(); i++) {
        score += f1[i] * f2[i];
    }
    return score;
}