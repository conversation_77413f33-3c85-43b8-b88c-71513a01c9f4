/**  
 * All rights Reserved, Designed By MI
 * @projectName ModuleMotionor
 * @title     motion   
 * @package    ${PACKAGE_NAME}  
 * @description    ${TODO}  
 * <AUTHOR>     
 * @date   2022/1/12 下午5:31  
 * @version V0.0.0
 * @copyright 2022 <EMAIL>
 */
//
#include "model/ModuleMotion.h"
#include "CommonUtils.h"
#define FAST_FLOOR(x) ((int)(x))


#define MAX2(x, y) (((x)>(y))?(x):(y))
#define MIN2(x, y) (((x)<(y))?(x):(y))
#define ABS(x) (((x)<0)?((-(x))):(x))
#define DIFF(x, y) (((x)>(y))?((x)-(y)):((y)-(x)))
#define DIFF2(x, y) (((x)-(y))*((x)-(y)))
void FindHistSpecification(float src[256], float dst[256], int map[256])
{
    int x = 0;
    int y = 0;
    int i = 0;
    int minX = 0;
    float minValue = 0;
    float srcMin[256][256];
    short lastStartY = 0, lastEndY = 0, startY = 0, endY = 0;

    for (y = 0; y < 256; y++)
    {
        for (x = 0; x < 256; x++)
        {
            srcMin[x][y] = fabs(src[y] - dst[x]);
        }
    }

    //GML

    for (x = 0; x < 256; x++)
    {
        minValue = srcMin[x][0];
        for (y = 0; y < 256; y++)
        {
            if (minValue > srcMin[x][y])
            {
                endY = y;
                minValue = srcMin[x][y];
            }
        }

        if (startY != lastStartY || endY != lastEndY)
        {
            for (i = startY; i <= endY; i++)
            {
                map[i] = x;
            }
            lastStartY = startY;
            lastEndY = endY;
            startY = lastEndY + 1;
        }
    }
}

void Hist2IntHist(int pHist[256], float pfHist[256])
{
    int i;
    int HistAcc[256];
    memset(HistAcc, 0, 256 * sizeof(int));
    HistAcc[0] = pHist[0];
    for (i = 1; i < 256; i++)
    {
        HistAcc[i] = HistAcc[i - 1] + pHist[i];
    }

    for (i = 0; i < 256; i++)
        pfHist[i] = (float)HistAcc[i] / (float)HistAcc[255];
}
void FindMappingCurve_Y_Give(float pHist[][256], float dstHist[256], int map_to_ref[][256], int map_from_ref[][256], int nExposureNum)
{
    int i;
    for (i = 0; i < nExposureNum; i++)
    {

        memset(map_to_ref[i], 0, 256 * sizeof(int));
        memset(map_from_ref[i], 0, 256 * sizeof(int));

        FindHistSpecification(pHist[i], dstHist, map_to_ref[i]);
        FindHistSpecification(dstHist, pHist[i], map_from_ref[i]);

    }
    for (int i = 0; i < (nExposureNum); i++)
    {
        for (int j = 1; j < 256; j++) {
            if (map_to_ref[i][j - 1] > map_to_ref[i][j]) map_to_ref[i][j] = map_to_ref[i][j - 1];
            if (map_from_ref[i][j - 1] > map_from_ref[i][j]) map_from_ref[i][j] = map_from_ref[i][j - 1];
        }
    }

    int maxA = 255; int minA = 0;
    int maxB = 255; int minB = 0;
    for (int i = 0; i < (nExposureNum); i++)
    {
        minA = MAX2(minA, map_to_ref[i][0]);
        maxA = MIN2(maxA, map_to_ref[i][255]);
        minB = MAX2(minB, map_from_ref[i][0]);
        maxB = MIN2(maxB, map_from_ref[i][255]);
    }
    for (int i = 0; i < (nExposureNum); i++)
    {
        for (int j = 0; j < 256; j++) {
            if (map_to_ref[i][j] < minA)map_to_ref[i][j] = minA;
            if (map_to_ref[i][j] > maxA)map_to_ref[i][j] = maxA;
            if (map_from_ref[i][j] < minB)map_from_ref[i][j] = minB;
            if (map_from_ref[i][j] > maxB)map_from_ref[i][j] = maxB;
        }

    }

}


//main
bool AvgHistogramMapY(int hist[8][256], int nExposureNum, int map_to_ref[8][256], int map_from_ref[8][256])
{
    int dstHist[256] = { 0 };
    float HistAcc[8][256];
    float dstHistAcc[256];
#if 0
	for (int j = 0; j < 256; j++)
	{
		for (int i = 0; i < nExposureNum; i++)
		{
			dstHist[j] += hist[i][j];
		}
		dstHist[j] /= nExposureNum;
	}
#else
    for (int j = 0; j < 256; j++)
    {
        dstHist[j] = hist[1][j];
    }
#endif


    Hist2IntHist(dstHist, dstHistAcc);

    for (int i = 0; i < nExposureNum; i++) {
        //printf("Hist %i\n", i);
        //for (int j = 0; j < 256; j++) printf("%i ", hist[i][j]);
        //printf("\n");
        Hist2IntHist(hist[i], HistAcc[i]);
    }
    FindMappingCurve_Y_Give(HistAcc, dstHistAcc, map_to_ref, map_from_ref, nExposureNum);
    return true;
}


int region_erode(uint8_t *srcImg,
                 int32_t width,
                 int32_t height,
                 int32_t kernel_size /* 3 */,
                 mi_motion_area_t *area_ptr)
{
    uint8_t *pSrc = NULL;
    int x_start;
    int w;
    int h;
    int s;
    int idx;
    int idx_temp;

    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    s = area_ptr->stride;

    if (width - kernel_size < 0 && height - kernel_size < 0) {
        return -1;
    }
    int mid = (kernel_size + 1) / 2 - 1;
    int k = mid;
    int p = mid;
    int m = 0;
    int n = 0;
    unsigned char val = 128;

    pSrc = srcImg;
    for (k = mid; k < h - mid - 1; k++) {
        idx = k * s + x_start;

        for (p = mid; p < w - mid - 1; p++) {
            for (m = 0; m < kernel_size; m++) {
                idx_temp = idx + p + m * s;
                for (n = 0; n < kernel_size; n++) {
                    if (m == mid && n == mid)continue;
                    val &= (*(pSrc + idx_temp + n));

                }

            }

            *(pSrc + idx + p) = val;
            val = 255;
        }
    }
    pSrc = NULL;

    return 0;
}

int motion_area_get(int w, int h, int wdiv_cnt, int hdiv_cnt, int *thresholds,float motion_rel_thres, std::vector<mi_motion_area_t> &areas)
{
    ;
    int i;
    int j;

    int grid_w;
    int grid_h;

    grid_w = w / wdiv_cnt;
    grid_h = h / hdiv_cnt;
    areas.clear();
    //        m_areas.reserve(wdiv_cnt*hdiv_cnt);

    for (i = 0; i < hdiv_cnt; i++) {
        for (j = 0; j < wdiv_cnt; j++) {
            mi_motion_area_t m_areas;
            m_areas.x_offset = j * grid_w;
            m_areas.y_offset = i * grid_h;
            m_areas.x_width = grid_w;
            m_areas.y_height = grid_h;
            m_areas.stride = w;
            m_areas.threshold = thresholds[i * wdiv_cnt + j];
            m_areas.motion_ratio = motion_rel_thres;
            m_areas.m_flag = 0;
            areas.push_back(m_areas);
        }
    }
    return 0;
}

int motion_area_update(std::vector<mi_motion_area_t> &areas)
{

    for (size_t k = 0; k < areas.size(); ++k) {

        areas[k].m_flag = 0;
    }

    return 0;
}

int image_abs(uint8_t *a_img, uint8_t *b_img, uint8_t *dst_img, int32_t image_size, uint8_t threshold)
{
    int32_t i;
    int32_t counting = 0;
    for (i = 0; i < image_size; ++i) {
        int abs_value = abs((int)*(a_img + i) - (int)*(b_img + i));
        if (abs_value > threshold) {
            *(dst_img + i) = 255;
            ++counting;
        }
        else {
            *(dst_img + i) = 0;
        }
    }
    return counting;
}

static int region_abs(uint8_t *a_img, uint8_t *b_img, uint8_t *dst_img, mi_motion_area_t *area_ptr)
{
    int x_start;
    int i;
    int j;
    int w;
    int h;
    int s;
    int idx;

    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    s = area_ptr->stride;

    for (j = 0; j < h; j++) {
        idx = x_start + j * s;
        for (i = 0; i < w; i++) {

            *(dst_img + idx + i) = (unsigned char)abs(
                (int)(*(a_img + idx + i)) - (int)(*(b_img + idx + i)));

        }
    }

    return 0;
}

static int region_blur_with_abs(uint8_t *a_img,
                                uint8_t *b_img,
                                uint8_t *blur_img,
                                uint8_t *dst_img,
                                mi_motion_area_t *area_ptr)
{
    int x_start;
    int i;
    int j;
    int k;
    int p;
    int w;
    int h;
    int s;
    int idx;
    float v = 1.0 / 9.0;
    float kernel[3][3] = {{v, v, v},                                //initialize the blurrring kernel
                          {v, v, v},
                          {v, v, v}};
    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    s = area_ptr->stride;

    for (j = 0; j < h; j++) {
        idx = x_start + j * s;
        for (i = 0; i < w; i++) {
            float sum = 0.0;
            for (k = -1; k <= 1; ++k) {
                for (p = -1; p <= 1; ++p) {
                    /// *buffer[(j+k)*width+(i+p)];
                    int index = x_start + (j + k) * s + (i + p);
                    sum = sum
                          + (float)kernel[k + 1][p + 1] * (*(a_img + index));    //matrix multiplication with kernel
                }
            }
            *(blur_img + idx + i) = sum;
            *(dst_img + idx + i) = (unsigned char)abs(
                (int)(sum) - (int)(*(b_img + idx + i)));
        }
    }

    return 0;
}

int region_dilation(uint8_t *srcImg,
                    int32_t width,
                    int32_t height,
                    int32_t kernel_size /* 3 */,
                    mi_motion_area_t *area_ptr)
{
    uint8_t *pSrc = NULL;
    uint8_t *pSrc_temp = NULL;
    int mid = (kernel_size + 1) / 2 - 1;
    int i = mid;
    int j = mid;
    int m = 0;
    int n = 0;
    int x_start;
    int w;
    int h;
    int s;
    int idx;
    unsigned char val = 0;

    if (width - kernel_size < 0 && height - kernel_size < 0) {
        return -1;
    }

    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    s = area_ptr->stride;


    pSrc = srcImg;
    for (j = mid; j < h - mid - 1; j++) {
        idx = x_start + j * s;

        for (i = mid; i < w - mid - 1; i++) {

            pSrc_temp = pSrc;
            for (m = 0; m < kernel_size; m++) {
                for (n = 0; n < kernel_size; n++) {
                    if (m == 0 && n == 0)continue;
                    val |= pSrc_temp[idx + i + n];
                }
                pSrc_temp += s;
            }

            pSrc[idx + i] = val;
            val = 0;
        }
        pSrc += s;
    }

    pSrc = NULL;
    pSrc_temp = NULL;

    return 0;
}

// ----------------------------------------------------------------------------
void resize_nearest(const uint8_t *image,
                    uint8_t *out,
                    int sourceWidth,
                    int sourceHeight,
                    int targetWidth,
                    int targetHeight)
{
    // Pre-calc some constants
    const float xRatio = sourceWidth / (float)targetWidth;
    const float yRatio = sourceHeight / (float)targetHeight;
    const int originalLineSize = sourceWidth * 1;
    const int newRowSize = targetWidth * 1;

    const auto channels = 1;

    // Go through each image line
    int newStart, oldStart;
    int scaledOriginalLineSize;
    for (int y = 0; y < targetHeight; y++) {
        scaledOriginalLineSize = FAST_FLOOR(y * yRatio) * originalLineSize;

        for (int x = 0; x < targetWidth; x++) {
            // calc start index of old and new pixel data
            newStart = y * newRowSize + x * channels;
            oldStart = scaledOriginalLineSize + FAST_FLOOR(x * xRatio) * channels;

            // copy values from the old pixel array to the new one
            memcpy(out + newStart, image + oldStart, channels);
        }
    }
}

ModuleMotion::ModuleMotion()
{
    is_init = false;
    input_height_ = 128;
    input_width_ = 128;
    input_image_size = input_height_*input_width_;
    first_in_status = true;
    cache_frame = NULL;
    abs_frame = NULL;
    erode_frame = NULL;


    if (cache_frame == NULL){
        cache_frame = (uint8_t *)malloc(input_image_size * sizeof(uint8_t));
        if (NULL == cache_frame) return;
    }


    //        uint8_t * dst_frame = (uint8_t *)malloc(input_image_size * sizeof(uint8_t));
    if (abs_frame == NULL){
        abs_frame = (uint8_t *)malloc(input_image_size * sizeof(uint8_t));
        if (NULL == abs_frame) return;
    }
    if (erode_frame == NULL){
        erode_frame = (uint8_t *)malloc(input_image_size * sizeof(uint8_t));
        if (NULL == erode_frame) return;
    }
}


int  ModuleMotion::motion_cache(move_param_t m_param,uint8_t *frame, int w, int h,  Bbox& box){
    int ret = 0;
    // crop extend bbox image
    int x          = static_cast<int>(box.x1);
    int y          = static_cast<int>(box.y1);
    int box_width  = static_cast<int>(box.x2 - box.x1 );
    int box_height = static_cast<int>(box.y2 - box.y1 );

    /// 加入人脸框的限制
    if (x < 0) {
        x = 0;
    }
    else if (x > w) {
        x = w;
    }

    if (y < 0) {
        y = 0;
    }
    else if (y > h) {
        y = h;
    }

    if (x + box_width > w) {
        box_width = w - x;
        if (box_width <= 0) {
            return -1;
        }
    }
    if (y + box_height > h) {
        box_height = h - y;
        if (box_height <= 0) {
            return -1;
        }
    }

    roi_detects_count_ = m_param.roi_detects_count;
    //        cache_frame = (uint8_t *)miio_malloc(input_image_size * sizeof(uint8_t), MIIO_ALIGN_BYTES_64);


    motion_area_get(input_width_,
                    input_height_,
                    m_param.width_divide_count,
                    m_param.height_divide_count,
                    m_param.thresholds,
                    m_param.motion_rel_thres,
                    roi_detects);


    is_init = true;

    std::vector<int8_t> roi_results;
    roi_results.resize(roi_detects_count_);




    int                            face_img_size = box_width * box_height * 1;
    unsigned char* face_img      = new unsigned char[face_img_size];
    if (NULL == face_img) return MOTION_INIT_ERR;
    bstutils::CropImageC1(frame, w, h, face_img, x, y, box_width, box_height);

    /// 缓存第一帧图像
    if(cache_frame != NULL && face_img!= NULL){
        ncnn::resize_bilinear_c1(face_img, box_width, box_height, cache_frame, input_width_, input_height_);
    }
    delete [] face_img;
    
    return MOTION_OPERATION_SUCCESS;
}

int ModuleMotion::motion_detect(uint8_t *frame, int w, int h, Bbox& box,
                                   int &motion_result,int g_first_in)
{
    int ret = 0;

    
    std::vector<int8_t> roi_results;
    if (!is_initialized()) {
        return MOTION_NOT_INIT_ERR;
    }

    if (g_first_in == 1)
    {
        /* code */
        motion_result = 65535;
        return MOTION_OPERATION_SUCCESS;
    }
    roi_results.resize(roi_detects_count_);


    // crop extend bbox image
    int x          = static_cast<int>(box.x1);
    int y          = static_cast<int>(box.y1);
    int box_width  = static_cast<int>(box.x2 - box.x1 );
    int box_height = static_cast<int>(box.y2 - box.y1 );

    /// 加入人脸框的限制
    if (x < 0) {
        x = 0;
    }
    else if (x > w) {
        x = w;
    }

    if (y < 0) {
        y = 0;
    }
    else if (y > h) {
        y = h;
    }

    if (x + box_width > w) {
        box_width = w - x;
        if (box_width <= 0) {
            return -1;
        }
    }
    if (y + box_height > h) {
        box_height = h - y;
        if (box_height <= 0) {
            return -1;
        }
    }

    int                            face_img_size = box_width * box_height * 1;
    unsigned char* face_img      = new unsigned char[face_img_size];
    unsigned char* face_img_resize      = new unsigned char[input_width_*input_height_*1];
    if (NULL == face_img || NULL == face_img_resize) return MOTION_INIT_ERR;
    bstutils::CropImageC1(frame, w, h, face_img, x, y, box_width, box_height);
//#define DUMP
#ifdef DUMP
    char file_name[256];
    static int counting = 0;
    snprintf(file_name, sizeof(file_name), "result_%d", counting);
    bstutils::save_image_png(file_name, face_img, box_width, box_height, 1);
#endif

    ncnn::resize_bilinear_c1(face_img, box_width, box_height, face_img_resize, input_width_, input_height_);
    delete [] face_img;
#ifdef DUMP
    bstutils::save_image_png("cache_frame", cache_frame, input_width_, input_height_, 1);
    bstutils::save_image_png("face_img_resize", face_img_resize, input_width_, input_height_, 1);
#endif
    // 第三帧图像 才开启图像对齐
    if (g_first_in > 1) {
        /// 直方图对齐

        int Hist[8][256] = { 0 };



        for (int j = 0; j < input_height_; j++)
        {
            for (int i = 0; i < input_width_; i++)
            {
                int d1 = int(cache_frame[j * input_width_ + i]);
                int d2 = int(face_img_resize[j * input_width_ + i]);
                Hist[0][d1]++;
                Hist[1][d2]++;
            }
        }
        int map2ref[8][256]; int ref2map[8][256];
        AvgHistogramMapY(Hist, 2, map2ref, ref2map);

        for (int j = 0; j < input_height_; j++)
        {
            for (int i = 0; i < input_width_; i++)

            {
                int d1 = int(cache_frame[j * input_width_ + i]);
                int d2 = int(face_img_resize[j * input_width_ + i]);
                cache_frame[j * input_width_ + i] = map2ref[0][d1];
                face_img_resize[j * input_width_ + i] = map2ref[1][d2];
            }
        }
    }
#ifdef DUMP
    bstutils::save_image_png("face_img_resize_aug", face_img_resize, input_width_, input_height_, 1);
    bstutils::save_image_png("cache_frame_aug", cache_frame, input_width_, input_height_, 1);
#endif

    /**
         * @brief 初始化
         */
    // std::fill(roi_results.begin(), roi_results.end(), 0);
    for (auto roi_result : roi_results) {
        roi_result = 0;
    }
    motion_area_update(roi_detects);
    memset(erode_frame, 0, input_image_size * sizeof(uint8_t));

    /**
         * @brief 1. 全图做图像滤波，太耗时了，替换简单滤波操作。
         */
    for (size_t i = 0; i < roi_detects.size(); ++i) {
        /**
             * @brief 2. 分区域做图像帧差
             */

        ret = region_abs(face_img_resize, cache_frame, abs_frame, &roi_detects[i]);
        ret = region_threshold_binary(abs_frame, erode_frame, abs_frame, &roi_detects[i]);

        //            memcpy(threshold_frame,abs_frame, input_image_size* sizeof(uint8_t));
        if (roi_detects[i].m_flag > 0) { /// 只有帧差有结果时，才进行腐蚀操作
            /**
                 * @brief 3. 分区域做腐蚀
                 */
            //                int kernel_size = 7;
            //                ret = region_dilation(erode_frame,input_width_,input_height_, 3 ,&roi_detects[i]);
            int kernel_size = 3;
            if (input_height_ >= kernel_size && input_width_ >= kernel_size){
                ret = region_erode(erode_frame, input_width_, input_height_, kernel_size, &roi_detects[i]);
            }

            /**
                 * @brief 4. 腐蚀完成后再统计
                 */
            ret = region_motion_caculate(erode_frame, &roi_detects[i]);

        }

        /**
             * @brief 5. 输出区域侦测结果
             */
        if (roi_detects[i].m_flag == 2) {
            roi_results[i] = 1;
        }
        else {
            roi_results[i] = 0;

        }
    }

    int m_flag = 0;
    int count_trigger = 0;
    motion_result = 0;
    m_flag = 0;
    count_trigger = 0;
    for (size_t index = 0; index < roi_results.size(); index++) {
        if (roi_results[index] > 0) {
            m_flag = 1;
        }
        else {
            m_flag = 0;
        }
        //m_flag = (m_areas[i].m_flag > 0 ? 1 : 0); //only for debug temporary!!!
        m_flag = m_flag << index;
        motion_result |= m_flag;
    }

#ifdef DUMP
            snprintf(file_name, sizeof(file_name), "abs_%d.jpg", counting);
            bstutils::save_image_png(file_name, abs_frame, input_width_, input_height_, 1);
#endif

    //
    //        counting++;

    is_init = false;

    delete [] face_img_resize;
    return MOTION_OPERATION_SUCCESS;

}

int ModuleMotion::set_thresholds(std::vector<int> thresholds)
{
    if (thresholds.size() != roi_detects.size()) {
        return -1;
    }
    for (int i = 0; i < roi_detects.size(); ++i) {
        roi_detects[i].threshold = thresholds[i];
    }
    return 0;
}

int ModuleMotion::get_thresholds(std::vector<int> &thresholds)
{
    if (thresholds.size() != roi_detects.size()) {
        return -1;
    }

    for (int i = 0; i < roi_detects.size(); ++i) {
        thresholds[i] = roi_detects[i].threshold;
    }
    return 0;
}
int ModuleMotion::get_areas_count(int &num)
{
    num = int(roi_detects.size());
    return 0;
}

bool ModuleMotion::is_initialized() const
{
    return is_init;
}

int ModuleMotion::region_threshold_binary(uint8_t *src_img,
                                             uint8_t *dst_img,
                                             uint8_t *buf_img,
                                             mi_motion_area_t *area_ptr)
{
    int x_start;
    int i;
    int j;
    int w;
    int h;
    int s;
    int idx;
    int threshold;
    int count = 0;
    int binary_threshold_count = 0; /// 区域内二值化的count

    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    binary_threshold_count = int(w * h * area_ptr->motion_ratio);
    s = area_ptr->stride;
    threshold = area_ptr->threshold;


    for (j = 0; j < h; j++) {
        idx = x_start + j * s;
        for (i = 0; i < w; i++) {
            uint8_t value = *(src_img + idx + i);
            if ((int)value > threshold) {
                *(dst_img + idx + i) = 255;
                *(buf_img + idx + i) = 255;
                ++count;
            }
            else {
                *(dst_img + idx + i) = 0;
                *(buf_img + idx + i) = 0;
            }

        }
    }
    if (count > binary_threshold_count) {
        area_ptr->m_flag = 1;
    }
    return 0;
}



int ModuleMotion::region_motion_caculate(uint8_t *src_img, mi_motion_area_t *area_ptr)
{
    int x_start;
    int i;
    int j;
    int w;
    int h;
    int s;
    int idx;
    int threshold;
    int count = 0;
    int binary_threshold_count = 0; /// 区域内二值化的count

    //frm is entire picture
    x_start = area_ptr->y_offset * area_ptr->stride + area_ptr->x_offset;
    w = area_ptr->x_width;
    h = area_ptr->y_height;
    binary_threshold_count = w * h * area_ptr->motion_ratio;
    s = area_ptr->stride;
    threshold = area_ptr->threshold;

    for (j = 0; j < h; j++) {
        idx = x_start + j * s;
        for (i = 0; i < w; i++) {
            uint8_t value = *(src_img + idx + i);
            if ((int)value > threshold) {
                ++count;
            }
        }
    }
    if (count > binary_threshold_count) {
        area_ptr->m_flag = 2;
    }
    return 0;
}


int ModuleMotion::clear_cache()
{
    first_in_status = true;
    return MOTION_OPERATION_SUCCESS;
}

int ModuleMotion::release()
{
    if (cache_frame != NULL) {
        free(cache_frame);
        cache_frame = NULL;
    }
    if (abs_frame != NULL) {
        free(abs_frame);
        abs_frame = NULL;
    }
    if (erode_frame != NULL) {
        free(erode_frame);
        erode_frame = NULL;
    }
    return 0;
}

