#include "BSTFaceUnlockConfig.h"
#include "Json.h"
#include "BuiltInCfg.h"

const char* DES_PARA_KEY = "BST_PARA_2189";
#define VER_LEN 64

void SetStringArray(std::vector<std::string>& value, const cJSON* json, const std::string& key) {
    cJSON*       sub_json = cJSON_GetObjectItemCaseSensitive(json, key.c_str());
    const cJSON* element  = nullptr;
    cJSON_ArrayForEach(element, sub_json) {
        if (cJSON_IsString(element) && (element->valuestring != nullptr)) {
            value.push_back(std::string(element->valuestring));
        }
    }
}

void SetString(std::string& value, const cJSON* json, const std::string& key) {
    cJSON* sub_json = cJSON_GetObjectItemCaseSensitive(json, key.c_str());
    if (cJSON_IsString(sub_json) && (sub_json->valuestring != nullptr)) {
        value = std::string(sub_json->valuestring);
    }
}

void SetFloat(float& value, const cJSON* json, const std::string& key) {
    cJSON* sub_json = cJSON_GetObjectItemCaseSensitive(json, key.c_str());
    if (cJSON_IsNumber(sub_json)) {
        value = cJSON_GetNumberValue(sub_json);
    }
}

void SetInt(int& value, const cJSON* json, const std::string& key) {
    cJSON* sub_json = cJSON_GetObjectItemCaseSensitive(json, key.c_str());
    if (cJSON_IsNumber(sub_json)) {
        value = (int)cJSON_GetNumberValue(sub_json);
    }
}

void SetBool(bool& value, const cJSON* json, const std::string& key) {
    cJSON* sub_json = cJSON_GetObjectItemCaseSensitive(json, key.c_str());
    if (cJSON_IsBool(sub_json)) {
        value = cJSON_IsTrue(sub_json);
    }
}

void ParseCommonConfig(CommonConfig& config, const cJSON* json) {
    SetBool(config.log_enable, json, "log_enable");
    SetBool(config.verfy_manu, json, "verfy_manu");
    SetBool(config.verfy_prod, json, "verfy_prod");
    SetBool(config.verfy_time, json, "verfy_time");
    SetString(config.manufacturer, json, "manufacturer");
    SetString(config.product_name, json, "product_name");
    SetString(config.time_end, json, "time_end");

    SetBool(config.dump_enable, json, "dump_enable");
    SetString(config.default_dump_path, json, "default_dump_path");
}

void ParseFilterConfig(FilterConfig& config, const cJSON* json) {
    SetBool(config.bright_filter_enable, json, "bright_filter_enable");
    SetBool(config.blur_filter_enable, json, "blur_filter_enable");
    SetBool(config.occ_filter_enable, json, "occ_filter_enable");
    SetBool(config.live_filter_enable, json, "live_filter_enable");
    SetBool(config.blink_filter_enable, json, "blink_filter_enable");
    SetBool(config.angel_filter_enable, json, "angel_filter_enable");
    SetFloat(config.match_thres, json, "match_thres");
    SetFloat(config.match_thres_withmask, json, "match_thres_withmask");
    SetFloat(config.match_thres_big_pitch, json, "match_thres_big_pitch");
    SetInt(config.match_thres_up_pitch_thres, json, "match_thres_up_pitch_thres");
    SetInt(config.match_thres_down_pitch_thres, json, "match_thres_down_pitch_thres");
    SetInt(config.match_thres_left_pitch_thres, json, "match_thres_left_pitch_thres");
    SetInt(config.match_thres_right_pitch_thres, json, "match_thres_right_pitch_thres");


    SetFloat(config.face_dark_level_1_thres, json, "face_dark_level_1_thres");
    SetFloat(config.face_dark_level_2_thres, json, "face_dark_level_2_thres");
    SetFloat(config.iso_dark_thres, json, "iso_dark_thres");
    SetFloat(config.iso_dark_level_1_thres, json, "iso_dark_level_1_thres");
    SetFloat(config.live_face_dark_thres, json, "live_face_dark_thres");
    SetFloat(config.live_bg_dark_thres, json, "live_bg_dark_thres");
    SetFloat(config.live_ISO_thres, json, "live_ISO_thres");
    SetFloat(config.bright_thres, json, "bright_thres");
    SetFloat(config.model_blur_thres, json, "model_blur_thres");
    SetFloat(config.quality_blur_thres, json, "quality_blur_thres");
    SetFloat(config.live_dark_thres, json, "live_dark_thres");
    SetFloat(config.live_normal_thres, json, "live_normal_thres");
    SetFloat(config.blink_thres, json, "blink_thres");
    SetFloat(config.mask_hole_thres, json, "mask_hole_thres");
    SetFloat(config.mask_thres, json, "mask_thres");

    SetFloat(config.left_yaw_thres, json, "left_yaw_thres");
    SetFloat(config.up_pitch_thres, json, "up_pitch_thres");
    SetFloat(config.right_yaw_thres, json, "right_yaw_thres");
    SetFloat(config.down_pitch_thres, json, "down_pitch_thres");
    SetInt(config.motion_abs_thres, json, "motion_abs_thres");
    SetInt(config.timeout_second, json, "timeout_second");
    SetInt(config.timeout_frame, json, "timeout_frame");
    SetInt(config.max_livingD_suspected_live_attack, json, "max_livingD_suspected_live_attack");
    SetInt(config.max_livingC_suspected_live_attack, json, "max_livingC_suspected_live_attack");
    SetInt(config.max_suspected_live_attack, json, "max_suspected_live_attack");
    SetInt(config.max_suspected_live_attack_range, json, "max_suspected_live_attack_range");

    SetInt(config.max_continue_real, json, "max_continue_real");
    SetInt(config.after_suspected_continue_real, json, "after_suspected_continue_real");
    SetInt(config.border_left, json, "border_left");
    SetInt(config.border_right, json, "border_right");
    SetInt(config.border_top, json, "border_top");
    SetInt(config.border_bottom, json, "border_bottom");

    SetFloat(config.min_face, json, "min_face");
}

bool ParseConfig(FaceUnlockConfig& fu_config, const char* const config_str) {
    cJSON* config_json = cJSON_Parse(config_str);
    if (config_json == nullptr) {
        cJSON_Delete(config_json);
        return false;
    }
    const cJSON* common     = cJSON_GetObjectItemCaseSensitive(config_json, "common");
    const cJSON* faceunlock = cJSON_GetObjectItemCaseSensitive(config_json, "faceunlock");
    const cJSON* enroll     = cJSON_GetObjectItemCaseSensitive(faceunlock, "enroll");
    const cJSON* authen     = cJSON_GetObjectItemCaseSensitive(faceunlock, "authen");
    ParseCommonConfig(fu_config.common_config, common);
    ParseFilterConfig(fu_config.enroll_config, enroll);
    ParseFilterConfig(fu_config.authen_config, authen);

    cJSON_Delete(config_json);

    return true;
}

bool Verify(const CommonConfig& config) {
#ifdef __ANDROID__
    // manufacturer
    if (config.verfy_manu) {
        char manufacturer[PROP_VALUE_MAX];
        memset(manufacturer, 0, PROP_VALUE_MAX);
        __system_property_get("ro.product.manufacturer", manufacturer);
        LOGI("manufacturer = %s\n", manufacturer);
        LOGI("manufacturer in cfg = %s\n", config.manufacturer.c_str());
        if (strstr(config.manufacturer.c_str(), manufacturer) != 0) {
            LOGI("Phone Manufacturer Check Pass");
        } else {
            LOGE("Phone Manufacturer Check %s Fail", manufacturer);
            return false;
        }
    } else {
        LOGI("Not need to check manufacturer");
    }

    // product_name
    if (config.verfy_prod) {
        char product_model[PROP_VALUE_MAX];
        memset(product_model, 0, PROP_VALUE_MAX);
        __system_property_get("ro.product.name", product_model);
        LOGI("product.name = %s\n", product_model);
        LOGI("product.name in cfg = %s\n", config.product_name.c_str());
        if (strstr(config.product_name.c_str(), product_model) != 0) {
            LOGI("Phone Product Check Pass");
        } else {
            LOGE("Phone Product Check Fail");
            return false;
        }
    } else {
        LOGI("Not need to check product name");
    }

    // time
    if (config.verfy_time) {
        int timeyear, timermonth, timerday;
        sscanf(config.time_end.c_str(), "%d-%d-%d", &timeyear, &timermonth, &timerday);
        if ((timeyear < 1990) || (timermonth < 0) || (timermonth > 12) || (timerday > 31) || (timerday < 1)) {
            LOGE("time_end value error %d-%d-%d!", timeyear, timermonth, timerday);
            return false;
        }
        bool       pass = false;
        time_t     now_time;
        struct tm* local_time;
        now_time   = time(NULL);
        local_time = localtime(&now_time);
        LOGI("time %d  %d  %d ", timeyear, timermonth, timerday);
        LOGI("local time %d  %d  %d ", local_time->tm_year + 1900, local_time->tm_mon + 1, local_time->tm_mday);

        if (local_time->tm_year + 1900 < timeyear) {
            pass = true;
        } else if (local_time->tm_year + 1900 > timeyear) {
            pass = false;
        } else {
            if (1 + local_time->tm_mon < timermonth) {
                pass = true;
            } else if (1 + local_time->tm_mon > timermonth) {
                pass = false;

            } else {
                if (local_time->tm_mday <= timerday) {
                    pass = true;
                } else {
                    pass = false;
                }
            }
        }

        if (pass) {
            LOGI("Phone Time Check Pass");
        } else {
            LOGE("Phone Time Check Fail TimeEnd %d-%d-%d!", timeyear, timermonth, timerday);
            return false;
        }

    } else {
        LOGI("Not need to check time");
    }
#endif // __ANDROID__
    return true;
}

bool LoadConfig(const BSTFaceUnlockConfig& config, FaceUnlockConfig& fu_config, char* cfg_version_out) {
#ifndef SECUREOS
    if (BST_FU_CONFIG_TYPE_BUILT_IN == config.config_type) {
        LOGE("Use built-in cfg instead.");
    } else if (BST_FU_CONFIG_TYPE_FILE == config.config_type) {
        LOGE("Load cfg from file.");
        LOGE("Cfg File Path %s", (char*)config.cfg.config_path);
        if (config.cfg.config_path == NULL) {
            LOGE("Invalid cfg path.");
            return false;
        }
    } else {
        LOGE("Invalid cfg type.");
        return false;
    }
#else
    if (BST_FU_CONFIG_TYPE_BUILT_IN == config.config_type) {
        LOGE("Use built-in cfg instead.");
    } else {
        LOGE("Invalid cfg type.");
        return false;
    }
#endif

    char magic[36];
    if (BST_FU_CONFIG_TYPE_BUILT_IN == config.config_type) {
        memcpy(magic, __cmake_built_in_cfg_json, 36 * sizeof(char));
    }
#ifndef SECUREOS
    else {
        FILE* fp = fopen(config.cfg.config_path, "rb");
        if (fp == nullptr) {
            LOGE("Can not open parameter file: %s\n", (char*)config.cfg.config_path);
            return false;
        }
        memcpy(cfg_version_out, "unknow_version", VER_LEN);
        fread(magic, 1, 36, fp);
        fclose(fp);
    }
#endif
    if (magic[0] == 0x04 && magic[1] == 0x05 && magic[2] == 0x06 && magic[3] == 0x07) {
        // ciphertext
#ifndef SECUREOS
        FILE* fp = fopen((char*)config.cfg.config_path, "rb");
        fseek(fp, 0, SEEK_END);
        int file_len = ftell(fp);
        rewind(fp);
        int   data_len   = file_len - VER_LEN - 4;
        char* ciphertext = new char[file_len];
        char* plaintext  = new char[data_len + 1];
        if (NULL == ciphertext || NULL == plaintext) return false;
        fread(ciphertext, 1, file_len, fp);
        fclose(fp);
#else
        int file_len = __cmake_built_in_cfg_json_len;
        int data_len = file_len - 36;
        char* ciphertext = new char[file_len];
        char* plaintext  = new char[data_len + 1];
        if (NULL == ciphertext || NULL == plaintext) return false;
        memcpy(ciphertext, __cmake_built_in_cfg_json, __cmake_built_in_cfg_json_len * sizeof(unsigned char));
#endif
        char cfg_version[64 + 1];
        memset(cfg_version, 0, VER_LEN + 1);
        memcpy(cfg_version, ciphertext + 4, VER_LEN);
        memcpy(cfg_version_out, ciphertext + 4, VER_LEN);
        LOGE("BlackSesame FaceUnlock Cfg Version : %s", cfg_version);

        int dec_len = data_len - data_len % 8;
        if (!Using_DES(plaintext, ciphertext + 4 + VER_LEN, dec_len, strlen(DES_PARA_KEY), DES_PARA_KEY, DES_DECRYPTION)) {
            LOGE("Decode config error\n");
            delete[] ciphertext;
            delete[] plaintext;
            return false; // DBE_ERR_UNINITIALIZED;
        }
        for (int i = 0; i < data_len % 8; i++) {
            plaintext[dec_len + i] = ciphertext[dec_len + 36 + i];
        }
        plaintext[data_len] = '\0';

        // TODO:
        // DumpPathCheck(plaintext, data_len, m_strDumpPath);
        // printf("m_strDumpPath = %s \n", m_strDumpPath);
        LOGI("m_strDumpPath = %s ", fu_config.common_config.default_dump_path.c_str());

        if (!ParseConfig(fu_config, plaintext)) {
            LOGE("Parse config error\n");
            delete[] ciphertext;
            delete[] plaintext;
            return false;
        }

        // verification
        if (!Verify(fu_config.common_config)) {
            LOGE("Verification fail \n");
            delete[] ciphertext;
            delete[] plaintext;
            return false;
        }

        delete[] ciphertext;
        delete[] plaintext;

    } else { // plaintext
        if (BST_FU_CONFIG_TYPE_BUILT_IN == config.config_type) {
            char* plaintext = new char[__cmake_built_in_cfg_json_len + 1];
            if (NULL == plaintext) return false;
            memcpy(plaintext, __cmake_built_in_cfg_json, __cmake_built_in_cfg_json_len * sizeof(unsigned char));
            plaintext[__cmake_built_in_cfg_json_len] = '\0';
            if (!ParseConfig(fu_config, plaintext)) {
                LOGE("Parse plaintext config error\n");
                delete[] plaintext;
                return false;
            }
            delete[] plaintext;
        }
#ifndef SECUREOS
        else {
            FILE* fp = fopen((char*)config.cfg.config_path, "rb");
            fseek(fp, 0, SEEK_END);
            int file_len = ftell(fp);
            rewind(fp);
            int   data_len  = file_len - 36;
            char* plaintext = new char[file_len + 1];
            if (NULL == plaintext) return false;
            fread(plaintext, 1, file_len, fp);
            fclose(fp);
            plaintext[file_len] = '\0';
            if (!ParseConfig(fu_config, plaintext)) {
                LOGE("Parse plaintext config error\n");
                delete[] plaintext;
                return false;
            }
            delete[] plaintext;
        }
#endif
    }

    return true;
}