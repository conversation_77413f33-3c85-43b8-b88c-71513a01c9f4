#include "CommonUtils.h"
#ifdef SECUREOS
#include "openlibm/openlibm_math.h"
#else
#include "math.h"
#endif

#define  PI 3.141592657

#ifndef SECUREOS
#include<climits>
#define STB_IMAGE_WRITE_IMPLEMENTATION
#define STB_IMAGE_IMPLEMENTATION
#include "stb/stb_image_write.h"
#include "stb/stb_image.h"
#endif

namespace bstutils {

float min(float a, float b) {
    return a < b ? a : b;
}

float min(float a, float b, float c, float d) {
    int tmp_min;
    tmp_min = min(a, b);
    tmp_min = min(tmp_min, c);
    tmp_min = min(tmp_min, d);
    return tmp_min;
}

float max(float a, float b) {
    return a > b ? a : b;
}

float max(float a, float b, float c, float d) {
    int tmp_max;
    tmp_max = max(a, b);
    tmp_max = max(tmp_max, c);
    tmp_max = max(tmp_max, d);
    return tmp_max;
}


bool pointInsideBox(Point pt, Object box){

    if(pt.x >= box.x1 && pt.x <= box.x2 && pt.y >= box.y1 && pt.y <= box.y2) {
        return true;
    }

    return false;

}

bool expand_box(Object box,Object &expand_box,float ratio){
    int extend_x, extend_y, extend_w, extend_h;
    int cx = (box.x1 + box.x2) * 0.5;
    int cy = (box.y1 + box.y2)* 0.5;
    extend_w = box.x2 - box.x1;
    extend_h =  box.y2 - box.y1;

    extend_w *= ratio;
    extend_h *= ratio;
    // Calculate expanded side lengths
    int expand_side_w = (int)(extend_w * ratio);
    int expand_side_h = (int)(extend_h * ratio);

    // Calculate cut coordinates

    extend_x = (int)(cx - expand_side_w / 2.0f);
    extend_y = (int)(cy - expand_side_h / 2.0f);


    int extend_x3 = (int)(cx + expand_side_w / 2.0f);
    int extend_y3 = (int)(cy + expand_side_h / 2.0f);
    expand_box.x1 = extend_x;
    expand_box.y1 = extend_y;
    expand_box.x2 = extend_x3;
    expand_box.y2 = extend_y3;
    return true;
}

// General
void rotate_y270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original) {
    for (int i = 0; i < width_original; i++) {
        for (int j = 0; j < height_original; j++) {
            m_pData[i * height_original + j] = m_pData_original[j * stride_original + (width_original - 1 - i)];
        }
    }
}
void rotate_uv270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original) {
    for (int i = 0; i < width_original / 2; i++) {
        for (int j = 0; j < height_original / 2; j++) {
            m_pData[(i * height_original / 2 + j) * 2]     = m_pData_original[(j * stride_original / 2 + (width_original / 2 - 1 - i)) * 2];
            m_pData[(i * height_original / 2 + j) * 2 + 1] = m_pData_original[(j * stride_original / 2 + (width_original / 2 - 1 - i)) * 2 + 1];
        }
    }
}
void rotate_yuv_nv21_270(unsigned char* m_pData_original, unsigned char* m_pData, int width_original, int height_original, int stride_original) {
    rotate_y270(m_pData_original, m_pData, width_original, height_original, stride_original);
    rotate_uv270(m_pData_original + width_original * height_original, m_pData + width_original * height_original, width_original, height_original, stride_original);
}

#define ZLIMIT(v, vmin, vmax) ((v) < (vmin) ? (vmin) : ((v) > (vmax) ? (vmax) : (v)))
void cvt_yuv2bgr_pixel(unsigned char* r, unsigned char* g, unsigned char* b,
                       unsigned char y, unsigned char u, unsigned char v) {
    float c = (y - 16) * 298;
    float d = u - 128;
    float e = v - 128;
    *r = ZLIMIT(int(c + 409 * e + 128) / 256, 0, 255);
    *g = ZLIMIT(int((c - 100 * d - 208 * e + 128) / 256), 0, 255);
    *b = ZLIMIT(int((c + 516 * d + 128) / 256), 0, 255);
}

void nv21_to_rgb_keepsize(unsigned char* pData, int width, int height, int stride, int uv_stride, unsigned char* rgbDataPtr) {
    unsigned char* y = new unsigned char[height * stride];
    unsigned char* u = new unsigned char[height * stride];
    unsigned char* v = new unsigned char[height * stride];
    if (NULL == y || NULL == u || NULL == v) return ;

    unsigned char* yTmp  = NULL;
    unsigned char* uvTmp = NULL;
    unsigned char* uvPtr = pData + stride * height;
    unsigned char* yPtr  = y;
    unsigned char* uPtr  = u;
    unsigned char* vPtr  = v;

    unsigned char* r = rgbDataPtr;
    unsigned char* g = rgbDataPtr + 1;
    unsigned char* b = rgbDataPtr + 2;
    for (int i = 0; i < height; i++) {
        yTmp  = pData + stride * i;
        uvTmp = uvPtr + uv_stride * (i / 2);
        for (int j = 0; j < width; j++) {
            *yPtr = *yTmp;
            yTmp++;

            *vPtr = *uvTmp;
            uvTmp++;

            *uPtr = *uvTmp;
            uvTmp += ((j % 2) * 2 - 1); //even -1; odd +1.

            cvt_yuv2bgr_pixel(r, g, b, *yPtr, *uPtr, *vPtr);
            yPtr++;
            vPtr++;
            uPtr++;

            r += 3;
            g += 3;
            b += 3;
        }
    }

    delete[] y;
    delete[] u;
    delete[] v;
    return;
}

void nv12_to_rgb_keepsize(unsigned char* pData, int width, int height, int stride, int uv_stride, unsigned char* rgbDataPtr) {
    unsigned char* y = new unsigned char[height * stride];
    unsigned char* u = new unsigned char[height * stride];
    unsigned char* v = new unsigned char[height * stride];
    if (NULL == y || NULL == u || NULL == v) return ;

    unsigned char* yTmp  = NULL;
    unsigned char* uvTmp = NULL;
    unsigned char* uvPtr = pData + stride * height;
    unsigned char* yPtr  = y;
    unsigned char* uPtr  = u;
    unsigned char* vPtr  = v;

    unsigned char* r = rgbDataPtr;
    unsigned char* g = rgbDataPtr + 1;
    unsigned char* b = rgbDataPtr + 2;
    for (int i = 0; i < height; i++) {
        yTmp  = pData + stride * i;
        uvTmp = uvPtr + uv_stride * (i / 2);
        for (int j = 0; j < width; j++) {
            *yPtr = *yTmp;
            yTmp++;

            *uPtr = *uvTmp;
            uvTmp++;

            *vPtr = *uvTmp;
            uvTmp += ((j % 2) * 2 - 1); //even -1; odd +1.

            cvt_yuv2bgr_pixel(r, g, b, *yPtr, *uPtr, *vPtr);
            yPtr++;
            vPtr++;
            uPtr++;

            r += 3;
            g += 3;
            b += 3;
        }
    }

    delete[] y;
    delete[] u;
    delete[] v;
    return;
}

// ModuleDetect

/**
 * 归一化与padding操作，用于Yolo系列letterbox
 * @param inImg
 * @param outImg
 * @param inWidth
 * @param inHeight
 * @param outWidth
 * @param outHeight
 */
void norm_and_zeropadding(unsigned char* inImg, unsigned char* outImg, int inWidth, int inHeight, int outWidth, int outHeight) {
    int v_padding = (int)(outHeight - inHeight) / 2;
    int h_padding = (int)(outWidth - inWidth) / 2;
    //#pragma omp parallel for schedule(dynamic, 8)
    for (int in_y = 0; in_y < inHeight; in_y++) {
        unsigned char* in_row  = inImg + (in_y * inWidth * 3);
        unsigned char* out_row = outImg + ((in_y + v_padding) * outWidth * 3);
        int            in_x    = 0;
        for (; in_x < inWidth; in_x++) {
            out_row[(in_x + h_padding) * 3 + 0] = (in_row[in_x * 3 + 0]); /// /255.
            out_row[(in_x + h_padding) * 3 + 1] = (in_row[in_x * 3 + 1]);
            out_row[(in_x + h_padding) * 3 + 2] = (in_row[in_x * 3 + 2]);
        }
    }
}

void CropImageC1(const unsigned char* src, int srcw, int srch, unsigned char* dst, int rx, int ry, int rw, int rh) {
    const unsigned char* p_row_src = src + ry * srcw + rx;
    unsigned char*       p_row_dst = dst;
    for (int i = 0; i < rh; i++) {
        memcpy(p_row_dst, p_row_src, rw);

        p_row_src += srcw;
        p_row_dst += rw;
    }
}

void CropImageC3(const unsigned char* src, int srcw, int srch, unsigned char* dst, int rx, int ry, int rw, int rh) {
    const unsigned char* p_row_src = src + (ry * srcw + rx) * 3;
    unsigned char*       p_row_dst = dst;
    int                  srcstride = srcw * 3;
    int                  dststride = rw * 3;
    for (int i = 0; i < rh; i++) {
        memcpy(p_row_dst, p_row_src, rw * 3);
        p_row_src += srcstride;
        p_row_dst += dststride;
    }
}

void DrawPoint(unsigned char* src, int w, int h, int c, int dx, int dy, int size, int r, int g, int b) {
    for (int d = -size; d < size; d++) {
        for (int f = -size; f < size; f++) {
            int x = (dx + d);
            int y = (dy + f);
            if (x > (w - 1) || x < 0) {
                x = x > (w - 1) ? w - 1 : 0;
            }
            if (y > (h - 1) || y < 0) {
                y = y > (h - 1) ? h - 1 : 0;
            }
            *(src + (y * w * c) + (x * c) + 0) = r;
            if (c == 3) {
                *(src + (y * w * c) + (x * c) + 1) = g;
                *(src + (y * w * c) + (x * c) + 2) = b;
            }
        }
    }
}

void DrawRect(unsigned char* src, int w, int h, int c, int x1, int y1, int x2, int y2, int size, int r, int g, int b) {
    for (int y = y1; y < y2; y++) {
        DrawPoint(src, w, h, c, x1, y, size, r, g, b);
        DrawPoint(src, w, h, c, x2, y, size, r, g, b);
    }

    for (int x = x1; x < x2; x++) {
        DrawPoint(src, w, h, c, x, y1, size, r, g, b);
        DrawPoint(src, w, h, c, x, y2, size, r, g, b);
    }
}

bool BSTAG_MatrixTranspose(float* m1, int row1, int col1, float* m2) {
    int i, j;
    if (m2 == NULL) //×ªÖÃ½á¹û±£ÁôÔÚÔ­¾ØÕóÖÐ
    {
        float* m3;

        m3 = (float*)malloc(sizeof(float) * row1 * col1);
        if (NULL == m3) return false;
        for (i = 0; i < col1; ++i)
            for (j = 0; j < row1; ++j) {
                m3[i * row1 + j] = m1[j * col1 + i];
            }
        for (i = 0; i < row1; ++i)
            for (j = 0; j < col1; ++j)
                m1[i * col1 + j] = m3[j * col1 + i];
        free(m3);
    } else {
        for (i = 0; i < col1; ++i)
            for (j = 0; j < row1; ++j) {
                m2[i * row1 + j] = m1[j * col1 + i];
            }
    }
    return true;
}
bool BSTAG_MatriBSTulti(float* m1, int row1, int col1, float* m2, int row2, int col2,
                        float* m3) {
    int i, j, k;

    for (i = 0; i < row1; ++i)
        for (j = 0; j < col2; ++j) {
            float sum = 0;
            for (k = 0; k < col1; ++k)
                sum += m1[i * col1 + k] * m2[k * col2 + j];
            m3[i * col2 + j] = sum;
        }

    return true;
}
bool BSTAG_MatrixInverse(float* m1, int row1, int col1) {
    int    i, j, k;
    float  div, temp;
    float* out;
    int *  is, *js;

    if (row1 != col1)
        return false;

    out = (float*)malloc(sizeof(float) * row1 * col1);
    is  = (int*)malloc(sizeof(int) * row1);
    js  = (int*)malloc(sizeof(int) * row1);
    if (NULL == out || NULL == is || NULL == js) return false;
    for (i = 0; i < row1; ++i) {
        is[i] = i;
        js[i] = i;
    }

    // start from first column to the next
    for (k = 0; k < row1; ++k) {
        div = 0;
        for (i = k; i < row1; ++i)
            for (j = k; j < row1; ++j) {
                if (fabs(m1[i * col1 + j]) > div) {
                    div   = fabs(m1[i * col1 + j]);
                    is[k] = i;
                    js[k] = j;
                }
            }
        if (fabs(div) < 1e-40) {
            free(out);
            free(is);
            free(js);
            return false;
        }
        if (is[k] != k) {
            for (j = 0; j < row1; ++j) {
                temp                 = m1[k * col1 + j];
                m1[k * col1 + j]     = m1[is[k] * col1 + j];
                m1[is[k] * col1 + j] = temp;
            }
        }
        if (js[k] != k) {
            for (i = 0; i < row1; ++i) {
                temp                 = m1[i * col1 + k];
                m1[i * col1 + k]     = m1[i * col1 + js[k]];
                m1[i * col1 + js[k]] = temp;
            }
        }
        m1[k * col1 + k] = 1 / m1[k * col1 + k];
        for (j = 0; j < row1; ++j) {
            if (j != k)
                m1[k * col1 + j] = m1[k * col1 + j] * m1[k * col1 + k];
        }
        for (i = 0; i < row1; ++i) {
            if (i != k) {
                for (j = 0; j < row1; ++j) {
                    if (j != k)
                        m1[i * col1 + j] -= m1[i * col1 + k] * m1[k * col1 + j];
                }
            }
        }
        for (i = 0; i < row1; ++i) {
            if (i != k)
                m1[i * col1 + k] = -m1[i * col1 + k] * m1[k * col1 + k];
        }
    }

    for (k = row1 - 1; k >= 0; --k) {
        for (j = 0; j < row1; ++j)
            if (js[k] != k) {
                temp                 = m1[k * col1 + j];
                m1[k * col1 + j]     = m1[js[k] * col1 + j];
                m1[js[k] * col1 + j] = temp;
            }
        for (i = 0; i < row1; ++i)
            if (is[k] != k) {
                temp                 = m1[i * col1 + k];
                m1[i * col1 + k]     = m1[i * col1 + is[k]];
                m1[i * col1 + is[k]] = temp;
            }
    }
    free(is);
    free(js);
    free(out);
    return true;
}

void BSTAG_CalAffineTransformData_float(float* pt1_x, float* pt1_y, float* pt2_x, float* pt2_y, int npt, float& rot_s_x, float& rot_s_y,
                                        float& move_x, float& move_y) {
    float *X, *A, *B;
    float *temp, *TA;
    int    nDim = 4, nrow = npt * 2;
    int    i, ii;
    int    n1, n2;

    X    = (float*)malloc(sizeof(float) * nDim);
    A    = (float*)malloc(sizeof(float) * npt * nDim * 2);
    TA   = (float*)malloc(sizeof(float) * npt * nDim * 2);
    B    = (float*)malloc(sizeof(float) * npt * 2);
    temp = (float*)malloc(sizeof(float) * nDim * nDim);
    if (NULL == X || NULL == A || NULL == TA || NULL == B || NULL == temp) return ;

    for (i = 0; i < npt; ++i) {
        ii        = (i << 1);
        n1        = ii * nDim;
        n2        = (ii + 1) * nDim;
        B[ii]     = pt1_x[i];
        B[ii + 1] = pt1_y[i];
        A[n1]     = pt2_x[i];
        A[n1 + 1] = -pt2_y[i];
        A[n1 + 2] = 1;
        A[n1 + 3] = 0;
        A[n2]     = pt2_y[i];
        A[n2 + 1] = pt2_x[i];
        A[n2 + 2] = 0;
        A[n2 + 3] = 1;
    }

    BSTAG_MatrixTranspose(A, nrow, nDim, TA);
    BSTAG_MatriBSTulti(TA, nDim, nrow, A, nrow, nDim, temp);
    BSTAG_MatrixInverse(temp, nDim, nDim);
    BSTAG_MatriBSTulti(TA, nDim, nrow, B, nrow, 1, A);
    BSTAG_MatriBSTulti(temp, nDim, nDim, A, nDim, 1, X);

    rot_s_x = X[0];
    rot_s_y = X[1];
    move_x  = X[2];
    move_y  = X[3];

    free(TA);
    free(X);
    free(A);
    free(B);
    free(temp);
}

#define BSTAG_max(a, b) (((a) > (b)) ? (a) : (b))

void BSTAG_AffineTransformImage_Sam_Bilinear_C3(float rot_s_x, float rot_s_y, float move_x, float move_y,
                                                unsigned char* image, int ht, int wd, const unsigned char* ori_image, int oriht, int oriwd)

{
    //int i, j, k, l, n;
    int i, j;
    //int x, y;
    float  x1, y1;
    float *rx, *ry;
    //int maxx, maxy, minx, miny;
    int max_ht_wd = BSTAG_max(ht, wd) + 1;
    // std::cout<<"max_ht_wd: "<<max_ht_wd<<std::endl;
    float tx1, ty1;

    rx = (float*)malloc(sizeof(float) * max_ht_wd);
    ry = (float*)malloc(sizeof(float) * max_ht_wd);
    if (NULL == rx || NULL == ry) return ;
    for (i = 0; i < max_ht_wd; ++i)
        rx[i] = rot_s_x * i;
    if (rot_s_y == 0)
        memset(ry, 0, sizeof(int) * max_ht_wd);
    else {
        for (i = 0; i < max_ht_wd; ++i) {
            ry[i] = rot_s_y * i;
            // std::cout<<"ry:"<<ry[i]<<std::endl;
        }
    }

    //#pragma omp parallel for
    // Too many args to microtask
    // std::cout<<"loop_index: "<<ht * wd<<std::endl;
    for (int loop_index = 0; loop_index < ht * wd; ++loop_index) {
        i   = loop_index / wd;
        j   = loop_index % wd;
        tx1 = -ry[i] + move_x;
        ty1 = rx[i] + move_y;

        x1 = rx[j] + tx1;
        y1 = ry[j] + ty1;

        if (x1 < 0 || y1 < 0 || x1 >= oriwd - 1 || y1 >= oriht - 1) {
            image[i * wd * 3 + j * 3 + 0] = 0;
            image[i * wd * 3 + j * 3 + 1] = 0;
            image[i * wd * 3 + j * 3 + 2] = 0;
            continue;
        }
        int   x_int = int(x1), y_int = int(y1);
        float x_tail = x1 - x_int, y_tail = y1 - y_int;
        int   x_round = x_int + 1, y_round = y_int + 1;

        // int tmp_idx = y_round * (oriwd * 3) + x_round * 3 + 2;
        // if (tmp_idx >= oriht * oriwd * 3)
        // {
        // 	std::cout<<"tmp_idx: "<<tmp_idx<<std::endl;
        // }

#if 1
        float pixel1_b = ori_image[y_int * (oriwd * 3) + x_int * 3 + 0] * (1 - x_tail) + ori_image[y_int * (oriwd * 3) + x_round * 3 + 0] * x_tail;
        float pixel1_g = ori_image[y_int * (oriwd * 3) + x_int * 3 + 1] * (1 - x_tail) + ori_image[y_int * (oriwd * 3) + x_round * 3 + 1] * x_tail;
        float pixel1_r = ori_image[y_int * (oriwd * 3) + x_int * 3 + 2] * (1 - x_tail) + ori_image[y_int * (oriwd * 3) + x_round * 3 + 2] * x_tail;

        float pixel2_b = ori_image[y_round * (oriwd * 3) + x_int * 3 + 0] * (1 - x_tail) + ori_image[y_round * (oriwd * 3) + x_round * 3 + 0] * x_tail;
        float pixel2_g = ori_image[y_round * (oriwd * 3) + x_int * 3 + 1] * (1 - x_tail) + ori_image[y_round * (oriwd * 3) + x_round * 3 + 1] * x_tail;
        float pixel2_r = ori_image[y_round * (oriwd * 3) + x_int * 3 + 2] * (1 - x_tail) + ori_image[y_round * (oriwd * 3) + x_round * 3 + 2] * x_tail;

        image[i * wd * 3 + j * 3 + 0] = int(pixel1_b * (1 - y_tail) + pixel2_b * y_tail + 0.5);
        image[i * wd * 3 + j * 3 + 1] = int(pixel1_g * (1 - y_tail) + pixel2_g * y_tail + 0.5);
        image[i * wd * 3 + j * 3 + 2] = int(pixel1_r * (1 - y_tail) + pixel2_r * y_tail + 0.5);
#else
        //optimize
        int   off      = y_int * oriwd;
        int   off1     = (off + x_int) * 3;
        int   off2     = (off + x_round) * 3;
        float pixel1_b = (ori_image[off2 + 0] - ori_image[off1 + 0]) * x_tail + ori_image[off1 + 0];
        float pixel1_g = (ori_image[off2 + 1] - ori_image[off1 + 1]) * x_tail + ori_image[off1 + 1];
        float pixel1_r = (ori_image[off2 + 2] - ori_image[off1 + 2]) * x_tail + ori_image[off1 + 2];

        off            = y_round * oriwd;
        off1           = (off + x_int) * 3;
        off2           = (off + x_round) * 3;
        float pixel2_b = (ori_image[off2 + 0] - ori_image[off1 + 0]) * x_tail + ori_image[off1 + 0];
        float pixel2_g = (ori_image[off2 + 1] - ori_image[off1 + 1]) * x_tail + ori_image[off1 + 1];
        float pixel2_r = (ori_image[off2 + 2] - ori_image[off1 + 2]) * x_tail + ori_image[off1 + 2];

        off            = (i * wd + j) * 3;
        image[off + 0] = int((pixel2_b - pixel1_b) * y_tail + pixel1_b + 0.5);
        image[off + 1] = int((pixel2_g - pixel1_g) * y_tail + pixel1_g + 0.5);
        image[off + 2] = int((pixel2_r - pixel1_r) * y_tail + pixel1_r + 0.5);
#endif
    }
    free(rx);
    free(ry);
}




#define min_f(a, b, c) (fminf(a, fminf(b, c)))
#define max_f(a, b, c) (fmaxf(a, fmaxf(b, c)))
void rgb2hsv(const unsigned char& src_r, const unsigned char& src_g, const unsigned char& src_b, unsigned char& dst_h, unsigned char& dst_s, unsigned char& dst_v) {
    float r = src_r / 255.0f;
    float g = src_g / 255.0f;
    float b = src_b / 255.0f;

    float h, s, v; // h:0-360.0, s:0.0-1.0, v:0.0-1.0

    float max = max_f(r, g, b);
    float min = min_f(r, g, b);

    v = max;

    if (max == 0.0f) {
        s = 0;
        h = 0;
    } else if (max - min == 0.0f) {
        s = 0;
        h = 0;
    } else {
        s = (max - min) / max;

        if (max == r) {
            h = 60 * ((g - b) / (max - min)) + 0;
        } else if (max == g) {
            h = 60 * ((b - r) / (max - min)) + 120;
        } else {
            h = 60 * ((r - g) / (max - min)) + 240;
        }
    }

    if (h < 0) h += 360.0f;

    dst_h = (unsigned char)(h / 2);   // dst_h : 0-180
    dst_s = (unsigned char)(s * 255); // dst_s : 0-255
    dst_v = (unsigned char)(v * 255); // dst_v : 0-255
}

void quick_sort(int left, int right, unsigned char arr[]) {
    if (left >= right) return;
    int i, j, base, temp;
    i = left, j = right;
    base = arr[left];
    while (i < j) {
        while (arr[j] >= base && i < j) j--;
        while (arr[i] <= base && i < j) i++;
        if (i < j) {
            temp   = arr[i];
            arr[i] = arr[j];
            arr[j] = temp;
        }
    }
    arr[left] = arr[i];
    arr[i]    = base;
    quick_sort(left, i - 1, arr);
    quick_sort(i + 1, right, arr);
}

float percentile_pixel(const unsigned char* src_data, int data_size, double q) {
    // TODO: assert deleted
    // assert(q >= 0.0 && q <= 1.0);

    unsigned char* src_data_tmp = (unsigned char*)malloc(data_size * sizeof(unsigned char));
    if (NULL == src_data_tmp) return 0.;
    memcpy(src_data_tmp, src_data, data_size * sizeof(unsigned char));
    quick_sort(0, data_size - 1, src_data_tmp);

    float percenttile_ = src_data_tmp[int(data_size * q)];
    free(src_data_tmp);

    return percenttile_;
}

void lighting_aug(const unsigned char* src_img, unsigned char* dst_img, int img_size) {
    int  pixel_index   = 0;
    long hsv_v_img_sum = 0;
    int  pixel_min     = 0;
    int  pixel_max     = 0;
    while (pixel_index < img_size) {
        unsigned char pixel_h = 0;
        unsigned char pixel_s = 0;
        unsigned char pixel_v = 0;

        rgb2hsv(*(src_img + pixel_index), *(src_img + pixel_index + 1), *(src_img + pixel_index + 2), pixel_h, pixel_s, pixel_v);

        hsv_v_img_sum += pixel_v;
        for (int i = 0; i < 3; i++) {
            if (src_img[pixel_index + i] < pixel_min) {
                pixel_min = src_img[pixel_index + i];
            } else if (src_img[pixel_index + i] > pixel_max) {
                pixel_max = src_img[pixel_index + i];
            }
        }
        pixel_index += 3;
    }

    if (hsv_v_img_sum / (img_size / 3) > 130) {
        memcpy(dst_img, src_img, sizeof(unsigned char) * img_size); // bright enough
        return;
    }

    double min_percentile_pixel = percentile_pixel(src_img, img_size, 0.01);
    double max_percentile_pixel = percentile_pixel(src_img, img_size, 0.99);

    for (int i = 0; i < img_size; ++i) {
        if (src_img[i] > max_percentile_pixel) {
            dst_img[i] = max_percentile_pixel;
        } else if (src_img[i] < min_percentile_pixel) {
            dst_img[i] = min_percentile_pixel;
        } else {
            dst_img[i] = src_img[i];
        }
        dst_img[i] = (dst_img[i] * 1.f - pixel_min) / (pixel_max - pixel_min) * (255 * 0.8) + 255 * 0.1;
    }

    return;
}

void get_rotation_matrix(float angle, float scale, float dx, float dy, float* tm) {
    angle *= (float)(3.14159265358979323846 / 180);
    float alpha = cos(angle) * scale;
    float beta  = sin(angle) * scale;

    tm[0] = alpha;
    tm[1] = beta;
    tm[2] = (1.f - alpha) * dx - beta * dy;
    tm[3] = -beta;
    tm[4] = alpha;
    tm[5] = beta * dx + (1.f - alpha) * dy;
}

void invert_affine_transform(const float* tm, float* tm_inv) {
    float D = tm[0] * tm[4] - tm[1] * tm[3];
    D       = D != 0.f ? 1.f / D : 0.f;

    float A11 = tm[4] * D;
    float A22 = tm[0] * D;
    float A12 = -tm[1] * D;
    float A21 = -tm[3] * D;
    float b1  = -A11 * tm[2] - A12 * tm[5];
    float b2  = -A21 * tm[2] - A22 * tm[5];

    tm_inv[0] = A11;
    tm_inv[1] = A12;
    tm_inv[2] = b1;
    tm_inv[3] = A21;
    tm_inv[4] = A22;
    tm_inv[5] = b2;
}

// #undef __ARM_NEON
void warpaffine_bilinear_c3(const unsigned char* src, int srcw, int srch, int srcstride, unsigned char* dst, int w, int h, int stride, const float* tm, int type, unsigned int v) {
    const unsigned char* border_color = (const unsigned char*)&v;
    const int            wgap         = stride - w * 3;

    const unsigned char* src0 = src;
    unsigned char*       dst0 = dst;

#define SATURATE_CAST_SHORT(X) (short)::std::min(::std::max((int)(X), SHRT_MIN), SHRT_MAX)
#define SATURATE_CAST_INT(X)   (int)::std::min(::std::max((int)((X) + ((X) >= 0.f ? 0.5f : -0.5f)), INT_MIN), INT_MAX)

    std::vector<int> adelta(w);
    std::vector<int> bdelta(w);
    for (int x = 0; x < w; x++) {
        adelta[x] = SATURATE_CAST_INT(tm[0] * x * (1 << 10));
        bdelta[x] = SATURATE_CAST_INT(tm[3] * x * (1 << 10));
    }

    int y = 0;
    for (; y < h; y++) {
        int X0 = SATURATE_CAST_INT((tm[1] * y + tm[2]) * (1 << 10));
        int Y0 = SATURATE_CAST_INT((tm[4] * y + tm[5]) * (1 << 10));

        int x = 0;
        for (; x + 7 < w; x += 8) {
            int sxy_inout = 0;
            {
                int X_0 = X0 + adelta[x];
                int Y_0 = Y0 + bdelta[x];
                int X_7 = X0 + adelta[x + 7];
                int Y_7 = Y0 + bdelta[x + 7];

                short sx_0 = SATURATE_CAST_SHORT((X_0 >> 10));
                short sy_0 = SATURATE_CAST_SHORT((Y_0 >> 10));
                short sx_7 = SATURATE_CAST_SHORT((X_7 >> 10));
                short sy_7 = SATURATE_CAST_SHORT((Y_7 >> 10));

                if (((unsigned short)sx_0 < srcw - 1 && (unsigned short)sy_0 < srch - 1) && ((unsigned short)sx_7 < srcw - 1 && (unsigned short)sy_7 < srch - 1)) {
                    // all inside
                    sxy_inout = 1;
                } else if ((sx_0 < -1 && sx_7 < -1) || (sx_0 >= srcw && sx_7 >= srcw) || (sy_0 < -1 && sy_7 < -1) || (sy_0 >= srch && sy_7 >= srch)) {
                    // all outside
                    sxy_inout = 2;
                }
            }

            if (sxy_inout == 1) {
                // all inside
#if __ARM_NEON
                int32x4_t _Xl = vaddq_s32(vdupq_n_s32(X0), vld1q_s32(adelta.data() + x));
                int32x4_t _Xh = vaddq_s32(vdupq_n_s32(X0), vld1q_s32(adelta.data() + x + 4));
                int32x4_t _Yl = vaddq_s32(vdupq_n_s32(Y0), vld1q_s32(bdelta.data() + x));
                int32x4_t _Yh = vaddq_s32(vdupq_n_s32(Y0), vld1q_s32(bdelta.data() + x + 4));

                int16x4_t _sxl = vqshrn_n_s32(_Xl, 10);
                int16x4_t _sxh = vqshrn_n_s32(_Xh, 10);
                int16x4_t _syl = vqshrn_n_s32(_Yl, 10);
                int16x4_t _syh = vqshrn_n_s32(_Yh, 10);

                uint32x4_t _v1024m1 = vdupq_n_u32((1 << 10) - 1);
                uint16x8_t _fx      = vcombine_u16(vmovn_u32(vandq_u32(vreinterpretq_u32_s32(_Xl), _v1024m1)), vmovn_u32(vandq_u32(vreinterpretq_u32_s32(_Xh), _v1024m1)));
                uint16x8_t _fy      = vcombine_u16(vmovn_u32(vandq_u32(vreinterpretq_u32_s32(_Yl), _v1024m1)), vmovn_u32(vandq_u32(vreinterpretq_u32_s32(_Yh), _v1024m1)));

                uint16x8_t _alpha0 = vsubq_u16(vdupq_n_u16(1 << 10), _fx);
                uint16x8_t _alpha1 = _fx;
                uint16x8_t _beta0  = vsubq_u16(vdupq_n_u16(1 << 10), _fy);
                uint16x8_t _beta1  = _fy;

                int16x4_t _srcstride = vdup_n_s16(srcstride);
                int16x4_t _v3        = vdup_n_s16(3);

                int32x4_t _a0l = vmlal_s16(vmull_s16(_srcstride, _syl), _sxl, _v3);
                int32x4_t _a0h = vmlal_s16(vmull_s16(_srcstride, _syh), _sxh, _v3);
                int32x4_t _b0l = vaddw_s16(_a0l, _srcstride);
                int32x4_t _b0h = vaddw_s16(_a0h, _srcstride);
                int32x4_t _a1l = vaddw_s16(_a0l, _v3);
                int32x4_t _a1h = vaddw_s16(_a0h, _v3);
                int32x4_t _b1l = vaddw_s16(_b0l, _v3);
                int32x4_t _b1h = vaddw_s16(_b0h, _v3);

                uint8x8x3_t _a0 = uint8x8x3_t();
                uint8x8x3_t _a1 = uint8x8x3_t();
                uint8x8x3_t _b0 = uint8x8x3_t();
                uint8x8x3_t _b1 = uint8x8x3_t();
                {
                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0l, 0), _a0, 0);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1l, 0), _a1, 0);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0l, 0), _b0, 0);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1l, 0), _b1, 0);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0l, 1), _a0, 1);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1l, 1), _a1, 1);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0l, 1), _b0, 1);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1l, 1), _b1, 1);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0l, 2), _a0, 2);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1l, 2), _a1, 2);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0l, 2), _b0, 2);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1l, 2), _b1, 2);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0l, 3), _a0, 3);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1l, 3), _a1, 3);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0l, 3), _b0, 3);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1l, 3), _b1, 3);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0h, 0), _a0, 4);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1h, 0), _a1, 4);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0h, 0), _b0, 4);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1h, 0), _b1, 4);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0h, 1), _a0, 5);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1h, 1), _a1, 5);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0h, 1), _b0, 5);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1h, 1), _b1, 5);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0h, 2), _a0, 6);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1h, 2), _a1, 6);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0h, 2), _b0, 6);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1h, 2), _b1, 6);

                    _a0 = vld3_lane_u8(src0 + vgetq_lane_s32(_a0h, 3), _a0, 7);
                    _a1 = vld3_lane_u8(src0 + vgetq_lane_s32(_a1h, 3), _a1, 7);
                    _b0 = vld3_lane_u8(src0 + vgetq_lane_s32(_b0h, 3), _b0, 7);
                    _b1 = vld3_lane_u8(src0 + vgetq_lane_s32(_b1h, 3), _b1, 7);
                }

                uint16x8_t _a0_0 = vmovl_u8(_a0.val[0]);
                uint16x8_t _a0_1 = vmovl_u8(_a0.val[1]);
                uint16x8_t _a0_2 = vmovl_u8(_a0.val[2]);
                uint16x8_t _a1_0 = vmovl_u8(_a1.val[0]);
                uint16x8_t _a1_1 = vmovl_u8(_a1.val[1]);
                uint16x8_t _a1_2 = vmovl_u8(_a1.val[2]);
                uint16x8_t _b0_0 = vmovl_u8(_b0.val[0]);
                uint16x8_t _b0_1 = vmovl_u8(_b0.val[1]);
                uint16x8_t _b0_2 = vmovl_u8(_b0.val[2]);
                uint16x8_t _b1_0 = vmovl_u8(_b1.val[0]);
                uint16x8_t _b1_1 = vmovl_u8(_b1.val[1]);
                uint16x8_t _b1_2 = vmovl_u8(_b1.val[2]);

                uint16x4_t _a00_0l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_a0_0), vget_low_u16(_alpha0)), vget_low_u16(_a1_0), vget_low_u16(_alpha1)), 5);
                uint16x4_t _a00_1l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_a0_1), vget_low_u16(_alpha0)), vget_low_u16(_a1_1), vget_low_u16(_alpha1)), 5);
                uint16x4_t _a00_2l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_a0_2), vget_low_u16(_alpha0)), vget_low_u16(_a1_2), vget_low_u16(_alpha1)), 5);
                uint16x4_t _a00_0h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_a0_0), vget_high_u16(_alpha0)), vget_high_u16(_a1_0), vget_high_u16(_alpha1)), 5);
                uint16x4_t _a00_1h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_a0_1), vget_high_u16(_alpha0)), vget_high_u16(_a1_1), vget_high_u16(_alpha1)), 5);
                uint16x4_t _a00_2h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_a0_2), vget_high_u16(_alpha0)), vget_high_u16(_a1_2), vget_high_u16(_alpha1)), 5);
                uint16x4_t _b00_0l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_b0_0), vget_low_u16(_alpha0)), vget_low_u16(_b1_0), vget_low_u16(_alpha1)), 5);
                uint16x4_t _b00_1l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_b0_1), vget_low_u16(_alpha0)), vget_low_u16(_b1_1), vget_low_u16(_alpha1)), 5);
                uint16x4_t _b00_2l = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_low_u16(_b0_2), vget_low_u16(_alpha0)), vget_low_u16(_b1_2), vget_low_u16(_alpha1)), 5);
                uint16x4_t _b00_0h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_b0_0), vget_high_u16(_alpha0)), vget_high_u16(_b1_0), vget_high_u16(_alpha1)), 5);
                uint16x4_t _b00_1h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_b0_1), vget_high_u16(_alpha0)), vget_high_u16(_b1_1), vget_high_u16(_alpha1)), 5);
                uint16x4_t _b00_2h = vqshrn_n_u32(vmlal_u16(vmull_u16(vget_high_u16(_b0_2), vget_high_u16(_alpha0)), vget_high_u16(_b1_2), vget_high_u16(_alpha1)), 5);

                uint16x4_t _dst_0l = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_0l, vget_low_u16(_beta0)), _b00_0l, vget_low_u16(_beta1)), 15);
                uint16x4_t _dst_1l = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_1l, vget_low_u16(_beta0)), _b00_1l, vget_low_u16(_beta1)), 15);
                uint16x4_t _dst_2l = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_2l, vget_low_u16(_beta0)), _b00_2l, vget_low_u16(_beta1)), 15);
                uint16x4_t _dst_0h = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_0h, vget_high_u16(_beta0)), _b00_0h, vget_high_u16(_beta1)), 15);
                uint16x4_t _dst_1h = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_1h, vget_high_u16(_beta0)), _b00_1h, vget_high_u16(_beta1)), 15);
                uint16x4_t _dst_2h = vqshrn_n_u32(vmlal_u16(vmull_u16(_a00_2h, vget_high_u16(_beta0)), _b00_2h, vget_high_u16(_beta1)), 15);

                uint8x8x3_t _dst;
                _dst.val[0] = vqmovn_u16(vcombine_u16(_dst_0l, _dst_0h));
                _dst.val[1] = vqmovn_u16(vcombine_u16(_dst_1l, _dst_1h));
                _dst.val[2] = vqmovn_u16(vcombine_u16(_dst_2l, _dst_2h));

                vst3_u8(dst0, _dst);

                dst0 += 3 * 8;
#else
                for (int xi = 0; xi < 8; xi++) {
                    int X = X0 + adelta[x + xi];
                    int Y = Y0 + bdelta[x + xi];

                    short sx = SATURATE_CAST_SHORT((X >> 10));
                    short sy = SATURATE_CAST_SHORT((Y >> 10));

                    short fx = X & ((1 << 10) - 1);
                    short fy = Y & ((1 << 10) - 1);

                    short alpha0 = (1 << 10) - fx;
                    short alpha1 = fx;

                    short beta0 = (1 << 10) - fy;
                    short beta1 = fy;

                    const unsigned char* a0             = src0 + srcstride * sy + sx * 3;
                    const unsigned char* output_img_hwc = src0 + srcstride * sy + sx * 3 + 3;
                    const unsigned char* b0             = src0 + srcstride * (sy + 1) + sx * 3;
                    const unsigned char* b1             = src0 + srcstride * (sy + 1) + sx * 3 + 3;

                    dst0[0] = (unsigned char)(((((unsigned short)((a0[0] * alpha0 + output_img_hwc[0] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[0] * alpha0 + b1[0] * alpha1) >> 5) * beta1))) >> 15);
                    dst0[1] = (unsigned char)(((((unsigned short)((a0[1] * alpha0 + output_img_hwc[1] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[1] * alpha0 + b1[1] * alpha1) >> 5) * beta1))) >> 15);
                    dst0[2] = (unsigned char)(((((unsigned short)((a0[2] * alpha0 + output_img_hwc[2] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[2] * alpha0 + b1[2] * alpha1) >> 5) * beta1))) >> 15);

                    dst0 += 3;
                }
#endif // __ARM_NEON
            } else if (sxy_inout == 2) {
                // all outside
                if (type != -233) {
#if __ARM_NEON
                    uint8x8x3_t _border_color;
                    _border_color.val[0] = vdup_n_u8(border_color[0]);
                    _border_color.val[1] = vdup_n_u8(border_color[1]);
                    _border_color.val[2] = vdup_n_u8(border_color[2]);

                    vst3_u8(dst0, _border_color);
#else
                    for (int xi = 0; xi < 8; xi++) {
                        dst0[xi * 3]     = border_color[0];
                        dst0[xi * 3 + 1] = border_color[1];
                        dst0[xi * 3 + 2] = border_color[2];
                    }
#endif // __ARM_NEON
                } else {
                    // skip
                }

                dst0 += 24;
            } else // if (sxy_inout == 0)
            {
                for (int xi = 0; xi < 8; xi++) {
                    int X = X0 + adelta[x + xi];
                    int Y = Y0 + bdelta[x + xi];

                    short sx = SATURATE_CAST_SHORT((X >> 10));
                    short sy = SATURATE_CAST_SHORT((Y >> 10));

                    if (type != -233 && (sx < -1 || sx >= srcw || sy < -1 || sy >= srch)) {
                        dst0[0] = border_color[0];
                        dst0[1] = border_color[1];
                        dst0[2] = border_color[2];
                    } else if (type == -233 && ((unsigned short)sx >= srcw - 1 || (unsigned short)sy >= srch - 1)) {
                        // skip
                    } else {
                        short fx = X & ((1 << 10) - 1);
                        short fy = Y & ((1 << 10) - 1);

                        short alpha0 = (1 << 10) - fx;
                        short alpha1 = fx;

                        short beta0 = (1 << 10) - fy;
                        short beta1 = fy;

                        short sx1 = sx + 1;
                        short sy1 = sy + 1;

                        const unsigned char* a0             = src0 + srcstride * sy + sx * 3;
                        const unsigned char* output_img_hwc = src0 + srcstride * sy + sx * 3 + 3;
                        const unsigned char* b0             = src0 + srcstride * (sy + 1) + sx * 3;
                        const unsigned char* b1             = src0 + srcstride * (sy + 1) + sx * 3 + 3;

                        if ((unsigned short)sx >= srcw || (unsigned short)sy >= srch) {
                            a0 = type != -233 ? border_color : dst0;
                        }
                        if ((unsigned short)sx1 >= srcw || (unsigned short)sy >= srch) {
                            output_img_hwc = type != -233 ? border_color : dst0;
                        }
                        if ((unsigned short)sx >= srcw || (unsigned short)sy1 >= srch) {
                            b0 = type != -233 ? border_color : dst0;
                        }
                        if ((unsigned short)sx1 >= srcw || (unsigned short)sy1 >= srch) {
                            b1 = type != -233 ? border_color : dst0;
                        }

                        dst0[0] = (unsigned char)(((((unsigned short)((a0[0] * alpha0 + output_img_hwc[0] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[0] * alpha0 + b1[0] * alpha1) >> 5) * beta1))) >> 15);
                        dst0[1] = (unsigned char)(((((unsigned short)((a0[1] * alpha0 + output_img_hwc[1] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[1] * alpha0 + b1[1] * alpha1) >> 5) * beta1))) >> 15);
                        dst0[2] = (unsigned char)(((((unsigned short)((a0[2] * alpha0 + output_img_hwc[2] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[2] * alpha0 + b1[2] * alpha1) >> 5) * beta1))) >> 15);
                    }

                    dst0 += 3;
                }
            }
        }
        for (; x < w; x++) {
            int X = X0 + adelta[x];
            int Y = Y0 + bdelta[x];

            short sx = SATURATE_CAST_SHORT((X >> 10));
            short sy = SATURATE_CAST_SHORT((Y >> 10));

            if (type != -233 && (sx < -1 || sx >= srcw || sy < -1 || sy >= srch)) {
                dst0[0] = border_color[0];
                dst0[1] = border_color[1];
                dst0[2] = border_color[2];
            } else if (type == -233 && ((unsigned short)sx >= srcw - 1 || (unsigned short)sy >= srch - 1)) {
                // skip
            } else {
                short fx = X & ((1 << 10) - 1);
                short fy = Y & ((1 << 10) - 1);

                short alpha0 = (1 << 10) - fx;
                short alpha1 = fx;

                short beta0 = (1 << 10) - fy;
                short beta1 = fy;

                short sx1 = sx + 1;
                short sy1 = sy + 1;

                const unsigned char* a0             = src0 + srcstride * sy + sx * 3;
                const unsigned char* output_img_hwc = src0 + srcstride * sy + sx * 3 + 3;
                const unsigned char* b0             = src0 + srcstride * (sy + 1) + sx * 3;
                const unsigned char* b1             = src0 + srcstride * (sy + 1) + sx * 3 + 3;

                if ((unsigned short)sx >= srcw || (unsigned short)sy >= srch) {
                    a0 = type != -233 ? border_color : dst0;
                }
                if ((unsigned short)sx1 >= srcw || (unsigned short)sy >= srch) {
                    output_img_hwc = type != -233 ? border_color : dst0;
                }
                if ((unsigned short)sx >= srcw || (unsigned short)sy1 >= srch) {
                    b0 = type != -233 ? border_color : dst0;
                }
                if ((unsigned short)sx1 >= srcw || (unsigned short)sy1 >= srch) {
                    b1 = type != -233 ? border_color : dst0;
                }

                dst0[0] = (unsigned char)(((((unsigned short)((a0[0] * alpha0 + output_img_hwc[0] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[0] * alpha0 + b1[0] * alpha1) >> 5) * beta1))) >> 15);
                dst0[1] = (unsigned char)(((((unsigned short)((a0[1] * alpha0 + output_img_hwc[1] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[1] * alpha0 + b1[1] * alpha1) >> 5) * beta1))) >> 15);
                dst0[2] = (unsigned char)(((((unsigned short)((a0[2] * alpha0 + output_img_hwc[2] * alpha1) >> 5) * beta0)) + (((unsigned short)((b0[2] * alpha0 + b1[2] * alpha1) >> 5) * beta1))) >> 15);
            }

            dst0 += 3;
        }

        dst0 += wgap;
    }
#undef SATURATE_CAST_SHORT
#undef SATURATE_CAST_INT
}

void RGB2Gray(const unsigned char* source, int w, int h, unsigned char* dest) {
    // coeffs for r g b = 0.299f, 0.587f, 0.114f
    // const unsigned char Y_shift = 8;//14
    // const unsigned char R2Y = 77;
    // const unsigned char G2Y = 150;
    // const unsigned char B2Y = 29;

    // unsigned char* ptr = gray;
    // int size = w * h;
    // int remain = size;
    // for (; remain>0; remain--)
    // {
    //     *ptr = (rgb[0] * R2Y + rgb[1] * G2Y + rgb[2] * B2Y) >> Y_shift;

    //     rgb += 3;
    //     ptr++;
    // }

    int sta   = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld3_u8(source + 24 * i);
            auto res   = vmull_u8(rC, rgb.val[0]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[2]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        int r = source[3 * i + 0];
        int g = source[3 * i + 1];
        int b = source[3 * i + 2];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

void BGR2Gray(const unsigned char* source, int w, int h, unsigned char* dest) {
    // coeffs for r g b = 0.299f, 0.587f, 0.114f
    // const unsigned char Y_shift = 8;//14
    // const unsigned char R2Y = 77;
    // const unsigned char G2Y = 150;
    // const unsigned char B2Y = 29;

    // unsigned char* ptr = gray;
    // int size = w * h;
    // int remain = size;
    // for (; remain>0; remain--)
    // {
    //     *ptr = (bgr[2] * R2Y + bgr[1] * G2Y + bgr[0] * B2Y) >> Y_shift;

    //     bgr += 3;
    //     ptr++;
    // }

    int sta   = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld3_u8(source + 24 * i);
            auto res   = vmull_u8(rC, rgb.val[2]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[0]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif

    for (int i = sta; i < count; ++i) {
        int r = source[3 * i + 2];
        int g = source[3 * i + 1];
        int b = source[3 * i + 0];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

int rotate_c3(unsigned char* inImg, unsigned char* outImg, int in_width, int in_height, int type) {
    switch (type) {
    case 0:
        memcpy(outImg, inImg, in_width * in_height * 3 * sizeof(unsigned char));
        break;
    case 1: /// 90
        for (int i = 0; i < in_width; i++) {
            for (int j = 0; j < in_height; j++) {
                outImg[(i * in_height + j) * 3]     = inImg[((in_height - 1 - j) * in_width + i) * 3];
                outImg[(i * in_height + j) * 3 + 1] = inImg[((in_height - 1 - j) * in_width + i) * 3 + 1];
                outImg[(i * in_height + j) * 3 + 2] = inImg[((in_height - 1 - j) * in_width + i) * 3 + 2];
            }
        }
        break;
    case 2: ///180
        for (int i = 0; i < in_width; i++) {
            for (int j = 0; j < in_height; j++) {
                outImg[(i * in_height + j) * 3]     = inImg[((in_width - 1 - i) * in_height + (in_height - 1 - j)) * 3];
                outImg[(i * in_height + j) * 3 + 1] = inImg[((in_width - 1 - i) * in_height + (in_height - 1 - j)) * 3 + 1];
                outImg[(i * in_height + j) * 3 + 2] = inImg[((in_width - 1 - i) * in_height + (in_height - 1 - j)) * 3 + 2];
            }
        }
        break;
    case 3: /// 270
        for (int i = 0; i < in_width; i++) {
            for (int j = 0; j < in_height; j++) {
                outImg[(i * in_height + j) * 3]     = inImg[(j * in_width + (in_width - 1 - i)) * 3];
                outImg[(i * in_height + j) * 3 + 1] = inImg[(j * in_width + (in_width - 1 - i)) * 3 + 1];
                outImg[(i * in_height + j) * 3 + 2] = inImg[(j * in_width + (in_width - 1 - i)) * 3 + 2];
            }
        }
        break;
    default:
        memcpy(outImg, inImg, in_width * in_height * 3 * sizeof(unsigned char));
        break;
    }

    return 0;
}

void bstFeatureCompare(
    const float* const feature1,  /* Feature */
    const float* const feature2,  /* Feature */
    int                dimension, /* Dimension of Feature */
    float&      score      /* Score */
){
    score = 0.;
    for (int i = 0; i < dimension; i++) {
        score += feature1[i] * feature2[i];
    }
    return;
}





// #ifndef SECUREOS
//彩色图直方图均衡
void ColorHistogramEqualizationOriginal(unsigned char* img, int biHeight, int biWidth) {
    const int L = 256;  // 像素值的取值范围为[0, L)
    int hist[L] = {0};  // 直方图
    int cdf[L] = {0};   // 累积分布函数
    int total_pixels = biHeight * biWidth;

    // 1. 计算亮度直方图
    for (int i = 0; i < biHeight; ++i) {
        for (int j = 0; j < biWidth; ++j) {
            int idx = (i * biWidth + j) * 3;
            // 使用标准的亮度转换公式
            int Y = (int)(0.299f * img[idx + 2] + 0.587f * img[idx + 1] + 0.114f * img[idx]);
            hist[Y]++;
        }
    }

    // 2. 计算累积分布函数
    cdf[0] = hist[0];
    for (int i = 1; i < L; ++i) {
        cdf[i] = cdf[i-1] + hist[i];
    }

    // 3. 创建查找表
    unsigned char lookup[L];
    for (int i = 0; i < L; ++i) {
        lookup[i] = (unsigned char)((cdf[i] * 255.0f) / total_pixels);
    }

    // 4. 应用直方图均衡化
    for (int i = 0; i < biHeight; ++i) {
        for (int j = 0; j < biWidth; ++j) {
            int idx = (i * biWidth + j) * 3;
            unsigned char r = img[idx + 2];
            unsigned char g = img[idx + 1];
            unsigned char b = img[idx];

            // 转换到YUV空间
            float Y = 0.299f * r + 0.587f * g + 0.114f * b;
            float U = -0.147f * r - 0.289f * g + 0.436f * b;
            float V = 0.615f * r - 0.515f * g - 0.100f * b;

            // 对Y通道进行均衡化
            float Y_eq = lookup[(int)Y];

            // 转换回RGB空间
            float r_new = Y_eq + 1.140f * V;
            float g_new = Y_eq - 0.395f * U - 0.581f * V;
            float b_new = Y_eq + 2.032f * U;

            // 确保值在有效范围内
            img[idx + 2] = (unsigned char)min(255.0f, max(0.0f, r_new));
            img[idx + 1] = (unsigned char)min(255.0f, max(0.0f, g_new));
            img[idx]     = (unsigned char)min(255.0f, max(0.0f, b_new));
        }
    }
}


// log
#ifndef SECUREOS
#ifdef __ANDROID__
bool g_print_info = false;
void update_log_setting(bool always_on) {
    if (always_on) {
        g_print_info = true;
    } else {
        char prop[PROP_VALUE_MAX] = {0};
        __system_property_get("debug.bstfu.log.enable", prop);

        if (prop[0] == 0x31)
            g_print_info = true;
        else
            g_print_info = false;
    }
}
#else // not __ANDROID__
void update_log_setting(bool always_on) {
}
#endif
#else // SECUREOS = ON
void update_log_setting(bool always_on) {
}
#endif


// USM锐化
void USMSharpening(unsigned char* img, int height, int width, float amount = 0.5f, float radius = 1.0f, float threshold = 0.0f) {
    // 参数验证
    if (img == nullptr) {
         return;
    }
    if (height <= 0 || width <= 0) {
         return;
    }
    if (amount < 0.0f || amount > 5.0f) {
         return;
    }
    if (radius <= 0.0f || radius > 10.0f) {
         return;
    }
    if (threshold < 0.0f || threshold > 255.0f) {
        return;
    }

    // 创建临时缓冲区
    unsigned char* temp = nullptr;
    
    temp = new unsigned char[height * width * 3];
    if (temp == nullptr) {
         return;
    }
    memcpy(temp, img, height * width * 3);

    // 高斯模糊核
    const int kernelSize = 5;
    const float sigma = radius;
    float kernel[kernelSize][kernelSize];
    float sum = 0.0f;
    

    // 计算高斯核
    for (int i = -2; i <= 2; i++) {
        for (int j = -2; j <= 2; j++) {
            kernel[i+2][j+2] = exp(-(i*i + j*j) / (2 * sigma * sigma));
            sum += kernel[i+2][j+2];
        }
    }
    
    // 归一化核
    if (sum < 1e-6f) {
        delete[] temp;
        return;
    }
    for (int i = 0; i < kernelSize; i++) {
        for (int j = 0; j < kernelSize; j++) {
            kernel[i][j] /= sum;
        }
    }
    
    // 应用USM锐化
    for (int i = 2; i < height - 2; i++) {
        for (int j = 2; j < width - 2; j++) {
            for (int c = 0; c < 3; c++) {
                float blur = 0.0f;
                // 应用高斯模糊
                for (int ky = -2; ky <= 2; ky++) {
                    for (int kx = -2; kx <= 2; kx++) {
                        int idx = ((i + ky) * width + (j + kx)) * 3 + c;
                        if (idx < 0 || idx >= height * width * 3) {
                            delete[] temp;
                            return;
                        }
                        blur += temp[idx] * kernel[ky+2][kx+2];
                    }
                }
                
                int idx = (i * width + j) * 3 + c;
                if (idx < 0 || idx >= height * width * 3) {
                     delete[] temp;
                     return;
                }
                float diff = temp[idx] - blur;
                
                // 应用阈值
                if (abs(diff) > threshold) {
                    float sharp = temp[idx] + amount * diff;
                    img[idx] = (unsigned char)min(255.0f, max(0.0f, sharp));
                }
            }
        }
    }

    
    delete[] temp;
}

// 修改ColorHistogramEqualization函数
void ColorHistogramEqualization(unsigned char* img, int biHeight, int biWidth, bool do_sharpening, float sharpening_strength) {
    // 参数验证
    if (img == nullptr) {
        return;
    }
    if (biHeight <= 0 || biWidth <= 0) {
         return;
    }
    if (do_sharpening && (sharpening_strength < 0.0f || sharpening_strength > 5.0f)) {
        return;
    }


    // 应用原始直方图均衡化
    ColorHistogramEqualizationOriginal(img, biHeight, biWidth);
    
    // 如果需要，应用USM锐化
    if (do_sharpening) {
        USMSharpening(img, biHeight, biWidth, sharpening_strength, 1.0f, 5.0f);
    }
    return;
}


} // namespace bstutils
