// BSTFaceUnlockV2 demo

#include <cstdio>
#include <cstring>
#include <iostream>
#include <vector>
#include <stdio.h>

#include "BSTFaceUnlock.h"
#include "BSTMatch.h"

using namespace std;

// 半 TEE 项目中，enroll 获取的 feature 应该加密存储
vector<vector<float> > feature_database;

bool save_feature(const float* const feature) {
    vector<float> feature_vec(feature, feature + FEATURE_DIM_SDK);
    feature_database.push_back(feature_vec);
    return true;
}

bool readYUV(char const* imgpath, int width, int height, unsigned char* const enroll_img) {
    FILE* fp = fopen(imgpath, "rb+");
    if (NULL == fp) {
        return false;
    }
    fread(enroll_img, 1, width * height * 1.5, fp);
    fclose(fp);
    return true;
}

// 模拟从相机驱动获取帧
bool getFrame(const char* const img_path, int width, int height, unsigned char* const enroll_img) {
    return readYUV(img_path, width, height, enroll_img);
}

// 计算数据库中保存的特征值与解锁时提取的特征值的相似度
// 半 TEE 项目中，enroll 获取的 featreu 应该加密存储，如下调用 FaceMatch 的代码应该运行在 TEE 中
bool dummyMatch(const BSTFaceFeature* const feature_auth, int* user_id, float* score) {
    *score    = 0.;
    *user_id  = -1;
    float max = -100.;

    for (int i = 0; i < feature_database.size(); i++) {
        bstFaceMatch(feature_database[i].data(), feature_auth->feature, feature_auth->threshold, FEATURE_DIM_SDK, score);
        if (*score >= max) {
            max      = *score;
            *user_id = i;
        }
    }

    return true;
}

int run(int width, int height, const string& enroll_img_path, const string& auth_img_path, const string& cfg_path) {
    // 1. 获取版本号
    const BSTFaceUnlockVersion* version = bstFaceUnlockGetVersion();
    if (version != nullptr) {
        printf("BSTFaceUnlock build date : %s\n", version->build_date);
        printf("BSTFaceUnlock version : %s\n", version->lib_version);
    }

    // 2. 初始化
    BSTFaceUnlockConfig config;
    config.config_type     = BST_FU_CONFIG_TYPE_FILE;
    config.cfg.config_path = cfg_path.c_str();
    if (BST_FU_SUCCESS != bstFaceUnlockInit(&config)) {
        exit(-1);
    }

    // 3. 调用人脸注册
    unsigned char* frame = new unsigned char[int(width * height * 1.5)];
    printf("[MAIN] enroll...\n");
    while (true) { // 此处可以自定义超时逻辑做保护
        // 从相机获取帧
        if (!getFrame(enroll_img_path.c_str(), width, height, frame)) {
            exit(-1);
        }

        // 构造输入
        BSTFaceFeature     enroll_feature;
        BSTFaceUnlockImage enroll_image;
        enroll_image.img_data.data     = (void*)frame;
        enroll_image.img_data.format   = BST_IMAGE_TYPE_NV21;
        enroll_image.img_data.width    = width;
        enroll_image.img_data.height   = height;
        enroll_image.img_data.rotation = 270;
        enroll_image.img_data.stride   = width;

        int ret = bstFaceUnlockEnroll(&enroll_image, &enroll_feature);
        // 返回值按照十六进制第四位分类
        // 0x0000 表示成功（success）          0xE000 表示出现了重要错误（Fatal Error）
        // 0xF000 表示注册/解锁失败（Fail）     0xC000 表示需要继续进帧（continue）
        int ret_class = ret & 0xF000;
        printf("Enroll ret class : %x\n", ret_class);
        if (0xE000 == ret_class) {
            // 输入参数错误/内部逻辑错误
            printf("Fatal Error : %x\n", ret);
            exit(-1);
        } else if (0xF000 == ret_class) {
            // 注册超时/检测到攻击
            printf("Enroll Fail : %x\n", ret);
            break;
        } else if (BST_FU_SUCCESS == ret_class) {
            // 注册成功
            save_feature(enroll_feature.feature);
            break;
        } else if (0xC000 == ret_class) {
            // 需要更多输入帧
            printf("Continue : %x\n", ret);
            continue;
        } else {
            printf("Unexpected Ret Value : %x\n", ret);
            break;
        }
    }
    if (BST_FU_SUCCESS != bstFaceUnlockEnrollReset()) {
        exit(-1);
    }
    delete[] frame;

    //TODO: TMP TEST
    frame = new unsigned char[int(width * height * 1.5)];
    printf("[MAIN] enroll...\n");
    while (true) { // 此处可以自定义超时逻辑做保护
        // 从相机获取帧
        if (!getFrame(enroll_img_path.c_str(), width, height, frame)) {
            exit(-1);
        }

        // 构造输入
        BSTFaceFeature     enroll_feature;
        BSTFaceUnlockImage enroll_image;
        enroll_image.img_data.data     = (void*)frame;
        enroll_image.img_data.format   = BST_IMAGE_TYPE_NV21;
        enroll_image.img_data.width    = width;
        enroll_image.img_data.height   = height;
        enroll_image.img_data.rotation = 270;
        enroll_image.img_data.stride   = width;

        int ret = bstFaceUnlockEnroll(&enroll_image, &enroll_feature);
        // 返回值按照十六进制第四位分类
        // 0x0000 表示成功（success）          0xE000 表示出现了重要错误（Fatal Error）
        // 0xF000 表示注册/解锁失败（Fail）     0xC000 表示需要继续进帧（continue）
        int ret_class = ret & 0xF000;
        printf("Enroll ret class : %x\n", ret_class);
        if (0xE000 == ret_class) {
            // 输入参数错误/内部逻辑错误
            printf("Fatal Error : %x\n", ret);
            exit(-1);
        } else if (0xF000 == ret_class) {
            // 注册超时/检测到攻击
            printf("Enroll Fail : %x\n", ret);
            break;
        } else if (BST_FU_SUCCESS == ret_class) {
            // 注册成功
            save_feature(enroll_feature.feature);
            break;
        } else if (0xC000 == ret_class) {
            // 需要更多输入帧
            printf("Continue : %x\n", ret);
            continue;
        } else {
            printf("Unexpected Ret Value : %x\n", ret);
            break;
        }
    }
    if (BST_FU_SUCCESS != bstFaceUnlockEnrollReset()) {
        exit(-1);
    }
    delete[] frame;
    // END

    // 调用人脸解锁
    printf("[MAIN] auth...\n");
    frame = new unsigned char[int(width * height * 1.5)];
    while (true) { // 此处可以自定义超时逻辑做保护
        // 从相机获取帧
        if (!getFrame(enroll_img_path.c_str(), width, height, frame)) {
            exit(-1);
        }

        // 构造输入
        BSTFaceFeature     auth_feature;
        BSTFaceUnlockImage auth_image;
        auth_image.img_data.data     = (void*)frame;
        auth_image.img_data.format   = BST_IMAGE_TYPE_NV21;
        auth_image.img_data.width    = width;
        auth_image.img_data.height   = height;
        auth_image.img_data.rotation = 270;
        auth_image.img_data.stride   = width;

        int ret = bstFaceUnlockAuthenticate(&auth_image, &auth_feature);
        // 返回值按照十六进制第四位分类
        // 0x0000 表示成功（Success）          0xE000 表示出现了重要错误（Fatal Error）
        // 0xF000 表示注册/解锁失败（Fail）     0xC000 表示需要继续进帧（Continue）
        int ret_class = ret & 0xF000;
        printf("Auth ret class : %x\n", ret_class);
        if (0xE000 == ret_class) {
            // 输入参数错误/内部逻辑错误
            printf("Fatal Error : %x\n", ret);
            exit(-1);
        } else if (0xF000 == ret_class) {
            // 解锁超时/检测到攻击
            printf("Auth Fail : %x\n", ret);
            break;
        } else if (BST_FU_SUCCESS == ret_class) {
            // 逻辑上解锁不会返回该值
            printf("Unexpected Ret Value : %x\n", ret);
            break;
        } else if (0xC000 == ret_class) {
            if (BST_FU_CONTINUE == ret) {
                int   user_id = -1;
                float score   = -100.;
                dummyMatch(&auth_feature, &user_id, &score);
                if (-1 == user_id) {
                    // 需要更多输入帧
                    printf("Continue : %x\n", ret);
                    continue;
                } else {
                    // 解锁成功
                    printf("Auth Success, score : %f\n", score);
                    break;
                }
            } else {
                // 需要更多输入帧
                printf("Continue : %x", ret);
                continue;
            }
        } else {
            printf("Unexpected Ret Value : %x\n", ret);
            break;
        }
    }
    if (BST_FU_SUCCESS != bstFaceUnlockAuthenticateReset()) {
        exit(-1);
    }
    delete[] frame;

    // 反初始化
    if (BST_FU_SUCCESS != bstFaceUnlockUninit()) {
        exit(-1);
    }

    return 0;
}

int main(int argc, char const* argv[]) {
    if (argc < 4) {
        cerr << "[MAIN] "
             << "Usage : ./demo <enroll_img> <auth_img> <cfg_path> <width> <height>" << endl;
        return -1;
    }

    int width  = atoi(argv[4]);
    int height = atoi(argv[5]);

    return run(width, height, string(argv[1]), string(argv[2]), string(argv[3]));
}