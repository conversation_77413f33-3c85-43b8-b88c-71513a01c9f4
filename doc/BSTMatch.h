#ifndef _BSTMATCH_H_
#define _BSTMATCH_H_

#ifdef __cplusplus
extern "C" {
#endif

// clang-format off

#define _In_
#define _In_opt_
#define _Inout_
#define _Out_

// clang-format on

/* Compare two feature */
void bstFaceMatch(
    _In_ const float* const feature1,  /* Feature */
    _In_ const float* const feature2,  /* Feature */
    _In_ float              threshold, /* Threshold */
    _In_ int                dimension, /* Dimension of Feature */
    _Out_ float* const      score      /* Score */
);

#ifdef __cplusplus
}
#endif

#endif