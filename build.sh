#!/usr/bin/env bash

export ASAN_OPTIONS=detect_leaks=1
export LDFLAGS="-L/opt/homebrew/opt/llvm/lib/c++ -Wl,-rpath,/opt/homebrew/opt/llvm/lib/c++"
export PATH="/opt/homebrew/opt/llvm/bin:$PATH"
export LDFLAGS="-L/opt/homebrew/opt/llvm/lib"
export CPPFLAGS="-I/opt/homebrew/opt/llvm/include"

NDK=/Users/<USER>/tools/android-ndk-r25c
export PATH=/Users/<USER>/tools/teegris_sdk/teegris/source/bf_sdk/common/toolchains/aarch64-secureos-gnueabi-gcc_6_3-linux-x86/bin/:$PATH
export PATH=/media/liwei/14T/code/GitLab/bst_ta_build/tools/lc_bf_sdk/common/toolchains/aarch64-secureos-gnueabi-gcc_6_3-linux-x86/bin/:$PATH

if [ $# -eq 0 ]
then
    echo "build.sh [-c][-tg][-qs][-a][-as][-h]" 
    exit
fi

# -c    Clean
if [ $1 ] && [ $1 = "-c" ]
then
    echo "build clean ..."
    rm -rf ./build-android-*
    rm -rf ./build-secureos-*
    rm -rf ./build-linux-*
fi

# -s    Build Teegris SecureOS Target
if [ $1 ] && [ $1 = "-tg" ]
then
    echo "build secureos arm64-v8a ..."
    mkdir -p build-secureos-arm64-v8a
    pushd build-secureos-arm64-v8a
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=./cmake/aarch64-secureos-gnu-c.toolchain.cmake -DSECUREOS=ON ..
    make -j4
    # make install
    popd
fi

# -s    Build QSEE Target
if [ $1 ] && [ $1 = "-qs" ]
then
    echo "build qsee arm64-v8a ..."
    mkdir -p build-qsee-arm64-v8a
    pushd build-qsee-arm64-v8a
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=./cmake/aarch64-qsee-gnu-c.toolchain.cmake -DSECUREOS=ON ..
    make -j4
    # make install
    popd
fi

# -a    Build Android Target
if [ $1 ] && [ $1 = "-a" ]
then
    echo "build android arm64-v8a ..."
    mkdir -p build-android-arm64-v8a
    pushd build-android-arm64-v8a
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSECUREOS=OFF -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-28 ..
    make -j8
    cp ./src/libbstFaceUnlock.so ./src/libbstFaceUnlock.so.debug
    $NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/llvm-strip ./src/libbstFaceUnlock.so
    # $NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/llvm-strip ./FaceUnlockMatch/libbstMatch.so  
    # make install
    # popd
    # mkdir -p build-android-arm64-v8a-test
    # pushd build-android-arm64-v8a-test
    # cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-28  ../test
    # make -j1
    # # make install
    popd
    echo "build android armeabi-v7a ..."
    mkdir -p build-android-armeabi-v7a
    pushd build-android-armeabi-v7a
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSECUREOS=OFF -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="armeabi-v7a" -DANDROID_PLATFORM=android-28 ..
    make -j8
    $NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/llvm-strip ./src/libbstFaceUnlock.so
    popd
fi

# -as    Build Android Sim-SecureOS Target
if [ $1 ] && [ $1 = "-as" ]
then
    echo "build android-sim-secureos arm64-v8a ..."
    mkdir -p build-android-sim-secureos-arm64-v8a
    pushd build-android-sim-secureos-arm64-v8a
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSECUREOS=ON -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-28 ..
    make -j4
    # $NDK/toolchains/llvm/prebuilt/linux-x86_64/bin/llvm-strip ./FaceUnlockMatch/libbstMatch.so  
    # make install
    # popd
    # mkdir -p build-android-arm64-v8a-test
    # pushd build-android-arm64-v8a-test
    # cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DSIMSECUREOS=ON -DCMAKE_BUILD_TYPE="Release" -DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake -DANDROID_ABI="arm64-v8a" -DANDROID_PLATFORM=android-28  ../test
    # make -j1
    # # make install
    # popd
fi

# -d    Build Host Target
if [ $1 ] && [ $1 = "-h" ]
then
    echo "build host ..."
    mkdir -p build-host
    pushd build-host
    cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Debug" ..
    make -j4
    # make install
    # popd
    # mkdir -p build-linux-amd64-test
    # pushd build-linux-amd64-test
    # cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=1 -DCMAKE_BUILD_TYPE="Release" ../test
    # make -j1
    # # make install
    popd
fi
