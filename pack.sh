#!/usr/bin/env bash

VersionNum=$(cat cmake/Version.str)
DeviceTarget=$(cat cmake/DeviceTarget.str)
CurTime=$(date "+%Y-%m-%d")
Target=$(echo $VersionNum | sed 's/.*\(..\)$/\1/')

if [ $Target = "tg" ]; then 
AlgoName=BSTFaceAuthTEE
else
AlgoName=BSTFaceUnlock
fi

if [ $Target = "ht" ]; then
TargetStr="Host"
BuildPath="build-host"
LibName="libbstFaceUnlock.so"
echo "Target : $TargetStr"
echo "Exit ..."
exit
elif [ $Target = "ne" ]; then
TargetStr="android"
BuildPath="build-android-arm64-v8a"
LibName="libbstFaceUnlock.so"
elif [ $Target = "st" ]; then
TargetStr="android-sim-tee"
BuildPath="build-android-sim-secureos-arm64-v8a"
LibName="libbstFaceUnlock.so"
elif [ $Target = "tg" ]; then
TargetStr="teegris"
BuildPath="build-secureos-arm64-v8a"
LibName="libBSTFaceAuthTEE.a"
fi
echo "Target : $TargetStr"
echo "LibName : $LibName"
ReleaseDir=lib${AlgoName}-${VersionNum}-${CurTime}-${TargetStr}-Release
echo "Packing $ReleaseDir ..."
read -n1 -rp "Press any key to continue: " key

mkdir -p .release
rm -rf .release/SDK*
mkdir -p .release/SDK
if [ $Target = "tg" ]; then 
mkdir -p .release/SDK/libs/arm64-v8a
else
mkdir -p .release/SDK/libs/arm64-v8a
mkdir -p .release/SDK/libs/armeabi-v7a
fi
mkdir -p .release/SDK/demo
mkdir -p .release/SDK/src
mkdir -p .release/SDK/include

cp ./$BuildPath/src/$LibName .release/SDK/libs/arm64-v8a
if [ $Target = "tg" ]; then
cp ./lib/secureos-arm64-v8a/* .release/SDK/libs/arm64-v8a
else
cp ./build-android-armeabi-v7a/src/libbstFaceUnlock.so  .release/SDK/libs/armeabi-v7a
fi
if [ $Target = "tg" ]; then 
echo "pack tg libs"
else
mkdir -p .release/SDK/cfg
echo "copy faceunlock_config_${DeviceTarget}.json"
cp ./doc/faceunlock_config_${DeviceTarget}.json .release/SDK/cfg/bstFaceUnlock.cfg
fi
cp ./doc/BSTFaceUnlockAPI.pdf .release/SDK 
cp ./include/BSTFaceUnlock.h .release/SDK/include
cp ./doc/BSTMatch.cpp .release/SDK/src
cp ./doc/BSTMatch.h .release/SDK/src
cp ./test/demo.cpp .release/SDK/demo
cp ./doc/BSTFaceUnlockAPI.pdf .release/SDK

pushd .release/SDK
zip -r $ReleaseDir.zip ./*
mv ./*.zip ..
popd
