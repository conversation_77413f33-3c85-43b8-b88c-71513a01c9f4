cmake_minimum_required (VERSION 3.4)

project("bstFaceUnlockTest")

# build target
message(STATUS "host_system:		${CMAKE_SYSTEM_NAME}")
message(STATUS "version:		${VERSION_BASE}.${VERSION_MAJOR}.${VERSION_MINOR}.${VERSION_BUILD}")
if(ANDROID)
	message(STATUS "target_abi:		${ANDROID_ABI}")
	if(ANDROID_ABI STREQUAL "arm64-v8a")
		set(TARGET_PLATFORM "android-arm64-v8a")
	else()
		message(FATAL_ERROR "unsupported target platform")
	endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Linux")
	set(TARGET_PLATFORM "linux-amd64")
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
	set(TARGET_PLATFORM "darwin-arm64")
else()
	message("unsupported target platform")
endif()

# gcc/ld option #
if(TARGET_PLATFORM STREQUAL "linux-amd64")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}    \
		-fvisibility-inlines-hidden -fPIC -std=c++11 -lm")
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}    \
		-fvisibility-inlines-hidden -fPIC -std=c++11 -lm")
else()
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}   -O2 -Ofast \
		-fvisibility-inlines-hidden -fPIC -std=c++11")
endif()

# header/lib path
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)
if(TARGET_PLATFORM STREQUAL "linux-amd64")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../lib/linux-amd64)
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../build-linux-amd64/src)
elseif(TARGET_PLATFORM STREQUAL "darwin-arm64")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../lib/darwin-arm64)
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../build-darwin-arm64/src)
elseif(TARGET_PLATFORM STREQUAL "android-arm64-v8a")
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../lib/android-arm64-v8a)
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/../build-android-arm64-v8a/src)
elseif()
	message(FATAL_ERROR "unsupported target platform")
endif()

# linked libs
set(STATIC_LINK_LIBS
	BSTFaceAuthTEE
	BSTInferTEE
)

if(TARGET_PLATFORM STREQUAL "android-arm64-v8a")
    set(DYNAMIC_LINK_LIBS    
        log
    )
else()
	set(DYNAMIC_LINK_LIBS)
endif()

# build test
add_executable(bstFaceUnlockTest ./demo.cpp)
target_link_libraries(bstFaceUnlockTest ${STATIC_LINK_LIBS} ${DYNAMIC_LINK_LIBS})