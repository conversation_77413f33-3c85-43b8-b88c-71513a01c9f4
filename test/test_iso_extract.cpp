#include <iostream>
#include <string>
#include <cstdlib>

// 从文件名中提取ISO值
int extractISOFromFilename(const std::string& filename) {
    // 查找 "iso." 在文件名中的位置
    size_t iso_pos = filename.find("iso.");
    if (iso_pos == std::string::npos) {
        return 0; // 默认值
    }
    
    // 跳过 "iso." 找到ISO值
    size_t start_pos = iso_pos + 4; // "iso." 长度为4
    size_t end_pos = filename.find(".", start_pos);
    
    if (end_pos == std::string::npos) {
        // 如果后面没有点号，取到字符串末尾
        end_pos = filename.length();
    }
    
    std::string iso_str = filename.substr(start_pos, end_pos - start_pos);
    return std::atoi(iso_str.c_str());
}

int main() {
    std::string test_filename = "2025712_161841.sec_hl.0x66235.iso.1760.err.0xc00a";
    
    int iso_value = extractISOFromFilename(test_filename);
    
    std::cout << "文件名: " << test_filename << std::endl;
    std::cout << "提取的ISO值: " << iso_value << std::endl;
    
    // 测试其他格式的文件名
    std::string test_cases[] = {
        "2025712_161841.sec_hl.0x66235.iso.1760.err.0xc00a",
        "test.iso.800.nv21",
        "image.iso.1600",
        "no_iso_file.nv21",
        "iso.400.test.nv21"
    };
    
    std::cout << "\n测试多个文件名:" << std::endl;
    for (const auto& filename : test_cases) {
        int iso = extractISOFromFilename(filename);
        std::cout << filename << " -> ISO: " << iso << std::endl;
    }
    
    return 0;
} 