#include <iostream>
#include <cstdio>
#include <string>
#include <string.h>
#include <stdio.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <vector>
#include <iomanip>
#include <fstream>
#include <unistd.h>

#include "model/ModuleDetect.h"
#include "model/ModuleLandmarker.h"
#include "model/ModuleAlign.h"
#include "CommonUtils.h"
#include "CommonDef.h"


using namespace std;
#define MAX_PATH_LEN (1024)


int endswith(std::string s, std::string sub) {
    if (s.rfind(sub) == -1) {//排除出现类似s:23   sub:123的情况.
        return 0;
    }
    else {
        return s.rfind(sub) == (s.length() - sub.length()) ? 1 : 0;
    }
}

void GlobFiles(const char* path, std::vector<std::string>& fileList, std::string fileFormat) {

    DIR *d = NULL;
    struct dirent *dp = NULL;
    struct stat st;
    char p[MAX_PATH_LEN] = {0};

    if (stat(path, &st) < 0 || !S_ISDIR(st.st_mode)) {
        return;
    }

    if (!(d = opendir(path))) {
        return;
    }

    while ((dp = readdir(d)) != NULL) {
        if ((!strncmp(dp->d_name, ".", 1)) || (!strncmp(dp->d_name, "..", 2)))
            continue;

        snprintf(p, sizeof(p) - 1, "%s/%s", path, dp->d_name);
        stat(p, &st);
        if (!S_ISDIR(st.st_mode) && endswith(std::string(dp->d_name), fileFormat)) {
            // printf("%s/%s\n", path, dp->d_name);
            fileList.push_back(std::string(path) + "/" + std::string(dp->d_name));
        } else {
            std::vector<std::string> pp_temp;

            GlobFiles(p, pp_temp, fileFormat);
            for(int i = 0; i < pp_temp.size(); i++){
                fileList.push_back(pp_temp[i]);
            }
        }
    }
    closedir(d);
    return ;
}

bool readYUV(char const* imgpath, int width, int height, unsigned char* const enroll_img) {
    FILE* fp = fopen(imgpath, "rb+");
    if (NULL == fp) {
        return false;
    }
    fread(enroll_img, 1, width * height * 1.5, fp);
    fclose(fp);
    return true;
}

std::string Replace(const string &src, const char *from, const char *to)
{
    string::size_type pos = 0;
	string::size_type fromlen = strlen(from);
	string::size_type tolen = strlen(to);

    string str = src;
	pos = str.find(from,pos);
	while(pos != string::npos)
	{
		str.replace(pos,fromlen,to);
		pos = str.find(from,pos + tolen);
	}
    return str;
}

string GetBaseDir(const string &path)
{
    size_t pos = path.find_last_of('/');
    if (pos != string::npos)
    {
        return path.substr(0, pos);
    }
    return path;
}

string GetBasePath(const string &path)
{
    size_t pos = path.find_last_of('.');
    if (pos != string::npos)
    {
        return path.substr(0, pos);
    }
    return path;
}

bool IsFolderExist(const string &path)
{
    return !access(path.c_str(), F_OK);
}

void CreateFolders(const char* dir)
{
    char order[1000] = "mkdir -p ";
    strcat(order, dir);
    system(order);
}

int main(int argc, const char* argv[]){
    if (argc != 5){
        cout<<"Usage: ./demo_aligner <file_root> <file_ext> <width> <height>"<<endl;
        return -1;
    }
    string file_root = argv[1];
    string file_ext = argv[2];
    int width = atoi(argv[3]);
    int height = atoi(argv[4]);
    printf("width = %d, height = %d\n", width, height);

    ModuleDetect* g_detect = new ModuleDetect();
    int ret = g_detect->init();
    ModuleLandmarker* g_landmarker = new ModuleLandmarker();
    ret = g_landmarker->init();
    std::vector<std::string> file_list;
    GlobFiles(file_root.c_str(), file_list, file_ext);
    printf("img num = %d\n", file_list.size());
    int input_yuv_size = int(width * height * 1.5);
    unsigned char* input = new unsigned char[input_yuv_size];
    unsigned char* input_rotate = new unsigned char[input_yuv_size];
    int rotate_width = height;
    int rotate_height = width;
    unsigned char* input_rgb = new unsigned char[rotate_width * rotate_height * 3];
    // ncnn::Mat align_face(NORM_W, NORM_H, 3, 1);
    unsigned char* align_face = new unsigned char[NORM_W * NORM_H * 3];
    for (int i = 0; i < (int)file_list.size(); i++) {
        printf("%d/%d %s\n", i, file_list.size(), file_list[i].c_str());
        readYUV(file_list[i].c_str(), width, height, input);
        bstutils::rotate_yuv_nv21_270(input, input_rotate, width, height, width);
        bstutils::nv21_to_rgb_keepsize(input_rotate, rotate_width, rotate_height, rotate_width, rotate_width, input_rgb);
        std::vector<BBox> tmp_objects;
        g_detect->perfom(input_rgb, rotate_width, rotate_height, 3, tmp_objects, true, 0);
        if (tmp_objects.size() == 0){
            continue;
        }
        float max_area = -1;
        int max_idx = 0;
        for (int j = 0; j < (int)tmp_objects.size(); j++){
            if(tmp_objects[j].w*tmp_objects[j].h > max_area){
                max_area = tmp_objects[j].w*tmp_objects[j].h;
                max_idx = j;
            }
        }
        LmkInfo lmk_info;
        bool land_ret =g_landmarker->perfom((uint8_t*)input_rgb, rotate_width, rotate_height, CHANNEL, tmp_objects[max_idx], lmk_info);
        Bbox box;
        box.x1 = tmp_objects[max_idx].x;
        box.y1 = tmp_objects[max_idx].y;
        box.x2 = tmp_objects[max_idx].w + tmp_objects[max_idx].x;
        box.y2 = tmp_objects[max_idx].h + tmp_objects[max_idx].y;
        for (int i = 0; i < LMK_NUM; i++) {
            box.ppoint[i * 2 + 0] = lmk_info.pts[i].x;
            box.ppoint[i * 2 + 1] = lmk_info.pts[i].y;
        }
        std::vector<float> lmks;
        for (int j = 0; j < LMK_NUM; ++j) {
            lmks.push_back(box.ppoint[2 * j]);
            lmks.push_back(box.ppoint[2 * j + 1]);
        }
        uint8_t* p_src   = (uint8_t*)input_rgb;
        memset(align_face,0,NORM_W * NORM_H * NORM_C);
        if (!faceAlign((unsigned char*)input_rgb,rotate_width, rotate_height,3,(unsigned char*)align_face,NORM_W, NORM_H,box)) {
            LOGE("failed to get norm face!");
            // TODO: wrong return value
            continue;
        }
        string save_path = Replace(file_list[i], "/人脸识别验收测试集/", "/人脸识别验收测试集_aligned_cpp/");
        string save_dir = GetBaseDir(save_path);
        if (!IsFolderExist(save_dir)){
            CreateFolders(save_dir.c_str());
        }
        string base_path = GetBasePath(save_path);
        bstutils::save_image_png(base_path, align_face, 112, 112, 3);
    }

    delete[] input;
    delete[] input_rotate;
    delete[] input_rgb;
    delete[] align_face;

    if (g_detect != NULL) {
        g_detect->release();
        delete g_detect;
        g_detect = NULL;
    }

    if (g_landmarker != NULL) {
        g_landmarker->release();
        delete g_landmarker;
        g_landmarker = NULL;
    }

    return 0;
}