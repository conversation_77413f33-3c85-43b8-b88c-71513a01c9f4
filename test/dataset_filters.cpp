// BSTFaceUnlockV2 demo

#include <cstdio>
#include <cstring>
#include <iostream>
#include <vector>
#include <stdio.h>
#include <vector>
#include <cstring>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/time.h>
#include "BSTFaceUnlock.h"
#include "BSTMatch.h"
#include <filesystem> // 对文件路径名的操作
#include <opencv2/opencv.hpp>
#include <iostream>
#include <unistd.h>
using namespace std;
#define MAX_PATH_LEN (1024)

// 将cv::Mat转换为unsigned char*数组
unsigned char* matToUCharPtr(const cv::Mat& mat) {
    // 创建一个足够大的unsigned char数组来存储数据
    unsigned char* data = new unsigned char[mat.total() * mat.channels()];

    // 将cv::Mat中的数据复制到unsigned char数组中
    // 注意：这里假设mat是单通道的，如果mat是多通道的，需要调整
    for (int i = 0; i < mat.total(); ++i) {
        data[i] = static_cast<unsigned char>(mat.at<uchar>(i));
    }

    return data;
}

// 释放分配的内存
void freeUCharPtr(unsigned char* data) {
    delete[] data;
}
#define MAX_PATH 10000
int
create_parent_directory (const char *path)
{
    char buf[MAX_PATH + 1], *c;
    struct stat st;

    strcpy (buf, path);
    /* If not in the root dir. */
    if ((c = strrchr (buf, '/')) && c > buf + 1 && c[-1] != ':')
    {
        *c = '\0';
        /* Parent path already exists ... */
        if (!stat (buf, &st))
        {
            /* ...but is not a directory. */
            if (!S_ISDIR (st.st_mode))
                return -1;
            /* ...but isn't accessible for me. */
            if (access (buf, W_OK) &&
                chmod (buf, S_ISVTX | S_IRWXU | S_IRWXG | S_IRWXO))
                return -1;
            return 0;
        }
        /* If we can't create the dir, try to create the parent dir (recursive).
           If that fails or another try to create the dir, give up. */
        if (mkdir (buf, S_ISVTX | S_IRWXU | S_IRWXG | S_IRWXO) &&
            (create_parent_directory (buf) ||
             mkdir (buf, S_ISVTX | S_IRWXU | S_IRWXG | S_IRWXO)))
            return -1;
    }
    return 0;
}

inline std::string& replace_all_distinct(std::string& result_str, const std::string& old_value, const std::string& new_value)
{

    //        int len=strlen(str.c_str())+1;
    //        char * result_char=new char[len];
    //        (str,len,p);
    //        std::string result_str = std::string::c;
    std::string::size_type pos=0;
    while((pos=result_str.find(old_value,pos))!= std::string::npos)
    {
        result_str=result_str.replace(pos,old_value.length(),new_value);
        if(new_value.length()>0)
        {
            pos+=new_value.length();
        }
    }
    return result_str;

}

//
//// 将BGR图像转换为NV21格式
//void convertToNV21(const cv::Mat& bgrImage, cv::Mat& nv21Image,int &width,int &height) {
//    // 检查输入图像是否为BGR格式
//    if (bgrImage.channels() != 3) {
//        std::cerr << "Input image must be in BGR format." << std::endl;
//        return;
//    }
//
//    // 获取图像尺寸
//    width = bgrImage.cols;
//    height = bgrImage.rows;
//
//    // 确保宽度和高度为偶数
//    width = (width % 2 == 0) ? width : width + 1;
//    height = (height % 2 == 0) ? height : height + 1;
//
//    // 创建调整后的BGR图像
//    cv::Mat bgrImageAdjusted;
//    cv::resize(bgrImage, bgrImageAdjusted, cv::Size(width, height), 0, 0, cv::INTER_LINEAR);
//
//
//
//    int cols = bgrImageAdjusted.cols;
//    int rows = bgrImageAdjusted.rows;
//
//    int Yindex = 0;
//    int UVindex = rows * cols;
//    int buflen = int(1.5 * rows * cols);
//
////    unsigned char* yuvbuff = new unsigned char[buflen];
//
//
//    cvtColor(bgrImageAdjusted, nv21Image, CV_BGR2YUV_I420);
////    memcpy(pYuvBuf, nv21Image.data, buflen*sizeof(unsigned char));
////    cv::Mat OpencvImg;
////    cv::cvtColor(bgrImageAdjusted, nv21Image, CV_BGR2YUV_YV12);
////
////    int UVRow{ 0 };
////    for (int i=0;i<rows;i++)
////    {
////        for (int j=0;j<cols;j++)
////        {
////            uchar* YPointer = NV21.ptr<uchar>(i);
////
////            int B = bgrImageAdjusted.at<cv::Vec3b>(i, j)[0];
////            int G = bgrImageAdjusted.at<cv::Vec3b>(i, j)[1];
////            int R = bgrImageAdjusted.at<cv::Vec3b>(i, j)[2];
////
////            //计算Y的值
////            int Y = (77 * R + 150 * G + 29 * B) >> 8;
////            YPointer[j] = Y;
////            yuvbuff[Yindex++] = (Y < 0) ? 0 : ((Y > 255) ? 255 : Y);
////            uchar* UVPointer = NV21.ptr<uchar>(rows+i/2);
////            //计算U、V的值，进行2x2的采样
////            if (i%2==0&&(j)%2==0)
////            {
////                int U = ((-44 * R - 87 * G + 131 * B) >> 8) + 128;
////                int V = ((131 * R - 110 * G - 21 * B) >> 8) + 128;
////                UVPointer[j] = V;
////                UVPointer[j+1] = U;
////                yuvbuff[UVindex++] = (V < 0) ? 0 : ((V > 255) ? 255 : V);
////                yuvbuff[UVindex++] = (U < 0) ? 0 : ((U > 255) ? 255 : U);
////            }
////        }
////    }
//    return;
//}
void convertToNV21(const cv::Mat& bgrImage, cv::Mat& nv21,int &width,int &height) {
        // 检查输入图像是否为BGR格式
        if (bgrImage.channels() != 3) {
            std::cerr << "Input image must be in BGR format." << std::endl;
            return;
        }

        // 获取图像尺寸
        width = bgrImage.cols;
        height = bgrImage.rows;

        // 确保宽度和高度为偶数
        width = (width % 2 == 0) ? width : width + 1;
        height = (height % 2 == 0) ? height : height + 1;

        // 创建调整后的BGR图像
        cv::Mat bgr;
        cv::resize(bgrImage, bgr, cv::Size(width, height), 0, 0, cv::INTER_LINEAR);
    cv::Mat i420;
    cv::cvtColor(bgr, i420, cv::COLOR_BGR2YUV_I420);
    nv21.create(i420.rows, i420.cols, CV_8UC1);

    const int w = bgr.cols, h = bgr.rows;
    const int stride0 = i420.step[0];

    const uchar *i420_data0 = i420.data;
    const uchar *i420_data1 = i420_data0 + stride0 * h;
    const int i420_stride1 = (stride0 + 1) / 2;
    const uchar *i420_data2 = i420_data1 + (stride0 * h + 3) / 4;
    const int i420_stride2 = (stride0 + 1) / 2;

    uchar *nv21_data0 = nv21.data;
    uchar *nv21_data1 = nv21_data0 + stride0 * h;
    const int nv21_stride1 = stride0;

    // copy Y channel
    memcpy(nv21_data0, i420_data0, stride0 * h);

    // U,V
    for (int y = 0; y < (h + 1) / 2; y++) {
        uchar *nv21_row_uv = nv21_data1 + y * nv21_stride1;
        const uchar *i420_row_u = i420_data1 + y * i420_stride1;
        const uchar *i420_row_v = i420_data2 + y * i420_stride2;
        for (int x = 0; x < (w + 1) / 2; x++) {
            nv21_row_uv[x * 2 + 1] = i420_row_u[x];
            nv21_row_uv[x * 2 + 0] = i420_row_v[x];
        } // x
    }   // y
}

int endswith(std::string s, std::string sub) {
    if (s.rfind(sub) == -1) {//排除出现类似s:23   sub:123的情况.
        return 0;
    }
    else {
        return s.rfind(sub) == (s.length() - sub.length()) ? 1 : 0;
    }

}


void GlobFiles(const char* path, std::vector<std::string>& fileList, std::string fileFormat) {

    DIR *d = NULL;
    struct dirent *dp = NULL;
    struct stat st;
    char p[MAX_PATH_LEN] = {0};

    if (stat(path, &st) < 0 || !S_ISDIR(st.st_mode)) {
        return;
    }

    if (!(d = opendir(path))) {
        return;
    }

    while ((dp = readdir(d)) != NULL) {
        if ((!strncmp(dp->d_name, ".", 1)) || (!strncmp(dp->d_name, "..", 2)))
            continue;

        snprintf(p, sizeof(p) - 1, "%s/%s", path, dp->d_name);
        stat(p, &st);
        if (!S_ISDIR(st.st_mode) && endswith(std::string(dp->d_name), fileFormat)) {
            printf("%s/%s\n", path, dp->d_name);
            fileList.push_back(std::string(path) + "/" + std::string(dp->d_name));
        } else {
            std::vector<std::string> pp_temp;

            GlobFiles(p, pp_temp, fileFormat);
            for(int i = 0; i < pp_temp.size(); i++){
                fileList.push_back(pp_temp[i]);
            }
        }
    }
    closedir(d);
    return ;
}

// 第一个参数包含文件名(./a.txt)，第二个参数包含地址(./new/),文件名保留原文件名
bool move_file(std::string old_path_and_file_name, std::string new_path)
{

    std::string cmd = std::string("mv '") + old_path_and_file_name + "' '" + new_path + "'";
    std::cout<<cmd<<std::endl;
    system(cmd.c_str());
}


int run(const string& dst_path, const string& auth_img_path, const string& cfg_path) {
    int width;
    int height;
    // 1. 获取版本号
    const BSTFaceUnlockVersion* version = bstFaceUnlockGetVersion();
    if (version != nullptr) {
        printf("BSTFaceUnlock build date : %s\n", version->build_date);
        printf("BSTFaceUnlock version : %s\n", version->lib_version);
    }

    // 2. 初始化
    BSTFaceUnlockConfig config;
    config.config_type     = BST_FU_CONFIG_TYPE_FILE;
    config.cfg.config_path = cfg_path.c_str();
    if (BST_FU_SUCCESS != bstFaceUnlockInit(&config)) {
        exit(-1);
    }

    // END

    // 调用人脸解锁
    printf("[MAIN] auth...\n");

    string file_format = "png";
    // auth
    std::vector<std::string> auth_img_list;
    GlobFiles(auth_img_path.c_str(), auth_img_list, file_format);

    for (auto img_path : auth_img_list) {

        cout << "[MAIN] " << " auth input : " << img_path << endl;
        // 读取JPEG图像
        cv::Mat bgrImage = cv::imread(img_path, cv::IMREAD_COLOR);
        width = bgrImage.cols;
        height = bgrImage.rows;
        // 检查图像是否成功加载
        if (bgrImage.empty()) {
            std::cerr << "Could not open or find the image" << std::endl;
            return -1;
        }

        // 创建NV21格式的图像
        cv::Mat nv21Image;

        // 转换图像
        convertToNV21(bgrImage, nv21Image,width,height);

        // 保存NV21图像（可选）
//         cv::imwrite("output_nv21.yuv", nv21Image);


        // 显示图像（可选）
//         cv::namedWindow("NV21 Image");
//         cv::imshow("NV21 Image", nv21Image);
//         cv::waitKey(30);
         // 将nv21Image转换为unsigned char*数组
        int buflen = int(1.5 * width * height);
        unsigned char* nv21Data = new unsigned char[buflen];

        memcpy(nv21Data, nv21Image.data, buflen*sizeof(unsigned char));


         // ...（此处可以使用nv21Data进行进一步处理）


        // 构造输入
        BSTFaceFeature     auth_feature;
        BSTFaceUnlockImage auth_image;
        auth_image.img_data.data     = (void*)nv21Data;
        auth_image.img_data.format   = BST_IMAGE_TYPE_NV21;
        auth_image.img_data.width    = width;
        auth_image.img_data.height   = height;
        auth_image.img_data.rotation = 0;
        auth_image.img_data.stride   = width;
//        auth_image.meta_data.iso = 0;
//        auth_image.meta_data.exposure = 0;
        int ret = bstFaceUnlockAuthenticate(&auth_image, &auth_feature);

        // 释放分配的内存
        freeUCharPtr(nv21Data);
        // 返回值按照十六进制第四位分类
        // 0x0000 表示成功（Success）          0xE000 表示出现了重要错误（Fatal Error）
        // 0xF000 表示注册/解锁失败（Fail）     0xC000 表示需要继续进帧（Continue）
        int ret_class = ret;
        printf("Auth ret class : %x\n", ret_class);
        if (0xE000 == ret_class) {
            // 输入参数错误/内部逻辑错误
            printf("Fatal Error : %x\n", ret);
            exit(-1);
        } else if (0xF000 == ret_class) {
            // 解锁超时/检测到攻击
            printf("Auth Fail : %x\n", ret);
            break;
        } else if (BST_FU_SUCCESS == ret_class) {
            // 逻辑上解锁不会返回该值
            printf("Unexpected Ret Value : %x\n", ret);
            break;
        } else if (0xC001 != ret_class) {
            std::string image_path;
            std::string relateImagePath = replace_all_distinct (img_path,auth_img_path,"");///imagePath - image_path;
            std::string savePatchImage = dst_path + relateImagePath;
            std::string srcPatchImage = auth_img_path + relateImagePath;


            create_parent_directory(savePatchImage.c_str());
            {
                            move_file(srcPatchImage,savePatchImage);
            }

        }
    }
    if (BST_FU_SUCCESS != bstFaceUnlockAuthenticateReset()) {
        exit(-1);
    }

    // 反初始化
    if (BST_FU_SUCCESS != bstFaceUnlockUninit()) {
        exit(-1);
    }

    return 0;
}

int main(int argc, char const* argv[]) {
    if (argc < 4) {
        cerr << "[MAIN] "
             << "Usage : ./demo <auth_folder> <cfg_path> <width> <height>" << endl;
        return -1;
    }



    return run(string(argv[3]), string(argv[2]), string(argv[1]));
}