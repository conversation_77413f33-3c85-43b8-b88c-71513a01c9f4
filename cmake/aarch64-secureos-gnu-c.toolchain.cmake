set(CMAKE_SYSTEM_NAME Linux-GNU)
set(CMAKE_SYSTEM_PROCESSOR aarch64)
set(CMAKE_TRY_COMPILE_TARGET_TYPE "STATIC_LIBRARY")

# aarch64-secureos-gnueabi-c++
set(CMAKE_C_COMPILER "aarch64-secureos-gnueabi-gcc")
set(CMAKE_CXX_COMPILER "aarch64-secureos-gnueabi-g++")
set(CMAKE_FORCE_C_COMPILER "aarch64-secureos-gnueabi-gcc" GNU)
set(CMAKE_FORCE_CXX_COMPILER "aarch64-secureos-gnueabi-g++" GNU)

set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

set(CMAKE_C_FLAGS "-march=armv8-a")
set(CMAKE_CXX_FLAGS "-march=armv8-a")

# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -nodefaultlibs -fno-builtin -fno-stack-protector -nostdinc++")

# cache flags
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}" CACHE STRING "c flags")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}" CACHE STRING "c++ flags")
